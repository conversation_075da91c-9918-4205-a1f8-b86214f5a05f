// Simple test script to verify creator API endpoints
const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

async function testCreatorAPI() {
  console.log('🧪 Testing Creator API Endpoints...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${API_BASE_URL.replace('/api', '')}/health`);
    console.log('✅ Health check:', healthResponse.data);

    // Test 2: API info
    console.log('\n2. Testing API info endpoint...');
    const infoResponse = await axios.get(`${API_BASE_URL.replace('/api', '')}`);
    console.log('✅ API info:', infoResponse.data);

    // Test 3: Creator registration
    console.log('\n3. Testing creator registration...');
    const testEmail = `test-creator-${Date.now()}@example.com`;
    const registrationData = {
      email: testEmail,
      password: 'testpassword123',
      display_name: 'Test Creator'
    };

    try {
      const registerResponse = await axios.post(`${API_BASE_URL}/creators/auth/register`, registrationData);
      console.log('✅ Creator registration:', registerResponse.data);
      
      // Test 4: Resend OTP
      console.log('\n4. Testing resend OTP...');
      const resendResponse = await axios.post(`${API_BASE_URL}/creators/auth/resend-otp?email=${encodeURIComponent(testEmail)}`);
      console.log('✅ Resend OTP:', resendResponse.data);
      
    } catch (error) {
      if (error.response?.status === 409) {
        console.log('⚠️  User already exists (expected for repeated tests)');
      } else {
        console.error('❌ Registration error:', error.response?.data || error.message);
      }
    }

    // Test 5: Invalid login attempt
    console.log('\n5. Testing invalid login...');
    try {
      await axios.post(`${API_BASE_URL}/creators/auth/login`, {
        email: '<EMAIL>',
        password: 'wrongpassword'
      });
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Invalid login properly rejected:', error.response.data.message);
      } else {
        console.error('❌ Unexpected login error:', error.response?.data || error.message);
      }
    }

    // Test 6: Forgot password
    console.log('\n6. Testing forgot password...');
    try {
      const forgotResponse = await axios.post(`${API_BASE_URL}/creators/auth/forgot-password`, {
        email: '<EMAIL>'
      });
      console.log('✅ Forgot password:', forgotResponse.data);
    } catch (error) {
      console.error('❌ Forgot password error:', error.response?.data || error.message);
    }

    console.log('\n🎉 Creator API tests completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Start the backend server: npm run start:dev');
    console.log('2. Test with mobile app or Postman');
    console.log('3. Check email service configuration for OTP delivery');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n💡 Make sure the backend server is running on http://localhost:3000');
  }
}

// Run the test
testCreatorAPI();
