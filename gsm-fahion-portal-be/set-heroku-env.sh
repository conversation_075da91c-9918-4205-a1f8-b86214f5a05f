#!/bin/bash

# Script to set all environment variables for Heroku deployment
# Make sure you have Heroku CLI installed and are logged in

APP_NAME="gsm-fashion-portal"

echo "Setting environment variables for Heroku app: $APP_NAME"

# Database
heroku config:set MONGODB_URI="mongodb+srv://zohaib:<EMAIL>/gsm-portal" --app $APP_NAME

# JWT Configuration
heroku config:set JWT_SECRET="your-super-secret-jwt-key-change-in-production" --app $APP_NAME
heroku config:set JWT_EXPIRES_IN="7d" --app $APP_NAME

# SMTP Configuration
heroku config:set SMTP_HOST="smtp.gmail.com" --app $APP_NAME
heroku config:set SMTP_PORT="465" --app $APP_NAME
heroku config:set SMTP_SECURE="true" --app $APP_NAME
heroku config:set SMTP_USER="<EMAIL>" --app $APP_NAME
heroku config:set SMTP_PASS="marv stlx nayp drwl" --app $APP_NAME

# Cloudinary Configuration
heroku config:set CLOUDINARY_CLOUD_NAME="dzuxnbt8u" --app $APP_NAME
heroku config:set CLOUDINARY_API_KEY="229778449983798" --app $APP_NAME
heroku config:set CLOUDINARY_API_SECRET="J3kXcIIz_hNTYgzkzRfGGau2loM" --app $APP_NAME

# Application Configuration
heroku config:set NODE_ENV="production" --app $APP_NAME

# Admin Credentials
heroku config:set ADMIN_EMAIL="<EMAIL>" --app $APP_NAME
heroku config:set ADMIN_PASSWORD="demo123" --app $APP_NAME

# Shopify Configuration
heroku config:set SHOPIFY_CLIENT_ID="9813548cb9d5599d4c7f31740753959f" --app $APP_NAME
heroku config:set SHOPIFY_CLIENT_SECRET="eb79349f2a390b49a91fa033519d1b28" --app $APP_NAME
heroku config:set SHOPIFY_API_VERSION="2024-07" --app $APP_NAME
heroku config:set SHOPIFY_WEBHOOK_SECRET="gsm_fashion_webhook_secret_2024" --app $APP_NAME
heroku config:set APP_URL="https://gsm-fashion-portal-f54077544850.herokuapp.com" --app $APP_NAME
heroku config:set FRONTEND_URL="https://gsm-fahion-portal.vercel.app" --app $APP_NAME

echo "Environment variables have been set successfully!"
echo "You can verify them by running: heroku config --app $APP_NAME"
