import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { v2 as cloudinary } from 'cloudinary';

@Injectable()
export class UploadService {
  private readonly logger = new Logger(UploadService.name);

  constructor(private configService: ConfigService) {
    cloudinary.config({
      cloud_name: this.configService.get('CLOUDINARY_CLOUD_NAME'),
      api_key: this.configService.get('CLOUDINARY_API_KEY'),
      api_secret: this.configService.get('CLOUDINARY_API_SECRET'),
      timeout: 60000, // 60 seconds timeout
    });
  }

  async uploadFile(file: Express.Multer.File, folder: string = 'uploads'): Promise<string> {
    const maxRetries = 3;
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logger.log(`Upload attempt ${attempt}/${maxRetries} for file: ${file.originalname}`);

        const result = await cloudinary.uploader.upload(
          `data:${file.mimetype};base64,${file.buffer.toString('base64')}`,
          {
            folder,
            resource_type: 'auto',
            quality: 'auto:low', // Reduce quality to speed up upload
            fetch_format: 'auto',
            timeout: 120000, // 2 minutes per upload
            transformation: [
              { width: 1000, height: 1000, crop: 'limit' }, // Limit size to reduce upload time
              { quality: 'auto:low' }
            ]
          },
        );

        this.logger.log(`File uploaded successfully: ${result.secure_url}`);
        return result.secure_url;
      } catch (error) {
        lastError = error;
        this.logger.warn(`Upload attempt ${attempt} failed:`, error.message);

        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
          this.logger.log(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    this.logger.error('Failed to upload file to Cloudinary after all retries:', lastError);
    throw new Error(`Failed to upload file after ${maxRetries} attempts: ${lastError.message}`);
  }

  async uploadLogo(file: Express.Multer.File): Promise<string> {
    return this.uploadFile(file, 'brand-logos');
  }

  async uploadDocument(file: Express.Multer.File): Promise<string> {
    return this.uploadFile(file, 'business-documents');
  }

  async uploadCampaignBanner(file: Express.Multer.File): Promise<string> {
    return this.uploadFile(file, 'campaign-banners');
  }

  async deleteFile(publicId: string): Promise<void> {
    try {
      await cloudinary.uploader.destroy(publicId);
      this.logger.log(`File deleted successfully: ${publicId}`);
    } catch (error) {
      this.logger.error(`Failed to delete file: ${publicId}`, error);
      throw new Error('Failed to delete file');
    }
  }
}
