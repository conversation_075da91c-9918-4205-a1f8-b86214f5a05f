import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { CreatorsAuthService } from './creators-auth.service';
import { CreatorRegisterDto } from './dto/creator-register.dto';
import { CreatorLoginDto } from './dto/creator-login.dto';
import { CreatorVerifyOtpDto } from './dto/creator-verify-otp.dto';
import { CreatorForgotPasswordDto, CreatorResetPasswordDto } from './dto/creator-forgot-password.dto';

@Controller('creators/auth')
export class CreatorsAuthController {
  constructor(private readonly creatorsAuthService: CreatorsAuthService) {}

  @Post('register')
  async register(@Body() registerDto: CreatorRegisterDto) {
    return this.creatorsAuthService.register(registerDto);
  }

  @Post('verify-otp')
  @HttpCode(HttpStatus.OK)
  async verifyOtp(@Body() verifyOtpDto: CreatorVerifyOtpDto) {
    return this.creatorsAuthService.verifyOtp(verifyOtpDto);
  }

  @Post('login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: CreatorLoginDto) {
    return this.creatorsAuthService.login(loginDto);
  }

  @Post('forgot-password')
  @HttpCode(HttpStatus.OK)
  async forgotPassword(@Body() forgotPasswordDto: CreatorForgotPasswordDto) {
    return this.creatorsAuthService.forgotPassword(forgotPasswordDto);
  }

  @Post('reset-password')
  @HttpCode(HttpStatus.OK)
  async resetPassword(@Body() resetPasswordDto: CreatorResetPasswordDto) {
    return this.creatorsAuthService.resetPassword(resetPasswordDto);
  }

  @Post('resend-otp')
  @HttpCode(HttpStatus.OK)
  async resendOtp(@Query('email') email: string) {
    return this.creatorsAuthService.resendOtp(email);
  }
}
