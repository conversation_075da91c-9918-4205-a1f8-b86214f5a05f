import {
  Controller,
  Get,
  Put,
  Post,
  Body,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../schemas/user.schema';
import { CreatorsProfileService } from './creators-profile.service';
import { UpdateCreatorProfileDto } from './dto/update-creator-profile.dto';
import { UploadService } from '../upload/upload.service';

@Controller('creators/profile')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.CREATOR)
export class CreatorsProfileController {
  constructor(
    private readonly creatorsProfileService: CreatorsProfileService,
    private readonly uploadService: UploadService,
  ) {}

  @Get()
  async getProfile(@Request() req) {
    return this.creatorsProfileService.getProfile(req.user._id);
  }

  @Put()
  async updateProfile(@Request() req, @Body() updateProfileDto: UpdateCreatorProfileDto) {
    return this.creatorsProfileService.updateProfile(req.user._id, updateProfileDto);
  }

  @Get('stats')
  async getStats(@Request() req) {
    return this.creatorsProfileService.getStats(req.user._id);
  }

  @Post('avatar')
  @UseInterceptors(FileInterceptor('avatar'))
  async uploadAvatar(@Request() req, @UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new Error('No file uploaded');
    }

    // Upload to Cloudinary
    const uploadResult = await this.uploadService.uploadFile(file, 'creator-avatars');

    // Update profile with new avatar URL
    const updateResult = await this.creatorsProfileService.updateProfile(req.user._id, {
      avatar_url: uploadResult.secure_url,
    });

    return {
      message: 'Avatar uploaded successfully',
      avatar_url: uploadResult.secure_url,
      profile: updateResult.profile,
    };
  }
}
