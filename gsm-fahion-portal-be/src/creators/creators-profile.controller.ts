import {
  Controller,
  Get,
  Put,
  Body,
  UseGuards,
  Request,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../schemas/user.schema';
import { CreatorsProfileService } from './creators-profile.service';
import { UpdateCreatorProfileDto } from './dto/update-creator-profile.dto';

@Controller('creators/profile')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.CREATOR)
export class CreatorsProfileController {
  constructor(private readonly creatorsProfileService: CreatorsProfileService) {}

  @Get()
  async getProfile(@Request() req) {
    return this.creatorsProfileService.getProfile(req.user._id);
  }

  @Put()
  async updateProfile(@Request() req, @Body() updateProfileDto: UpdateCreatorProfileDto) {
    return this.creatorsProfileService.updateProfile(req.user._id, updateProfileDto);
  }

  @Get('stats')
  async getStats(@Request() req) {
    return this.creatorsProfileService.getStats(req.user._id);
  }
}
