import { IsOptional, IsString, Is<PERSON><PERSON>y, IsDateString, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

class SocialHandlesDto {
  @IsOptional()
  @IsString()
  instagram?: string;

  @IsOptional()
  @IsString()
  tiktok?: string;

  @IsOptional()
  @IsString()
  twitter?: string;

  @IsOptional()
  @IsString()
  youtube?: string;

  @IsOptional()
  @IsString()
  facebook?: string;
}

class NotificationPreferencesDto {
  @IsOptional()
  email?: boolean;

  @IsOptional()
  push?: boolean;

  @IsOptional()
  campaign_invites?: boolean;

  @IsOptional()
  reward_updates?: boolean;
}

class PrivacyPreferencesDto {
  @IsOptional()
  @IsString()
  profile_visibility?: 'public' | 'private';

  @IsOptional()
  show_stats?: boolean;
}

class PreferencesDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => NotificationPreferencesDto)
  notifications?: NotificationPreferencesDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => PrivacyPreferencesDto)
  privacy?: PrivacyPreferencesDto;
}

export class UpdateCreatorProfileDto {
  @IsOptional()
  @IsString()
  display_name?: string;

  @IsOptional()
  @IsString()
  bio?: string;

  @IsOptional()
  @IsString()
  avatar_url?: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsDateString()
  date_of_birth?: string;

  @IsOptional()
  @IsString()
  phone_number?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => SocialHandlesDto)
  social_handles?: SocialHandlesDto;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  interests?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  preferred_brands?: string[];

  @IsOptional()
  @ValidateNested()
  @Type(() => PreferencesDto)
  preferences?: PreferencesDto;
}
