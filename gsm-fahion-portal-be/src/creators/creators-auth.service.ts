import {
  Injectable,
  ConflictException,
  UnauthorizedException,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';

import { User, UserDocument, UserRole, UserStatus } from '../schemas/user.schema';
import { CreatorProfile, CreatorProfileDocument } from '../schemas/creator-profile.schema';
import { EmailService } from '../email/email.service';
import { OtpUtil } from '../common/utils/otp.util';
import { CreatorRegisterDto } from './dto/creator-register.dto';
import { CreatorLoginDto } from './dto/creator-login.dto';
import { CreatorVerifyOtpDto } from './dto/creator-verify-otp.dto';
import { CreatorForgotPasswordDto, CreatorResetPasswordDto } from './dto/creator-forgot-password.dto';

@Injectable()
export class CreatorsAuthService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(CreatorProfile.name) private creatorProfileModel: Model<CreatorProfileDocument>,
    private jwtService: JwtService,
    private emailService: EmailService,
    private configService: ConfigService,
  ) {}

  async register(registerDto: CreatorRegisterDto) {
    const { email, password, display_name } = registerDto;

    // Check if user already exists
    const existingUser = await this.userModel.findOne({ email }).exec();
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Generate OTP
    const otp = OtpUtil.generateOtp();
    const otpExpires = OtpUtil.generateOtpExpiry();

    // Create user with CREATOR role
    const user = new this.userModel({
      email,
      password: hashedPassword,
      role: UserRole.CREATOR,
      status: UserStatus.APPROVED, // Creators are auto-approved
      email_verified: false,
      otp,
      otp_expires: otpExpires,
    });

    await user.save();

    // Create creator profile
    const creatorProfile = new this.creatorProfileModel({
      user_id: user._id,
      display_name: display_name || email.split('@')[0],
      profile_completed: false,
      is_active: true,
      last_active_at: new Date(),
    });
    await creatorProfile.save();

    // Send OTP email
    await this.emailService.sendOtpEmail(email, otp);

    return {
      message: 'Registration successful. Please check your email for verification code.',
      email,
    };
  }

  async verifyOtp(verifyOtpDto: CreatorVerifyOtpDto) {
    const { email, otp } = verifyOtpDto;

    const user = await this.userModel.findOne({ 
      email, 
      role: UserRole.CREATOR 
    }).exec();
    
    if (!user) {
      throw new NotFoundException('Creator not found');
    }

    if (user.email_verified) {
      throw new BadRequestException('Email already verified');
    }

    if (!user.otp || user.otp !== otp) {
      throw new BadRequestException('Invalid OTP');
    }

    if (OtpUtil.isOtpExpired(user.otp_expires)) {
      throw new BadRequestException('OTP has expired');
    }

    // Mark email as verified
    user.email_verified = true;
    user.otp = undefined;
    user.otp_expires = undefined;
    await user.save();

    // Update creator profile last active
    await this.creatorProfileModel.updateOne(
      { user_id: user._id },
      { last_active_at: new Date() }
    );

    // Generate JWT token
    const payload = { email: user.email, sub: user._id, role: user.role };
    const token = this.jwtService.sign(payload);

    return {
      message: 'Email verified successfully',
      access_token: token,
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        status: user.status,
        email_verified: user.email_verified,
      },
    };
  }

  async login(loginDto: CreatorLoginDto) {
    const { email, password } = loginDto;

    const user = await this.userModel.findOne({ 
      email, 
      role: UserRole.CREATOR 
    }).exec();
    
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (!user.email_verified) {
      throw new UnauthorizedException('Please verify your email first');
    }

    // Update creator profile last active
    await this.creatorProfileModel.updateOne(
      { user_id: user._id },
      { last_active_at: new Date() }
    );

    // Generate JWT token
    const payload = { email: user.email, sub: user._id, role: user.role };
    const token = this.jwtService.sign(payload);

    return {
      access_token: token,
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        status: user.status,
        email_verified: user.email_verified,
      },
    };
  }

  async forgotPassword(forgotPasswordDto: CreatorForgotPasswordDto) {
    const { email } = forgotPasswordDto;

    const user = await this.userModel.findOne({ 
      email, 
      role: UserRole.CREATOR 
    }).exec();
    
    if (!user) {
      // Don't reveal if user exists or not
      return {
        message: 'If an account with this email exists, you will receive a password reset code.',
      };
    }

    // Generate reset token
    const resetToken = OtpUtil.generateOtp();
    const resetTokenExpires = OtpUtil.generateOtpExpiry();

    user.reset_token = resetToken;
    user.reset_token_expires = resetTokenExpires;
    await user.save();

    // Send reset token email
    await this.emailService.sendPasswordResetEmail(email, resetToken);

    return {
      message: 'If an account with this email exists, you will receive a password reset code.',
    };
  }

  async resetPassword(resetPasswordDto: CreatorResetPasswordDto) {
    const { email, reset_token, new_password } = resetPasswordDto;

    const user = await this.userModel.findOne({ 
      email, 
      role: UserRole.CREATOR 
    }).exec();
    
    if (!user) {
      throw new BadRequestException('Invalid reset token');
    }

    if (!user.reset_token || user.reset_token !== reset_token) {
      throw new BadRequestException('Invalid reset token');
    }

    if (OtpUtil.isOtpExpired(user.reset_token_expires)) {
      throw new BadRequestException('Reset token has expired');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(new_password, 12);

    // Update password and clear reset token
    user.password = hashedPassword;
    user.reset_token = undefined;
    user.reset_token_expires = undefined;
    await user.save();

    return {
      message: 'Password reset successfully',
    };
  }

  async resendOtp(email: string) {
    const user = await this.userModel.findOne({ 
      email, 
      role: UserRole.CREATOR 
    }).exec();
    
    if (!user) {
      throw new NotFoundException('Creator not found');
    }

    if (user.email_verified) {
      throw new BadRequestException('Email already verified');
    }

    // Generate new OTP
    const otp = OtpUtil.generateOtp();
    const otpExpires = OtpUtil.generateOtpExpiry();

    user.otp = otp;
    user.otp_expires = otpExpires;
    await user.save();

    // Send OTP email
    await this.emailService.sendOtpEmail(email, otp);

    return {
      message: 'OTP sent successfully',
    };
  }
}
