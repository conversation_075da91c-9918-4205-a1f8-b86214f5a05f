import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';

import { CreatorsAuthService } from './creators-auth.service';
import { CreatorsProfileService } from './creators-profile.service';
import { CreatorsAuthController } from './creators-auth.controller';
import { CreatorsProfileController } from './creators-profile.controller';
import { User, UserSchema } from '../schemas/user.schema';
import { CreatorProfile, CreatorProfileSchema } from '../schemas/creator-profile.schema';
import { EmailModule } from '../email/email.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: CreatorProfile.name, schema: CreatorProfileSchema },
    ]),
    PassportModule,
    JwtModule.registerAsync({
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN'),
        },
      }),
      inject: [ConfigService],
    }),
    EmailModule,
  ],
  controllers: [CreatorsAuthController, CreatorsProfileController],
  providers: [CreatorsAuthService, CreatorsProfileService],
  exports: [CreatorsAuthService, CreatorsProfileService],
})
export class CreatorsModule {}
