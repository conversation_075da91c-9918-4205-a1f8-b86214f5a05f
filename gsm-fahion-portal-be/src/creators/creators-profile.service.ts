import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { User, UserDocument, UserRole } from '../schemas/user.schema';
import { CreatorProfile, CreatorProfileDocument } from '../schemas/creator-profile.schema';
import { UpdateCreatorProfileDto } from './dto/update-creator-profile.dto';

@Injectable()
export class CreatorsProfileService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(CreatorProfile.name) private creatorProfileModel: Model<CreatorProfileDocument>,
  ) {}

  async getProfile(userId: string) {
    // Verify user is a creator
    const user = await this.userModel.findOne({ 
      _id: userId, 
      role: UserRole.CREATOR 
    }).exec();
    
    if (!user) {
      throw new NotFoundException('Creator not found');
    }

    const profile = await this.creatorProfileModel.findOne({ user_id: userId }).exec();
    if (!profile) {
      throw new NotFoundException('Creator profile not found');
    }

    return {
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        status: user.status,
        email_verified: user.email_verified,
      },
      profile: {
        id: profile._id,
        display_name: profile.display_name,
        bio: profile.bio,
        avatar_url: profile.avatar_url,
        location: profile.location,
        date_of_birth: profile.date_of_birth,
        phone_number: profile.phone_number,
        social_handles: profile.social_handles,
        interests: profile.interests,
        preferred_brands: profile.preferred_brands,
        total_tokens: profile.total_tokens,
        completed_campaigns: profile.completed_campaigns,
        total_submissions: profile.total_submissions,
        approved_submissions: profile.approved_submissions,
        profile_completed: profile.profile_completed,
        is_active: profile.is_active,
        last_active_at: profile.last_active_at,
        preferences: profile.preferences,
        createdAt: profile.createdAt,
        updatedAt: profile.updatedAt,
      },
    };
  }

  async updateProfile(userId: string, updateDto: UpdateCreatorProfileDto) {
    // Verify user is a creator
    const user = await this.userModel.findOne({ 
      _id: userId, 
      role: UserRole.CREATOR 
    }).exec();
    
    if (!user) {
      throw new NotFoundException('Creator not found');
    }

    const profile = await this.creatorProfileModel.findOne({ user_id: userId }).exec();
    if (!profile) {
      throw new NotFoundException('Creator profile not found');
    }

    // Update profile fields
    Object.keys(updateDto).forEach(key => {
      if (updateDto[key] !== undefined) {
        profile[key] = updateDto[key];
      }
    });

    // Check if profile is now completed
    const requiredFields = ['display_name', 'bio'];
    const isCompleted = requiredFields.every(field => profile[field] && profile[field].trim() !== '');
    profile.profile_completed = isCompleted;

    // Update last active timestamp
    profile.last_active_at = new Date();

    await profile.save();

    return {
      message: 'Profile updated successfully',
      profile: {
        id: profile._id,
        display_name: profile.display_name,
        bio: profile.bio,
        avatar_url: profile.avatar_url,
        location: profile.location,
        date_of_birth: profile.date_of_birth,
        phone_number: profile.phone_number,
        social_handles: profile.social_handles,
        interests: profile.interests,
        preferred_brands: profile.preferred_brands,
        total_tokens: profile.total_tokens,
        completed_campaigns: profile.completed_campaigns,
        total_submissions: profile.total_submissions,
        approved_submissions: profile.approved_submissions,
        profile_completed: profile.profile_completed,
        is_active: profile.is_active,
        last_active_at: profile.last_active_at,
        preferences: profile.preferences,
        updatedAt: profile.updatedAt,
      },
    };
  }

  async getStats(userId: string) {
    // Verify user is a creator
    const user = await this.userModel.findOne({ 
      _id: userId, 
      role: UserRole.CREATOR 
    }).exec();
    
    if (!user) {
      throw new NotFoundException('Creator not found');
    }

    const profile = await this.creatorProfileModel.findOne({ user_id: userId }).exec();
    if (!profile) {
      throw new NotFoundException('Creator profile not found');
    }

    // Calculate additional stats
    const approvalRate = profile.total_submissions > 0 
      ? Math.round((profile.approved_submissions / profile.total_submissions) * 100)
      : 0;

    const avgTokensPerCampaign = profile.completed_campaigns > 0
      ? Math.round(profile.total_tokens / profile.completed_campaigns)
      : 0;

    return {
      total_tokens: profile.total_tokens,
      completed_campaigns: profile.completed_campaigns,
      total_submissions: profile.total_submissions,
      approved_submissions: profile.approved_submissions,
      approval_rate: approvalRate,
      avg_tokens_per_campaign: avgTokensPerCampaign,
      profile_completion: profile.profile_completed ? 100 : 50, // Basic completion percentage
      last_active_at: profile.last_active_at,
    };
  }

  async updateTokens(userId: string, amount: number, operation: 'add' | 'subtract' = 'add') {
    const profile = await this.creatorProfileModel.findOne({ user_id: userId }).exec();
    if (!profile) {
      throw new NotFoundException('Creator profile not found');
    }

    if (operation === 'add') {
      profile.total_tokens += amount;
    } else {
      if (profile.total_tokens < amount) {
        throw new BadRequestException('Insufficient tokens');
      }
      profile.total_tokens -= amount;
    }

    await profile.save();
    return { total_tokens: profile.total_tokens };
  }

  async incrementCampaignStats(userId: string, type: 'completed' | 'submission' | 'approved_submission') {
    const profile = await this.creatorProfileModel.findOne({ user_id: userId }).exec();
    if (!profile) {
      throw new NotFoundException('Creator profile not found');
    }

    switch (type) {
      case 'completed':
        profile.completed_campaigns += 1;
        break;
      case 'submission':
        profile.total_submissions += 1;
        break;
      case 'approved_submission':
        profile.approved_submissions += 1;
        break;
    }

    profile.last_active_at = new Date();
    await profile.save();

    return {
      completed_campaigns: profile.completed_campaigns,
      total_submissions: profile.total_submissions,
      approved_submissions: profile.approved_submissions,
    };
  }
}
