import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  Logger,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CampaignsService } from './campaigns.service';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { CampaignStatus } from './schemas/campaign.schema';

@Controller('campaigns')
@UseGuards(JwtAuthGuard)
export class CampaignsController {
  private readonly logger = new Logger(CampaignsController.name);

  constructor(private readonly campaignsService: CampaignsService) {}

  // Create a new campaign
  @Post()
  async createCampaign(@Request() req, @Body() createCampaignDto: CreateCampaignDto) {
    try {
      const brandId = req.user._id || req.user.id;
      this.logger.log(`Creating campaign for brand: ${brandId}`);

      const campaign = await this.campaignsService.createCampaign(brandId, createCampaignDto);

      return {
        success: true,
        message: 'Campaign created successfully',
        campaign
      };
    } catch (error) {
      this.logger.error(`Failed to create campaign: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get campaigns for the authenticated brand
  @Get()
  async getCampaigns(
    @Request() req,
    @Query('page') page = 1,
    @Query('limit') limit = 20,
    @Query('status') status?: CampaignStatus,
    @Query('search') search?: string
  ) {
    try {
      const brandId = req.user._id || req.user.id;

      const options = {
        page: parseInt(page.toString()),
        limit: parseInt(limit.toString()),
        status,
        search
      };

      const result = await this.campaignsService.getCampaignsByBrand(brandId, options);

      return {
        success: true,
        ...result
      };
    } catch (error) {
      this.logger.error(`Failed to fetch campaigns: ${error.message}`);
      return {
        success: false,
        error: error.message,
        campaigns: [],
        pagination: { page: 1, limit: 20, total: 0, pages: 0 }
      };
    }
  }

  // Get single campaign by ID
  @Get(':id')
  async getCampaign(@Request() req, @Param('id') campaignId: string) {
    try {
      const brandId = req.user._id || req.user.id;
      const campaign = await this.campaignsService.getCampaignById(brandId, campaignId);

      return {
        success: true,
        campaign
      };
    } catch (error) {
      this.logger.error(`Failed to fetch campaign: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get campaign statistics
  @Get('stats/overview')
  async getCampaignStats(@Request() req) {
    try {
      const brandId = req.user._id || req.user.id;
      const stats = await this.campaignsService.getCampaignStats(brandId);

      return {
        success: true,
        stats
      };
    } catch (error) {
      this.logger.error(`Failed to fetch campaign stats: ${error.message}`);
      return {
        success: false,
        error: error.message,
        stats: {
          total: 0,
          pending: 0,
          approved: 0,
          active: 0,
          rejected: 0,
          completed: 0
        }
      };
    }
  }
}
