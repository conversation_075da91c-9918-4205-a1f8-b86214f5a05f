import { IsString, <PERSON>NotEmpty, IsOptional, IsArray, IsEnum, IsNumber, IsBoolean, IsDateString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { MissionType, RewardType, VerificationMethod } from '../schemas/campaign.schema';

export class CreateMissionDto {
  @IsEnum(MissionType)
  @IsNotEmpty()
  type: MissionType;

  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  instructions?: string;

  @IsOptional()
  @IsBoolean()
  required?: boolean;

  // Video mission specific
  @IsOptional()
  @IsNumber()
  min_duration?: number;

  // Social mission specific
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  platforms?: string[];

  @IsOptional()
  @IsString()
  hashtags?: string;

  @IsOptional()
  @IsString()
  mentions?: string;

  // Custom mission specific
  @IsOptional()
  @IsEnum(VerificationMethod)
  verification_method?: VerificationMethod;
}

export class CreateRewardSettingsDto {
  @IsEnum(RewardType)
  @IsNotEmpty()
  type: RewardType;

  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @IsOptional()
  @IsString()
  token?: string;

  @IsOptional()
  @IsNumber()
  daily_limit?: number;

  @IsOptional()
  @IsNumber()
  user_limit?: number;

  @IsOptional()
  @IsDateString()
  start_date?: string;

  @IsOptional()
  @IsDateString()
  end_date?: string;

  @IsOptional()
  @IsBoolean()
  approval_required?: boolean;
}

export class CreateCampaignDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsOptional()
  @IsString()
  banner_url?: string;

  @IsArray()
  @IsString({ each: true })
  selected_products: string[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateMissionDto)
  missions: CreateMissionDto[];

  @ValidateNested()
  @Type(() => CreateRewardSettingsDto)
  reward_settings: CreateRewardSettingsDto;

  @IsOptional()
  @IsNumber()
  target_submissions?: number;

  @IsOptional()
  @IsDateString()
  expires_at?: string;
}
