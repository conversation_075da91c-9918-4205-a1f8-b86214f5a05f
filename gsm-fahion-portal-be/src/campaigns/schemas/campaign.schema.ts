import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum CampaignStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed'
}

export enum RewardType {
  CRYPTO = 'crypto',
  NFT = 'nft',
  POINTS = 'points'
}

export enum MissionType {
  VIDEO = 'video',
  SOCIAL = 'social',
  CUSTOM = 'custom'
}

export enum VerificationMethod {
  SCREENSHOT = 'screenshot',
  MANUAL = 'manual',
  URL = 'url'
}

@Schema({ timestamps: true })
export class Mission extends Document {
  @Prop({ type: String, enum: MissionType, required: true })
  type: MissionType;

  @Prop()
  title?: string;

  @Prop()
  description?: string;

  @Prop()
  instructions?: string;

  @Prop({ default: false })
  required: boolean;

  // Video mission specific
  @Prop()
  min_duration?: number;

  // Social mission specific
  @Prop({ type: [String], default: [] })
  platforms?: string[];

  @Prop()
  hashtags?: string;

  @Prop()
  mentions?: string;

  // Custom mission specific
  @Prop({ type: String, enum: VerificationMethod })
  verification_method?: VerificationMethod;
}

@Schema({ timestamps: true })
export class RewardSettings extends Document {
  @Prop({ type: String, enum: RewardType, required: true })
  type: RewardType;

  @Prop({ required: true })
  amount: number;

  @Prop()
  token?: string;

  @Prop()
  daily_limit?: number;

  @Prop()
  user_limit?: number;

  @Prop()
  start_date?: Date;

  @Prop()
  end_date?: Date;

  @Prop({ default: false })
  approval_required: boolean;
}

@Schema({ timestamps: true })
export class Campaign extends Document {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  brand_id: Types.ObjectId;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop()
  banner_url?: string;

  @Prop({ type: [{ type: Types.ObjectId, ref: 'Product' }], default: [] })
  selected_products: Types.ObjectId[];

  @Prop({ type: [Mission], default: [] })
  missions: Mission[];

  @Prop({ type: RewardSettings })
  reward_settings: RewardSettings;

  @Prop({ type: String, enum: CampaignStatus, default: CampaignStatus.PENDING })
  status: CampaignStatus;

  @Prop()
  admin_notes?: string;

  @Prop()
  approved_at?: Date;

  @Prop()
  rejected_at?: Date;

  @Prop({ default: 0 })
  submissions_count: number;

  @Prop()
  target_submissions?: number;

  @Prop({ default: true })
  is_active: boolean;

  @Prop()
  published_at?: Date;

  @Prop()
  expires_at?: Date;
}

export const CampaignSchema = SchemaFactory.createForClass(Campaign);
export const MissionSchema = SchemaFactory.createForClass(Mission);
export const RewardSettingsSchema = SchemaFactory.createForClass(RewardSettings);
