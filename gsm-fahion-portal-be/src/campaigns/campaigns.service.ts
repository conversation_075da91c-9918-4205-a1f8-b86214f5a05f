import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Campaign, CampaignStatus } from './schemas/campaign.schema';
import { CreateCampaignDto } from './dto/create-campaign.dto';

@Injectable()
export class CampaignsService {
  private readonly logger = new Logger(CampaignsService.name);

  constructor(
    @InjectModel(Campaign.name) private campaignModel: Model<Campaign>,
  ) {}

  // Create a new campaign
  async createCampaign(brandId: string, createCampaignDto: CreateCampaignDto): Promise<Campaign> {
    try {
      this.logger.log(`Creating campaign for brand: ${brandId}`);

      const campaignData = {
        ...createCampaignDto,
        brand_id: new Types.ObjectId(brandId),
        selected_products: createCampaignDto.selected_products.map(id => new Types.ObjectId(id)),
        status: CampaignStatus.PENDING,
      };

      const campaign = new this.campaignModel(campaignData);
      const savedCampaign = await campaign.save();

      this.logger.log(`Campaign created successfully: ${savedCampaign._id}`);
      return savedCampaign;
    } catch (error) {
      this.logger.error(`Failed to create campaign: ${error.message}`);
      throw new BadRequestException(`Failed to create campaign: ${error.message}`);
    }
  }

  // Get campaigns for a brand
  async getCampaignsByBrand(
    brandId: string,
    options: {
      page?: number;
      limit?: number;
      status?: CampaignStatus;
      search?: string;
    } = {}
  ) {
    try {
      const { page = 1, limit = 20, status, search } = options;
      const skip = (page - 1) * limit;

      const query: any = {
        brand_id: new Types.ObjectId(brandId),
        is_active: true
      };

      if (status) {
        query.status = status;
      }

      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      const [campaigns, total] = await Promise.all([
        this.campaignModel
          .find(query)
          .populate('selected_products', 'title images')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        this.campaignModel.countDocuments(query)
      ]);

      return {
        campaigns,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      this.logger.error(`Failed to fetch campaigns: ${error.message}`);
      throw new BadRequestException(`Failed to fetch campaigns: ${error.message}`);
    }
  }

  // Get single campaign by ID
  async getCampaignById(brandId: string, campaignId: string): Promise<Campaign> {
    try {
      const campaign = await this.campaignModel
        .findOne({
          _id: new Types.ObjectId(campaignId),
          brand_id: new Types.ObjectId(brandId),
          is_active: true
        })
        .populate('selected_products')
        .exec();

      if (!campaign) {
        throw new NotFoundException('Campaign not found');
      }

      return campaign;
    } catch (error) {
      this.logger.error(`Failed to fetch campaign: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to fetch campaign: ${error.message}`);
    }
  }

  // Get campaign statistics for a brand
  async getCampaignStats(brandId: string) {
    try {
      const stats = await this.campaignModel.aggregate([
        {
          $match: {
            brand_id: new Types.ObjectId(brandId),
            is_active: true
          }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const result = {
        total: 0,
        pending: 0,
        approved: 0,
        active: 0,
        rejected: 0,
        completed: 0
      };

      stats.forEach(stat => {
        result[stat._id] = stat.count;
        result.total += stat.count;
      });

      return result;
    } catch (error) {
      this.logger.error(`Failed to fetch campaign stats: ${error.message}`);
      throw new BadRequestException(`Failed to fetch campaign stats: ${error.message}`);
    }
  }

  // Approve campaign (for admin use) - following same pattern as brand approval
  async approveCampaign(campaignId: string, notes?: string) {
    try {
      const campaign = await this.campaignModel.findById(campaignId).exec();
      if (!campaign) {
        throw new NotFoundException('Campaign not found');
      }

      if (campaign.status === CampaignStatus.APPROVED) {
        return { message: 'Campaign is already approved' };
      }

      campaign.status = CampaignStatus.APPROVED;
      campaign.admin_notes = notes;
      campaign.approved_at = new Date();
      await campaign.save();

      this.logger.log(`Campaign ${campaignId} approved successfully`);
      return {
        message: 'Campaign approved successfully',
        campaign: {
          id: campaign._id,
          name: campaign.name,
          status: campaign.status,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to approve campaign: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to approve campaign: ${error.message}`);
    }
  }

  // Reject campaign (for admin use) - following same pattern as brand rejection
  async rejectCampaign(campaignId: string, notes?: string) {
    try {
      const campaign = await this.campaignModel.findById(campaignId).exec();
      if (!campaign) {
        throw new NotFoundException('Campaign not found');
      }

      if (campaign.status === CampaignStatus.REJECTED) {
        return { message: 'Campaign is already rejected' };
      }

      campaign.status = CampaignStatus.REJECTED;
      campaign.admin_notes = notes;
      campaign.rejected_at = new Date();
      await campaign.save();

      this.logger.log(`Campaign ${campaignId} rejected successfully`);
      return {
        message: 'Campaign rejected successfully',
        campaign: {
          id: campaign._id,
          name: campaign.name,
          status: campaign.status,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to reject campaign: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to reject campaign: ${error.message}`);
    }
  }

  // Get all campaigns for admin
  async getAllCampaigns(options: {
    page?: number;
    limit?: number;
    status?: CampaignStatus;
    search?: string;
  } = {}) {
    try {
      const { page = 1, limit = 20, status, search } = options;
      const skip = (page - 1) * limit;

      const query: any = { is_active: true };

      if (status) {
        query.status = status;
      }

      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      const [campaigns, total] = await Promise.all([
        this.campaignModel
          .find(query)
          .populate('brand_id', 'email')
          .populate('selected_products', 'title images')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        this.campaignModel.countDocuments(query)
      ]);

      return {
        campaigns,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      this.logger.error(`Failed to fetch all campaigns: ${error.message}`);
      throw new BadRequestException(`Failed to fetch all campaigns: ${error.message}`);
    }
  }

  // Get campaign by ID for admin
  async getCampaignByIdForAdmin(campaignId: string): Promise<Campaign> {
    try {
      const campaign = await this.campaignModel
        .findOne({
          _id: new Types.ObjectId(campaignId),
          is_active: true
        })
        .populate('brand_id', 'email')
        .populate('selected_products')
        .exec();

      if (!campaign) {
        throw new NotFoundException('Campaign not found');
      }

      return campaign;
    } catch (error) {
      this.logger.error(`Failed to fetch campaign for admin: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to fetch campaign for admin: ${error.message}`);
    }
  }
}
