import {
  Injectable,
  ConflictException,
  UnauthorizedException,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';

import { User, UserDocument, UserRole, UserStatus } from '../schemas/user.schema';
import { BrandProfile, BrandProfileDocument } from '../schemas/brand-profile.schema';
import { EmailService } from '../email/email.service';
import { OtpUtil } from '../common/utils/otp.util';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(BrandProfile.name) private brandProfileModel: Model<BrandProfileDocument>,
    private jwtService: JwtService,
    private emailService: EmailService,
    private configService: ConfigService,
  ) {}

  async register(registerDto: RegisterDto) {
    const { email, password } = registerDto;

    // Check if user already exists
    const existingUser = await this.userModel.findOne({ email }).exec();
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Generate OTP
    const otp = OtpUtil.generateOtp();
    const otpExpires = OtpUtil.generateOtpExpiry();

    // Create user
    const user = new this.userModel({
      email,
      password: hashedPassword,
      role: UserRole.BRAND,
      status: UserStatus.PENDING,
      email_verified: false,
      otp,
      otp_expires: otpExpires,
    });

    await user.save();

    // Create empty brand profile
    const brandProfile = new this.brandProfileModel({
      user_id: user._id,
      profile_completed: false,
    });
    await brandProfile.save();

    // Send OTP email
    await this.emailService.sendOtpEmail(email, otp);

    return {
      message: 'Registration successful. Please check your email for verification code.',
      email,
    };
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto) {
    const { email, otp } = verifyOtpDto;

    const user = await this.userModel.findOne({ email }).exec();
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.email_verified) {
      throw new BadRequestException('Email already verified');
    }

    if (!user.otp || user.otp !== otp) {
      throw new BadRequestException('Invalid OTP');
    }

    if (OtpUtil.isOtpExpired(user.otp_expires)) {
      throw new BadRequestException('OTP has expired');
    }

    // Mark email as verified
    user.email_verified = true;
    user.otp = undefined;
    user.otp_expires = undefined;
    await user.save();

    // Generate JWT token
    const payload = { email: user.email, sub: user._id, role: user.role };
    const token = this.jwtService.sign(payload);

    return {
      message: 'Email verified successfully',
      access_token: token,
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        status: user.status,
        email_verified: user.email_verified,
      },
    };
  }

  async login(loginDto: LoginDto) {
    const { email, password } = loginDto;

    const user = await this.userModel.findOne({ email }).exec();
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (!user.email_verified) {
      throw new UnauthorizedException('Please verify your email first');
    }

    // Generate JWT token
    const payload = { email: user.email, sub: user._id, role: user.role };
    const token = this.jwtService.sign(payload);

    return {
      access_token: token,
      user: {
        id: user._id,
        email: user.email,
        role: user.role,
        status: user.status,
        email_verified: user.email_verified,
      },
    };
  }

  async adminLogin(loginDto: LoginDto) {
    const { email, password } = loginDto;

    // Check against static admin credentials
    const adminEmail = this.configService.get('ADMIN_EMAIL');
    const adminPassword = this.configService.get('ADMIN_PASSWORD');

    if (email !== adminEmail || password !== adminPassword) {
      throw new UnauthorizedException('Invalid admin credentials');
    }

    // Generate JWT token for admin
    const payload = { email, sub: 'admin', role: UserRole.ADMIN };
    const token = this.jwtService.sign(payload);

    return {
      access_token: token,
      user: {
        id: 'admin',
        email,
        role: UserRole.ADMIN,
        status: UserStatus.APPROVED,
        email_verified: true,
      },
    };
  }
}
