import {
  <PERSON>,
  Post,
  Body,
  Headers,
  Logger,
  BadRequestException,
  RawBodyRequest,
  Req
} from '@nestjs/common';
import { Request } from 'express';
import * as crypto from 'crypto';

@Controller('webhooks/shopify/privacy')
export class ShopifyPrivacyController {
  private readonly logger = new Logger(ShopifyPrivacyController.name);

  // Verify webhook authenticity
  private verifyWebhook(body: string, signature: string): boolean {
    try {
      if (!signature) {
        this.logger.warn('No HMAC signature provided');
        return false;
      }

      const hmac = crypto.createHmac('sha256', process.env.SHOPIFY_WEBHOOK_SECRET);
      hmac.update(body, 'utf8');
      const hash = hmac.digest('base64');

      return crypto.timingSafeEqual(
        Buffer.from(signature, 'base64'),
        Buffer.from(hash, 'base64')
      );
    } catch (error) {
      this.logger.error(`Webhook verification failed: ${error.message}`);
      return false;
    }
  }

  // Customer data request webhook
  @Post('customers/data_request')
  async customerDataRequest(
    @Body() body: any,
    @Headers('x-shopify-hmac-sha256') hmac: string,
    @Req() req: RawBodyRequest<Request>
  ) {
    try {
      this.logger.log('Customer data request received');

      // Verify webhook authenticity
      const rawBody = req.rawBody?.toString() || JSON.stringify(body);
      if (!this.verifyWebhook(rawBody, hmac)) {
        this.logger.warn('Invalid webhook signature for customer data request');
        throw new BadRequestException('Invalid signature');
      }

      // Log the request for compliance
      this.logger.log(`Customer data request for shop: ${body.shop_domain}, customer: ${body.customer?.id}`);

      // Since we don't store customer data, return success
      // In a real app, you would collect and return customer data here

      return { status: 'success', message: 'Customer data request processed' };
    } catch (error) {
      this.logger.error(`Error processing customer data request: ${error.message}`);
      throw error;
    }
  }

  // Customer data erasure webhook
  @Post('customers/redact')
  async customerDataErasure(
    @Body() body: any,
    @Headers('x-shopify-hmac-sha256') hmac: string,
    @Req() req: RawBodyRequest<Request>
  ) {
    try {
      this.logger.log('Customer data erasure request received');

      // Verify webhook authenticity
      const rawBody = req.rawBody?.toString() || JSON.stringify(body);
      if (!this.verifyWebhook(rawBody, hmac)) {
        this.logger.warn('Invalid webhook signature for customer data erasure');
        throw new BadRequestException('Invalid signature');
      }

      // Log the request for compliance
      this.logger.log(`Customer data erasure for shop: ${body.shop_domain}, customer: ${body.customer?.id}`);

      // Since we don't store customer data, return success
      // In a real app, you would delete customer data here

      return { status: 'success', message: 'Customer data erasure processed' };
    } catch (error) {
      this.logger.error(`Error processing customer data erasure: ${error.message}`);
      throw error;
    }
  }

  // Shop data erasure webhook
  @Post('shop/redact')
  async shopDataErasure(
    @Body() body: any,
    @Headers('x-shopify-hmac-sha256') hmac: string,
    @Req() req: RawBodyRequest<Request>
  ) {
    try {
      this.logger.log('Shop data erasure request received');

      // Verify webhook authenticity
      const rawBody = req.rawBody?.toString() || JSON.stringify(body);
      if (!this.verifyWebhook(rawBody, hmac)) {
        this.logger.warn('Invalid webhook signature for shop data erasure');
        throw new BadRequestException('Invalid signature');
      }

      const shopDomain = body.shop_domain;
      this.logger.log(`Shop data erasure for shop: ${shopDomain}`);

      if (shopDomain) {
        // TODO: Implement shop data deletion logic
        // Delete all data related to this shop:
        // - Shopify connections
        // - Products imported from this shop
        // - Any other shop-related data
        this.logger.log(`Processing data deletion for shop: ${shopDomain}`);
      }

      return { status: 'success', message: 'Shop data erasure processed' };
    } catch (error) {
      this.logger.error(`Error processing shop data erasure: ${error.message}`);
      throw error;
    }
  }
}
