import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class ShopifyConnection extends Document {
  @Prop({ type: Types.ObjectId, ref: 'User', required: false })
  brand_id?: Types.ObjectId;

  @Prop({ required: true })
  shop_domain: string;

  @Prop({ required: true })
  access_token: string;

  @Prop({ type: [String], default: ['read_products', 'read_inventory'] })
  scopes: string[];

  @Prop({ default: true })
  is_active: boolean;

  @Prop()
  shop_name: string;

  @Prop()
  shop_email: string;

  @Prop()
  shop_id: string;

  @Prop()
  last_sync_at: Date;

  @Prop({ default: 0 })
  total_products: number;

  @Prop({ default: 0 })
  synced_products: number;

  @Prop()
  createdAt: Date;

  @Prop()
  updatedAt: Date;
}

export const ShopifyConnectionSchema = SchemaFactory.createForClass(ShopifyConnection);

// Create indexes for better performance
ShopifyConnectionSchema.index({ brand_id: 1 });
ShopifyConnectionSchema.index({ is_active: 1 });

// Compound unique index: only one active connection per shop domain
// This allows multiple inactive connections for the same shop domain
ShopifyConnectionSchema.index(
  { shop_domain: 1, is_active: 1 },
  {
    unique: true,
    partialFilterExpression: { is_active: true },
    name: 'unique_active_shop_domain'
  }
);
