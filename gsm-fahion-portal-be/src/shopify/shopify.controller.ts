import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Query,
  Req,
  Res,
  UseG<PERSON>s,
  <PERSON>gger,
  BadRequestException,
  Param
} from '@nestjs/common';
import { ShopifyService } from './shopify.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Request, Response } from 'express';
import { Types } from 'mongoose';

@Controller('shopify')
export class ShopifyController {
  private readonly logger = new Logger(ShopifyController.name);

  constructor(private readonly shopifyService: ShopifyService) {}

  // Root Shopify app access - handles direct app URL access
  @Get()
  async handleAppAccess(
    @Query('shop') shop: string,
    @Query('embedded') embedded: string,
    @Res() res: Response
  ) {
    try {
      if (!shop) {
        // If no shop parameter, this might be a direct access - redirect to installation
        res.redirect('/api/shopify/install');
        return;
      }

      // Check if this is an embedded request
      if (embedded === '1' || embedded === 'true') {
        // Serve embedded app directly
        res.redirect(`/api/shopify/app?shop=${encodeURIComponent(shop)}&embedded=1`);
        return;
      }

      // For non-embedded requests, initiate OAuth flow for immediate authentication
      this.logger.log(`Direct app access for shop: ${shop}, initiating OAuth`);

      const oauthUrl = this.shopifyService.generateOAuthUrl(shop, null);
      res.redirect(oauthUrl);
    } catch (error) {
      this.logger.error(`App access error: ${error.message}`);
      res.status(400).send(`App access error: ${error.message}`);
    }
  }

  // Initiate Shopify connection
  @Post('connect')
  @UseGuards(JwtAuthGuard)
  async connectStore(@Body('shopDomain') shopDomain: string, @Req() req) {
    try {
      const brandId = req.user._id || req.user.id;
      
      if (!shopDomain) {
        throw new BadRequestException('Shop domain is required');
      }
      
      // Clean and validate shop domain format
      let cleanDomain = shopDomain.trim().toLowerCase();
      
      // Remove protocol if present
      cleanDomain = cleanDomain.replace(/^https?:\/\//, '');
      
      // Add .myshopify.com if not present
      if (!cleanDomain.includes('.myshopify.com')) {
        cleanDomain = `${cleanDomain}.myshopify.com`;
      }
      
      // Remove trailing slash
      cleanDomain = cleanDomain.replace(/\/$/, '');
      
      this.logger.log(`Brand ${brandId} initiating connection to ${cleanDomain}`);
      
      const oauthUrl = this.shopifyService.generateOAuthUrl(cleanDomain, brandId);
      
      return { 
        success: true,
        oauthUrl,
        shopDomain: cleanDomain
      };
    } catch (error) {
      this.logger.error(`Failed to initiate connection: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Handle Shopify app installation (GET request from Shopify)
  @Get('install')
  async handleInstallation(
    @Query('shop') shop: string,
    @Res() res: Response
  ) {
    try {
      if (!shop) {
        throw new BadRequestException('Shop domain is required');
      }

      // Clean and validate shop domain format
      let cleanDomain = shop.trim().toLowerCase();

      // Remove protocol if present
      cleanDomain = cleanDomain.replace(/^https?:\/\//, '');

      // Add .myshopify.com if not present
      if (!cleanDomain.includes('.myshopify.com')) {
        cleanDomain = `${cleanDomain}.myshopify.com`;
      }

      // Remove trailing slash
      cleanDomain = cleanDomain.replace(/\/$/, '');

      this.logger.log(`Handling app installation for shop: ${cleanDomain}`);

      // Generate OAuth URL without brand association for immediate authentication
      const oauthUrl = this.shopifyService.generateOAuthUrl(cleanDomain, null);

      // Redirect immediately to OAuth for authentication
      res.redirect(oauthUrl);
    } catch (error) {
      this.logger.error(`Install app error: ${error.message}`);
      res.status(400).send(`Installation error: ${error.message}`);
    }
  }

  // Initiate Shopify app installation (POST request for API)
  @Post('install')
  async installApp(@Body('shop') shop: string) {
    try {
      if (!shop) {
        throw new BadRequestException('Shop domain is required');
      }

      // Clean and validate shop domain format
      let cleanDomain = shop.trim().toLowerCase();

      // Remove protocol if present
      cleanDomain = cleanDomain.replace(/^https?:\/\//, '');

      // Add .myshopify.com if not present
      if (!cleanDomain.includes('.myshopify.com')) {
        cleanDomain = `${cleanDomain}.myshopify.com`;
      }

      // Remove trailing slash
      cleanDomain = cleanDomain.replace(/\/$/, '');

      this.logger.log(`Initiating app installation for shop: ${cleanDomain}`);

      // Generate OAuth URL without brand association
      const oauthUrl = this.shopifyService.generateOAuthUrl(cleanDomain, null);

      return {
        success: true,
        oauthUrl,
        shopDomain: cleanDomain,
      };
    } catch (error) {
      this.logger.error(`Install app error: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // OAuth callback handler
  @Get('callback')
  async handleCallback(
    @Query('code') code: string,
    @Query('shop') shop: string,
    @Query('state') state: string,
    @Query('hmac') hmac: string,
    @Query('timestamp') timestamp: string,
    @Res() res: Response,
    @Req() req: Request,
  ) {
    try {
      this.logger.log(`Handling OAuth callback for shop: ${shop}`);

      if (!code || !shop) {
        throw new Error('Missing required parameters');
      }

      // Verify HMAC for security (Shopify requirement)
      if (hmac && timestamp) {
        const isValid = this.shopifyService.verifyHmac(req.query, hmac);
        if (!isValid) {
          throw new Error('Invalid HMAC signature');
        }
      }

      // For embedded apps, we need to handle the authentication differently
      // If state is provided, it's from our brand connection flow
      let brandId = null;
      if (state) {
        try {
          const stateData = this.shopifyService.verifyState(state);
          brandId = stateData.brandId;
        } catch (error) {
          this.logger.warn(`Invalid state parameter: ${error.message}`);
          // Continue without brandId for initial app installation
        }
      }

      // Exchange code for access token
      const accessToken = await this.shopifyService.exchangeCodeForToken(shop, code);

      // Save connection (with or without brandId)
      const connection = await this.shopifyService.saveConnection(brandId, shop, accessToken);

      this.logger.log(`Successfully connected shop ${shop}${brandId ? ` for brand ${brandId}` : ' (initial install)'}`);

      // For embedded apps, redirect to the app within Shopify admin
      // This satisfies Shopify's "immediate authentication" requirement
      const appUrl = process.env.APP_URL || 'https://gsm-fashion-portal-f54077544850.herokuapp.com';

      if (brandId) {
        // Brand-initiated connection - redirect to frontend
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:8080';
        res.redirect(`${frontendUrl}/shopify/callback?shopify=connected&shop=${encodeURIComponent(shop)}`);
      } else {
        // Initial app installation - redirect to app homepage (embedded app interface)
        // This satisfies Shopify's "redirect to app UI after authentication" requirement
        this.logger.log(`Redirecting to app homepage for shop: ${shop}`);

        // Redirect to our app's root URL which will serve the embedded app interface
        // Since the app is now installed, it will detect this and serve the embedded UI
        const appHomepage = `${appUrl}/?shop=${encodeURIComponent(shop)}&installed=1`;
        res.redirect(appHomepage);
      }
    } catch (error) {
      this.logger.error(`Shopify callback error: ${error.message}`);

      // Determine appropriate error redirect
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:8080';
      res.redirect(`${frontendUrl}/shopify/callback?shopify=error&message=${encodeURIComponent(error.message)}`);
    }
  }

  // Get brand's products from Shopify
  @Get('products')
  @UseGuards(JwtAuthGuard)
  async getProducts(
    @Req() req, 
    @Query('limit') limit = 50, 
    @Query('page_info') pageInfo?: string
  ) {
    try {
      const brandId = req.user._id || req.user.id;
      this.logger.log(`Fetching products for brand: ${brandId}`);
      
      const result = await this.shopifyService.getBrandProducts(brandId, limit, pageInfo);
      
      return {
        success: true,
        ...result
      };
    } catch (error) {
      this.logger.error(`Failed to fetch products: ${error.message}`);
      return {
        success: false,
        error: error.message,
        products: [],
        pageInfo: {}
      };
    }
  }

  // Get single product from Shopify
  @Get('products/:id')
  @UseGuards(JwtAuthGuard)
  async getProduct(@Req() req, @Query('id') productId: string) {
    try {
      const brandId = req.user._id || req.user.id;
      const product = await this.shopifyService.getBrandProduct(brandId, productId);
      
      return {
        success: true,
        product
      };
    } catch (error) {
      this.logger.error(`Failed to fetch product: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Check connection status
  @Get('status')
  @UseGuards(JwtAuthGuard)
  async getConnectionStatus(@Req() req) {
    try {
      const brandId = req.user._id || req.user.id;
      this.logger.log(`Checking connection status for brand: ${brandId}`);
      const isConnected = await this.shopifyService.isShopifyConnected(brandId);
      this.logger.log(`Connection status for brand ${brandId}: ${isConnected}`);
      
      if (isConnected) {
        const connection = await this.shopifyService.getShopifyConnection(brandId);

        // Fetch actual product count from Shopify
        let actualProductCount = connection.total_products || 0;
        try {
          const productsResult = await this.shopifyService.getBrandProducts(brandId, 1);
          // Get total count by fetching first page and checking if there are more
          const firstPageResult = await this.shopifyService.getBrandProducts(brandId, 250); // Max per page
          actualProductCount = firstPageResult.products.length;

          // Update the stored count
          await this.shopifyService.updateSyncStats(brandId, actualProductCount, actualProductCount);
        } catch (error) {
          this.logger.warn(`Failed to fetch product count: ${error.message}`);
        }

        return {
          connected: true,
          shopName: connection.shop_name,
          shopDomain: connection.shop_domain,
          shopEmail: connection.shop_email,
          connectedAt: connection.createdAt || connection['createdAt'],
          lastSyncAt: connection.last_sync_at,
          totalProducts: actualProductCount,
          syncedProducts: connection.synced_products || actualProductCount,
        };
      }
      
      return { connected: false };
    } catch (error) {
      this.logger.error(`Failed to get connection status: ${error.message}`);
      return {
        connected: false,
        error: error.message
      };
    }
  }

  // Disconnect Shopify store
  @Delete('disconnect')
  @UseGuards(JwtAuthGuard)
  async disconnectStore(@Req() req) {
    try {
      const brandId = req.user._id || req.user.id;
      await this.shopifyService.disconnectStore(brandId);
      
      this.logger.log(`Disconnected Shopify store for brand: ${brandId}`);
      
      return {
        success: true,
        message: 'Shopify store disconnected successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to disconnect store: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Test endpoint for development
  @Get('test')
  async testConnection() {
    return {
      message: 'Shopify integration is working',
      clientId: process.env.SHOPIFY_CLIENT_ID ? 'Set' : 'Not set',
      clientSecret: process.env.SHOPIFY_CLIENT_SECRET ? 'Set' : 'Not set',
      appUrl: process.env.APP_URL,
      frontendUrl: process.env.FRONTEND_URL,
      apiVersion: process.env.SHOPIFY_API_VERSION,
    };
  }

  // Fix existing connections with wrong brand_id type
  @Post('fix-connections')
  async fixConnections() {
    try {
      const connections = await this.shopifyService.connectionModel.find({});
      let fixed = 0;

      for (const connection of connections) {
        // Check if brand_id is a string instead of ObjectId
        if (typeof connection.brand_id === 'string') {
          this.logger.log(`Fixing connection for brand_id: ${connection.brand_id}`);
          connection.brand_id = new Types.ObjectId(connection.brand_id);
          await connection.save();
          fixed++;
        }
      }

      this.logger.log(`Fixed ${fixed} connections`);
      return {
        success: true,
        message: `Fixed ${fixed} connections`,
        fixed
      };
    } catch (error) {
      this.logger.error(`Failed to fix connections: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Embedded app page for Shopify admin
  @Get('app')
  async embeddedApp(@Res() res: Response, @Query('shop') shop: string, @Query('embedded') embedded: string) {
    // If this is an embedded request, serve the embedded app
    if (embedded === '1' || embedded === 'true') {
      const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>GSM Portal</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <script src="https://unpkg.com/@shopify/app-bridge@3"></script>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; }
          .container { max-width: 600px; margin: 0 auto; text-align: center; }
          .success { color: #008060; }
          .btn { background: #008060; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1 class="success">✅ GSM Fashion Portal Installed</h1>
          <p>Welcome to GSM Fashion Portal!</p>
          <p><strong>Store:</strong> ${shop || 'Unknown'}</p>
          <p>Your app has been successfully installed and authenticated.</p>
          <p>To connect this store to a brand account and start managing campaigns, visit our main portal:</p>
          <a href="${process.env.FRONTEND_URL || 'https://gsm-fahion-portal.vercel.app'}" class="btn" target="_parent">
            Open GSM Portal
          </a>
        </div>

        <script>
          // Initialize Shopify App Bridge for embedded app
          const AppBridge = window['app-bridge'];
          if (AppBridge && '${shop}') {
            const app = AppBridge.createApp({
              apiKey: '${process.env.SHOPIFY_CLIENT_ID}',
              shopOrigin: '${shop}',
            });

            // Set up navigation
            const TitleBar = AppBridge.actions.TitleBar;
            const titleBar = TitleBar.create(app, {
              title: 'GSM Fashion Portal',
            });
          }
        </script>
      </body>
      </html>
      `;

      res.setHeader('Content-Type', 'text/html');
      res.setHeader('X-Frame-Options', 'ALLOWALL');
      res.send(html);
    } else {
      // If not embedded, redirect to the embedded version
      const embeddedUrl = `https://${shop}/admin/apps/${process.env.SHOPIFY_CLIENT_ID}`;
      res.redirect(embeddedUrl);
    }
  }

  // Test authentication endpoint for embedded app
  @Get('test-auth')
  async testAuth(@Req() req: Request, @Query('shop') shop: string) {
    try {
      const authorization = req.headers.authorization;
      this.logger.log(`Test auth request - shop: ${shop}, auth header: ${authorization ? 'present' : 'none'}`);

      // For embedded apps, this endpoint confirms the app is working
      return {
        success: true,
        message: 'Authentication successful',
        shop: shop,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Test auth error: ${error.message}`);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Shop info endpoint to demonstrate immediate API usage
  @Get('shop-info')
  async getShopInfo(@Query('shop') shop: string) {
    try {
      this.logger.log(`Shop info request for: ${shop}`);

      // Check if we have a connection for this shop
      const connection = await this.shopifyService.getConnectionByShop(shop);

      if (connection) {
        // Get shop info using stored access token
        const shopInfo = await this.shopifyService.getShopInfo(shop, connection.access_token);

        return {
          success: true,
          shop: shopInfo,
          message: 'Shopify API access successful',
        };
      } else {
        return {
          success: false,
          message: 'No connection found for this shop',
          shop: shop,
        };
      }
    } catch (error) {
      this.logger.error(`Shop info error: ${error.message}`);
      return {
        success: false,
        error: error.message,
        shop: shop,
      };
    }
  }

  // Debug OAuth URL generation
  @Get('debug-oauth/:shopDomain')
  async debugOAuthUrl(@Param('shopDomain') shopDomain: string) {
    try {
      const brandId = 'debug-brand-id';
      const oauthUrl = this.shopifyService.generateOAuthUrl(shopDomain, brandId);

      return {
        success: true,
        shopDomain,
        oauthUrl,
        redirectUri: `${process.env.APP_URL}/api/shopify/callback`,
        clientId: process.env.SHOPIFY_CLIENT_ID,
        scopes: 'read_products,read_inventory',
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Test products endpoint without auth (for debugging)
  @Get('test-products')
  async testProducts() {
    try {
      // Get the first connection from database for testing
      const connection = await this.shopifyService.connectionModel.findOne({ is_active: true });

      if (!connection) {
        return { error: 'No active Shopify connections found' };
      }

      const result = await this.shopifyService.getBrandProducts(connection.brand_id.toString(), 10);

      return {
        success: true,
        connectionFound: true,
        shopDomain: connection.shop_domain,
        productCount: result.products.length,
        products: result.products.map(p => ({
          id: p.id,
          title: p.title,
          status: p.status,
          variants: p.variants.length
        }))
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}
