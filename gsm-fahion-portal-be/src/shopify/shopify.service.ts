import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { ShopifyConnection } from './schemas/shopify-connection.schema';
import axios from 'axios';
import * as crypto from 'crypto';

@Injectable()
export class ShopifyService {
  private readonly logger = new Logger(ShopifyService.name);

  constructor(
    @InjectModel(ShopifyConnection.name)
    private shopifyConnectionModel: Model<ShopifyConnection>,
  ) {
    // Run migration on startup to fix index issues
    this.migrateIndexes();
  }

  // Expose model for testing
  get connectionModel() {
    return this.shopifyConnectionModel;
  }

  // Migration method to fix index issues
  private async migrateIndexes() {
    try {
      const collection = this.shopifyConnectionModel.collection;

      // Get existing indexes
      const indexes = await collection.indexes();
      const hasOldIndex = indexes.some(index =>
        index.name === 'shop_domain_1' ||
        (index.key && index.key.shop_domain === 1 && index.unique === true)
      );

      if (hasOldIndex) {
        this.logger.log('Dropping old shop_domain unique index...');
        try {
          await collection.dropIndex('shop_domain_1');
          this.logger.log('Successfully dropped old shop_domain unique index');
        } catch (error) {
          this.logger.warn(`Could not drop old index: ${error.message}`);
        }
      }

      // Check if new index exists
      const hasNewIndex = indexes.some(index =>
        index.name === 'unique_active_shop_domain'
      );

      if (!hasNewIndex) {
        this.logger.log('Creating new compound unique index for active shop domains...');
        await collection.createIndex(
          { shop_domain: 1, is_active: 1 },
          {
            unique: true,
            partialFilterExpression: { is_active: true },
            name: 'unique_active_shop_domain'
          }
        );
        this.logger.log('Successfully created new compound unique index');
      }
    } catch (error) {
      this.logger.error(`Index migration failed: ${error.message}`);
    }
  }

  // Generate OAuth URL for brand's store or app installation
  generateOAuthUrl(shopDomain: string, brandId: string | null): string {
    const scopes = 'read_products,read_inventory';
    const redirectUri = `${process.env.APP_URL}/api/shopify/callback`;

    this.logger.log(`Generating OAuth URL for shop: ${shopDomain}${brandId ? `, brand: ${brandId}` : ' (app installation)'}`);

    let url = `https://admin.shopify.com/oauth/authorize?` +
              `client_id=${process.env.SHOPIFY_CLIENT_ID}&` +
              `scope=${scopes}&` +
              `redirect_uri=${encodeURIComponent(redirectUri)}&` +
              `shop=${shopDomain}`;

    // Add state parameter only if brandId is provided
    if (brandId) {
      const state = this.generateState(brandId);
      url += `&state=${state}`;
    }

    return url;
  }

  // Exchange authorization code for access token
  async exchangeCodeForToken(shopDomain: string, code: string): Promise<string> {
    try {
      this.logger.log(`Exchanging code for token for shop: ${shopDomain}`);
      
      const response = await axios.post(
        `https://${shopDomain}/admin/oauth/access_token`,
        {
          client_id: process.env.SHOPIFY_CLIENT_ID,
          client_secret: process.env.SHOPIFY_CLIENT_SECRET,
          code: code,
        }
      );

      this.logger.log(`Successfully obtained access token for shop: ${shopDomain}`);
      return response.data.access_token;
    } catch (error) {
      this.logger.error(`Failed to exchange code for token: ${error.message}`);
      throw new Error(`Failed to exchange code for token: ${error.message}`);
    }
  }

  // Save Shopify connection for a brand (or initial app installation)
  async saveConnection(
    brandId: string | null,
    shopDomain: string,
    accessToken: string,
  ): Promise<ShopifyConnection> {
    try {
      // Get shop info
      const shopInfo = await this.getShopInfo(shopDomain, accessToken);

      this.logger.log(`Saving connection for ${brandId ? `brand: ${brandId}` : 'initial app install'}, shop: ${shopDomain}`);

      if (brandId) {
        // Brand-initiated connection - handle existing connections

        // First, check if there's an existing connection for this exact brand + shop domain combination
        const exactMatch = await this.shopifyConnectionModel.findOne({
          brand_id: new Types.ObjectId(brandId),
          shop_domain: shopDomain,
        });

        if (exactMatch) {
          // Perfect match found - just reactivate and update the existing connection
          this.logger.log(`Found existing connection for brand ${brandId} and shop ${shopDomain}, reactivating...`);
          exactMatch.access_token = accessToken;
          exactMatch.shop_name = shopInfo.name;
          exactMatch.shop_email = shopInfo.email;
          exactMatch.shop_id = shopInfo.id.toString();
          exactMatch.is_active = true;
          exactMatch.last_sync_at = new Date();
          return await exactMatch.save();
        }

        // Check if this brand has any other active connections (should disconnect them first)
        const existingBrandConnection = await this.shopifyConnectionModel.findOne({
          brand_id: new Types.ObjectId(brandId),
          is_active: true,
        });

        if (existingBrandConnection) {
          // Deactivate the existing connection for this brand
          this.logger.log(`Deactivating existing connection for brand ${brandId} (shop: ${existingBrandConnection.shop_domain})`);
          existingBrandConnection.is_active = false;
          await existingBrandConnection.save();
        }
      } else {
        // Initial app installation - check if shop already has a connection
        const existingShopConnection = await this.shopifyConnectionModel.findOne({
          shop_domain: shopDomain,
          is_active: true,
        });

        if (existingShopConnection) {
          // Update existing connection
          this.logger.log(`Updating existing connection for shop ${shopDomain}`);
          existingShopConnection.access_token = accessToken;
          existingShopConnection.shop_name = shopInfo.name;
          existingShopConnection.shop_email = shopInfo.email;
          existingShopConnection.shop_id = shopInfo.id.toString();
          existingShopConnection.last_sync_at = new Date();
          return await existingShopConnection.save();
        }
      }

      // Create new connection
      const connectionData: any = {
        shop_domain: shopDomain,
        access_token: accessToken,
        shop_name: shopInfo.name,
        shop_email: shopInfo.email,
        shop_id: shopInfo.id.toString(),
        is_active: true,
        last_sync_at: new Date(),
        connected_at: new Date(),
      };

      // Only add brand_id if it's provided (not null)
      if (brandId) {
        connectionData.brand_id = new Types.ObjectId(brandId);

        // Use upsert to either update existing inactive connection or create new one
        const upsertResult = await this.shopifyConnectionModel.findOneAndUpdate(
          {
            brand_id: new Types.ObjectId(brandId),
            shop_domain: shopDomain,
          },
          connectionData,
          {
            upsert: true, // Create if doesn't exist
            new: true,    // Return the updated document
            runValidators: true,
          }
        );

        this.logger.log(`Successfully saved/updated connection for brand ${brandId} and shop ${shopDomain}`);
        return upsertResult;
      } else {
        // Initial app installation without brand association
        const newConnection = new this.shopifyConnectionModel(connectionData);
        const savedConnection = await newConnection.save();

        this.logger.log(`Successfully created initial connection for shop ${shopDomain}`);
        return savedConnection;
      }
    } catch (error) {
      this.logger.error(`Failed to save connection: ${error.message}`);
      throw error;
    }
  }

  // Removed duplicate method - using the new one below

  // Get products from brand's Shopify store using GraphQL API
  async getBrandProducts(brandId: string, limit = 50, cursor?: string) {
    const connection = await this.getShopifyConnection(brandId);

    if (!connection) {
      throw new NotFoundException('Shopify store not connected');
    }

    try {
      this.logger.log(`Fetching products for brand: ${brandId} from shop: ${connection.shop_domain} using GraphQL`);

      const query = `
        query getProducts($first: Int!, $after: String) {
          products(first: $first, after: $after) {
            edges {
              node {
                id
                title
                handle
                description
                productType
                vendor
                tags
                status
                createdAt
                updatedAt
                images(first: 10) {
                  edges {
                    node {
                      id
                      url
                      altText
                    }
                  }
                }
                variants(first: 100) {
                  edges {
                    node {
                      id
                      title
                      price
                      compareAtPrice
                      sku
                      inventoryQuantity
                      taxable
                      image {
                        id
                        url
                        altText
                      }
                    }
                  }
                }
              }
              cursor
            }
            pageInfo {
              hasNextPage
              hasPreviousPage
              startCursor
              endCursor
            }
          }
        }
      `;

      const variables = {
        first: limit,
        after: cursor || null
      };

      const response = await axios.post(
        `https://${connection.shop_domain}/admin/api/2024-07/graphql.json`,
        {
          query,
          variables
        },
        {
          headers: {
            'X-Shopify-Access-Token': connection.access_token,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data.errors) {
        throw new Error(`GraphQL errors: ${JSON.stringify(response.data.errors)}`);
      }

      const products = response.data.data.products.edges.map(edge => this.transformGraphQLProduct(edge.node));
      const pageInfo = response.data.data.products.pageInfo;

      return {
        products,
        pageInfo: {
          hasNextPage: pageInfo.hasNextPage,
          hasPreviousPage: pageInfo.hasPreviousPage,
          next: pageInfo.hasNextPage ? pageInfo.endCursor : null,
          previous: pageInfo.hasPreviousPage ? pageInfo.startCursor : null
        },
        shop: {
          name: connection.shop_name,
          domain: connection.shop_domain,
        }
      };
    } catch (error) {
      this.logger.error(`Failed to fetch products: ${error.message}`);
      throw new Error(`Failed to fetch products: ${error.message}`);
    }
  }

  // Get single product from Shopify using GraphQL API
  async getBrandProduct(brandId: string, productId: string) {
    const connection = await this.getShopifyConnection(brandId);

    if (!connection) {
      throw new NotFoundException('Shopify store not connected');
    }

    try {
      const query = `
        query getProduct($id: ID!) {
          product(id: $id) {
            id
            title
            handle
            description
            productType
            vendor
            tags
            status
            createdAt
            updatedAt
            images(first: 10) {
              edges {
                node {
                  id
                  url
                  altText
                }
              }
            }
            variants(first: 100) {
              edges {
                node {
                  id
                  title
                  price
                  compareAtPrice
                  sku
                  inventoryQuantity
                  taxable
                  image {
                    id
                    url
                    altText
                  }
                }
              }
            }
          }
        }
      `;

      const variables = {
        id: `gid://shopify/Product/${productId}`
      };

      const response = await axios.post(
        `https://${connection.shop_domain}/admin/api/2024-07/graphql.json`,
        {
          query,
          variables
        },
        {
          headers: {
            'X-Shopify-Access-Token': connection.access_token,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data.errors) {
        throw new Error(`GraphQL errors: ${JSON.stringify(response.data.errors)}`);
      }

      return this.transformGraphQLProduct(response.data.data.product);
    } catch (error) {
      this.logger.error(`Failed to fetch product: ${error.message}`);
      throw new Error(`Failed to fetch product: ${error.message}`);
    }
  }

  // Get Shopify connection for a brand
  async getShopifyConnection(brandId: string): Promise<ShopifyConnection | null> {
    return await this.shopifyConnectionModel.findOne({
      brand_id: new Types.ObjectId(brandId),
      is_active: true,
    });
  }

  // Check if brand has connected Shopify
  async isShopifyConnected(brandId: string): Promise<boolean> {
    const connection = await this.getShopifyConnection(brandId);
    return !!connection; // getShopifyConnection already filters by is_active: true
  }

  // Disconnect Shopify store
  async disconnectStore(brandId: string): Promise<void> {
    // Set ALL connections as inactive and clear sensitive data (use updateMany instead of updateOne)
    const result = await this.shopifyConnectionModel.updateMany(
      { brand_id: new Types.ObjectId(brandId) },
      {
        is_active: false,
        access_token: '', // Clear the access token for security
        last_sync_at: null
      }
    );
    this.logger.log(`Disconnected Shopify store for brand: ${brandId}, updated ${result.modifiedCount} records`);
  }

  // Generate secure state parameter
  private generateState(brandId: string): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(7);
    const data = `${brandId}:${timestamp}:${random}`;
    return Buffer.from(data).toString('base64');
  }

  // Verify and decode state parameter
  verifyState(state: string): { brandId: string; timestamp: number } {
    try {
      const decoded = Buffer.from(state, 'base64').toString();
      const [brandId, timestamp] = decoded.split(':');

      // Check if state is not older than 10 minutes
      const now = Date.now();
      const stateTime = parseInt(timestamp);
      if (now - stateTime > 10 * 60 * 1000) {
        throw new Error('State parameter expired');
      }

      return { brandId, timestamp: stateTime };
    } catch (error) {
      this.logger.error(`Invalid state parameter: ${error.message}`);
      throw new Error('Invalid state parameter');
    }
  }

  // Verify HMAC signature for OAuth callback
  verifyHmac(query: any, hmac: string): boolean {
    try {
      // Remove hmac and signature from query params
      const { hmac: _, signature: __, ...params } = query;

      // Sort parameters and create query string
      const sortedParams = Object.keys(params)
        .sort()
        .map(key => `${key}=${params[key]}`)
        .join('&');

      // Create HMAC
      const hash = crypto
        .createHmac('sha256', process.env.SHOPIFY_CLIENT_SECRET)
        .update(sortedParams)
        .digest('hex');

      return crypto.timingSafeEqual(
        Buffer.from(hmac, 'hex'),
        Buffer.from(hash, 'hex')
      );
    } catch (error) {
      this.logger.error(`HMAC verification failed: ${error.message}`);
      return false;
    }
  }

  // Get connection by shop domain
  async getConnectionByShop(shopDomain: string): Promise<ShopifyConnection | null> {
    try {
      const connection = await this.shopifyConnectionModel.findOne({
        shop_domain: shopDomain,
        is_active: true,
      });

      return connection;
    } catch (error) {
      this.logger.error(`Error getting connection for shop ${shopDomain}: ${error.message}`);
      return null;
    }
  }

  // Get shop information using GraphQL API
  async getShopInfo(shopDomain: string, accessToken: string): Promise<any> {
    try {
      const query = `
        query getShop {
          shop {
            id
            name
            email
            myshopifyDomain
            primaryDomain {
              host
              sslEnabled
            }
            plan {
              displayName
              partnerDevelopment
              shopifyPlus
            }
            currencyCode
            timezoneAbbreviation
            ianaTimezone
            weightUnit
            unitSystem
            contactEmail
            createdAt
            updatedAt
          }
        }
      `;

      const response = await axios.post(
        `https://${shopDomain}/admin/api/2024-07/graphql.json`,
        { query },
        {
          headers: {
            'X-Shopify-Access-Token': accessToken,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data.errors) {
        throw new Error(`GraphQL errors: ${JSON.stringify(response.data.errors)}`);
      }

      return response.data.data.shop;
    } catch (error) {
      this.logger.error(`Error getting shop info for ${shopDomain}: ${error.message}`);
      throw error;
    }
  }

  // Transform GraphQL product to REST-like format for compatibility
  private transformGraphQLProduct(graphqlProduct: any): any {
    if (!graphqlProduct) return null;

    // Extract numeric ID from GraphQL ID
    const numericId = graphqlProduct.id.split('/').pop();

    return {
      id: parseInt(numericId),
      title: graphqlProduct.title,
      handle: graphqlProduct.handle,
      body_html: graphqlProduct.description,
      product_type: graphqlProduct.productType,
      vendor: graphqlProduct.vendor,
      tags: graphqlProduct.tags.join(','),
      status: graphqlProduct.status.toLowerCase(),
      created_at: graphqlProduct.createdAt,
      updated_at: graphqlProduct.updatedAt,
      images: graphqlProduct.images.edges.map((edge: any) => ({
        id: parseInt(edge.node.id.split('/').pop()),
        src: edge.node.url,
        alt: edge.node.altText,
        position: 1
      })),
      variants: graphqlProduct.variants.edges.map((edge: any, index: number) => ({
        id: parseInt(edge.node.id.split('/').pop()),
        product_id: parseInt(numericId),
        title: edge.node.title,
        price: edge.node.price,
        compare_at_price: edge.node.compareAtPrice,
        sku: edge.node.sku,
        inventory_quantity: edge.node.inventoryQuantity,
        weight: null, // Not available in GraphQL API
        weight_unit: null, // Not available in GraphQL API
        requires_shipping: true, // Default value
        taxable: edge.node.taxable,
        position: index + 1,
        image_id: edge.node.image ? parseInt(edge.node.image.id.split('/').pop()) : null
      }))
    };
  }

  // Update sync statistics
  async updateSyncStats(brandId: string, totalProducts: number, syncedProducts: number) {
    await this.shopifyConnectionModel.updateOne(
      { brand_id: new Types.ObjectId(brandId) },
      {
        total_products: totalProducts,
        synced_products: syncedProducts,
        last_sync_at: new Date()
      }
    );
  }
}
