import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ShopifyService } from './shopify.service';
import { ShopifyController } from './shopify.controller';
import { ShopifyWebhookController } from './webhook.controller';
import { ShopifyPrivacyController } from './privacy.controller';
import { ShopifyConnection, ShopifyConnectionSchema } from './schemas/shopify-connection.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ShopifyConnection.name, schema: ShopifyConnectionSchema },
    ]),
  ],
  controllers: [ShopifyController, ShopifyWebhookController, ShopifyPrivacyController],
  providers: [ShopifyService],
  exports: [ShopifyService],
})
export class ShopifyModule {}
