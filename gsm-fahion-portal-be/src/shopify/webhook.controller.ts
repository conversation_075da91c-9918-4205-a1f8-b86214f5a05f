import { 
  Controller, 
  Post, 
  Body, 
  Headers,
  Logger,
  RawBodyRequest,
  Req
} from '@nestjs/common';
import { Request } from 'express';
import * as crypto from 'crypto';

@Controller('webhooks/shopify')
export class ShopifyWebhookController {
  private readonly logger = new Logger(ShopifyWebhookController.name);

  // Verify webhook authenticity
  private verifyWebhook(body: string, signature: string): boolean {
    try {
      const hmac = crypto.createHmac('sha256', process.env.SHOPIFY_WEBHOOK_SECRET);
      hmac.update(body, 'utf8');
      const hash = hmac.digest('base64');
      
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'base64'),
        Buffer.from(hash, 'base64')
      );
    } catch (error) {
      this.logger.error(`Webhook verification failed: ${error.message}`);
      return false;
    }
  }

  @Post('products/create')
  async handleProductCreate(
    @Body() product: any,
    @Headers('x-shopify-hmac-sha256') signature: string,
    @Headers('x-shopify-shop-domain') shopDomain: string,
    @Req() req: RawBodyRequest<Request>
  ) {
    try {
      // Verify webhook authenticity
      const body = req.rawBody?.toString() || JSON.stringify(product);
      if (!this.verifyWebhook(body, signature)) {
        this.logger.warn(`Invalid webhook signature for product create from ${shopDomain}`);
        return { received: false, error: 'Invalid signature' };
      }

      this.logger.log(`Product created webhook received from ${shopDomain}: ${product.title}`);
      
      // TODO: Process product creation
      // - Find brand by shop domain
      // - Add product to brand's catalog
      // - Update sync statistics
      
      return { received: true };
    } catch (error) {
      this.logger.error(`Error processing product create webhook: ${error.message}`);
      return { received: false, error: error.message };
    }
  }

  @Post('products/update')
  async handleProductUpdate(
    @Body() product: any,
    @Headers('x-shopify-hmac-sha256') signature: string,
    @Headers('x-shopify-shop-domain') shopDomain: string,
    @Req() req: RawBodyRequest<Request>
  ) {
    try {
      const body = req.rawBody?.toString() || JSON.stringify(product);
      if (!this.verifyWebhook(body, signature)) {
        this.logger.warn(`Invalid webhook signature for product update from ${shopDomain}`);
        return { received: false, error: 'Invalid signature' };
      }

      this.logger.log(`Product updated webhook received from ${shopDomain}: ${product.title}`);
      
      // TODO: Process product update
      // - Find brand by shop domain
      // - Update product in brand's catalog
      
      return { received: true };
    } catch (error) {
      this.logger.error(`Error processing product update webhook: ${error.message}`);
      return { received: false, error: error.message };
    }
  }

  @Post('products/delete')
  async handleProductDelete(
    @Body() product: any,
    @Headers('x-shopify-hmac-sha256') signature: string,
    @Headers('x-shopify-shop-domain') shopDomain: string,
    @Req() req: RawBodyRequest<Request>
  ) {
    try {
      const body = req.rawBody?.toString() || JSON.stringify(product);
      if (!this.verifyWebhook(body, signature)) {
        this.logger.warn(`Invalid webhook signature for product delete from ${shopDomain}`);
        return { received: false, error: 'Invalid signature' };
      }

      this.logger.log(`Product deleted webhook received from ${shopDomain}: ${product.id}`);
      
      // TODO: Process product deletion
      // - Find brand by shop domain
      // - Remove product from brand's catalog
      
      return { received: true };
    } catch (error) {
      this.logger.error(`Error processing product delete webhook: ${error.message}`);
      return { received: false, error: error.message };
    }
  }

  @Post('inventory/update')
  async handleInventoryUpdate(
    @Body() inventory: any,
    @Headers('x-shopify-hmac-sha256') signature: string,
    @Headers('x-shopify-shop-domain') shopDomain: string,
    @Req() req: RawBodyRequest<Request>
  ) {
    try {
      const body = req.rawBody?.toString() || JSON.stringify(inventory);
      if (!this.verifyWebhook(body, signature)) {
        this.logger.warn(`Invalid webhook signature for inventory update from ${shopDomain}`);
        return { received: false, error: 'Invalid signature' };
      }

      this.logger.log(`Inventory updated webhook received from ${shopDomain}`);
      
      // TODO: Process inventory update
      // - Find brand by shop domain
      // - Update inventory levels
      
      return { received: true };
    } catch (error) {
      this.logger.error(`Error processing inventory update webhook: ${error.message}`);
      return { received: false, error: error.message };
    }
  }
}
