import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type CreatorProfileDocument = CreatorProfile & Document & {
  createdAt: Date;
  updatedAt: Date;
};

@Schema({ timestamps: true })
export class CreatorProfile {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  user_id: Types.ObjectId;

  @Prop()
  display_name: string;

  @Prop()
  bio: string;

  @Prop()
  avatar_url: string;

  @Prop()
  location: string;

  @Prop()
  date_of_birth: Date;

  @Prop()
  phone_number: string;

  @Prop({
    type: {
      instagram: String,
      tiktok: String,
      twitter: String,
      youtube: String,
      facebook: String,
    },
    default: {},
  })
  social_handles: {
    instagram?: string;
    tiktok?: string;
    twitter?: string;
    youtube?: string;
    facebook?: string;
  };

  @Prop({ type: [String], default: [] })
  interests: string[];

  @Prop({ type: [String], default: [] })
  preferred_brands: string[];

  @Prop({ default: 0 })
  total_tokens: number;

  @Prop({ default: 0 })
  completed_campaigns: number;

  @Prop({ default: 0 })
  total_submissions: number;

  @Prop({ default: 0 })
  approved_submissions: number;

  @Prop({ default: false })
  profile_completed: boolean;

  @Prop({ default: true })
  is_active: boolean;

  @Prop()
  last_active_at: Date;

  @Prop({ type: Object })
  preferences: {
    notifications?: {
      email?: boolean;
      push?: boolean;
      campaign_invites?: boolean;
      reward_updates?: boolean;
    };
    privacy?: {
      profile_visibility?: 'public' | 'private';
      show_stats?: boolean;
    };
  };
}

export const CreatorProfileSchema = SchemaFactory.createForClass(CreatorProfile);

// Create indexes for better performance
CreatorProfileSchema.index({ user_id: 1 }, { unique: true });
CreatorProfileSchema.index({ is_active: 1 });
CreatorProfileSchema.index({ total_tokens: -1 });
CreatorProfileSchema.index({ completed_campaigns: -1 });
CreatorProfileSchema.index({ interests: 1 });
CreatorProfileSchema.index({ preferred_brands: 1 });
