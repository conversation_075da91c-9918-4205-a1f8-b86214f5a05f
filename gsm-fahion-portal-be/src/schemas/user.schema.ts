import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type UserDocument = User & Document;

export enum UserRole {
  BRAND = 'brand',
  ADMIN = 'admin',
  CREATOR = 'creator',
}

export enum UserStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

@Schema({ timestamps: true })
export class User {
  @Prop({ required: true, unique: true })
  email: string;

  @Prop({ required: true })
  password: string;

  @Prop({ type: String, enum: UserRole, default: UserRole.BRAND })
  role: UserRole;

  @Prop({ type: String, enum: UserStatus, default: UserStatus.PENDING })
  status: UserStatus;

  @Prop({ default: false })
  email_verified: boolean;

  @Prop()
  otp: string;

  @Prop()
  otp_expires: Date;

  @Prop()
  reset_token: string;

  @Prop()
  reset_token_expires: Date;
}

export const UserSchema = SchemaFactory.createForClass(User);
