import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type BrandProfileDocument = BrandProfile & Document;

@Schema({ timestamps: true })
export class BrandProfile {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true, unique: true })
  user_id: Types.ObjectId;

  @Prop()
  brand_name: string;

  @Prop()
  website: string;

  @Prop()
  bio: string;

  @Prop()
  logo_url: string;

  @Prop({
    type: {
      instagram: String,
      tiktok: String,
      twitter: String,
      facebook: String,
    },
    default: {},
  })
  social_handles: {
    instagram?: string;
    tiktok?: string;
    twitter?: string;
    facebook?: string;
  };

  @Prop()
  business_name: string;

  @Prop()
  tax_id: string;

  @Prop()
  business_registration_doc_url: string;

  @Prop({ default: false })
  profile_completed: boolean;
}

export const BrandProfileSchema = SchemaFactory.createForClass(BrandProfile);
