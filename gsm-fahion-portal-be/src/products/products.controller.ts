import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Req,
  UseGuards,
  Logger,
  SetMetadata
} from '@nestjs/common';
import { ProductsService } from './products.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('products')
@UseGuards(JwtAuthGuard)
export class ProductsController {
  private readonly logger = new Logger(ProductsController.name);

  constructor(private readonly productsService: ProductsService) {}

  // Sync products from Shopify
  @Post('sync')
  async syncProducts(@Req() req) {
    try {
      const brandId = req.user.id;
      this.logger.log(`Starting product sync for brand: ${brandId}`);
      
      const result = await this.productsService.syncProductsFromShopify(brandId);
      
      return {
        success: true,
        message: `Successfully synced ${result.synced} products`,
        synced: result.synced,
        errors: result.errors
      };
    } catch (error) {
      this.logger.error(`Product sync failed: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get products with filtering and pagination
  @Get()
  async getProducts(
    @Req() req,
    @Query('page') page = 1,
    @Query('limit') limit = 20,
    @Query('search') search?: string,
    @Query('status') status?: string,
    @Query('product_type') product_type?: string,
    @Query('category') category?: string,
    @Query('import_source') import_source?: string,
    @Query('verification_type') verification_type?: string,
    @Query('import_status') import_status?: string,
    @Query('tags') tags?: string,
    @Query('sort') sort?: string,
    @Query('order') order?: string
  ) {
    try {
      const brandId = req.user.id;
      
      const options = {
        page: parseInt(page.toString()),
        limit: parseInt(limit.toString()),
        search,
        status,
        product_type,
        category,
        import_source,
        verification_type,
        import_status,
        tags: tags ? tags.split(',') : undefined,
        sort,
        order
      };

      const result = await this.productsService.getProductsByBrand(brandId, options);
      
      return {
        success: true,
        ...result
      };
    } catch (error) {
      this.logger.error(`Failed to fetch products: ${error.message}`);
      return {
        success: false,
        error: error.message,
        products: [],
        pagination: { page: 1, limit: 20, total: 0, pages: 0 }
      };
    }
  }

  // Get single product
  @Get(':id')
  async getProduct(@Req() req, @Param('id') productId: string) {
    try {
      const brandId = req.user.id;
      const product = await this.productsService.getProductById(brandId, productId);
      
      if (!product) {
        return {
          success: false,
          error: 'Product not found'
        };
      }
      
      return {
        success: true,
        product
      };
    } catch (error) {
      this.logger.error(`Failed to fetch product: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Update product
  @Put(':id')
  async updateProduct(
    @Req() req, 
    @Param('id') productId: string,
    @Body() updateData: any
  ) {
    try {
      const brandId = req.user.id;
      
      // Only allow certain fields to be updated
      const allowedFields = [
        'verification_type',
        'campaigns_count',
        'is_active'
      ];
      
      const filteredData = Object.keys(updateData)
        .filter(key => allowedFields.includes(key))
        .reduce((obj, key) => {
          obj[key] = updateData[key];
          return obj;
        }, {});

      const product = await this.productsService.updateProduct(brandId, productId, filteredData);
      
      if (!product) {
        return {
          success: false,
          error: 'Product not found'
        };
      }
      
      return {
        success: true,
        product
      };
    } catch (error) {
      this.logger.error(`Failed to update product: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Delete product (soft delete)
  @Delete(':id')
  async deleteProduct(@Req() req, @Param('id') productId: string) {
    try {
      const brandId = req.user.id;
      const success = await this.productsService.deleteProduct(brandId, productId);
      
      if (!success) {
        return {
          success: false,
          error: 'Product not found'
        };
      }
      
      return {
        success: true,
        message: 'Product deleted successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to delete product: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get product statistics
  @Get('stats/overview')
  async getProductStats(@Req() req) {
    try {
      const brandId = req.user.id;
      const stats = await this.productsService.getProductStats(brandId);
      
      return {
        success: true,
        stats
      };
    } catch (error) {
      this.logger.error(`Failed to fetch product stats: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Get filter options
  @Get('filters/options')
  async getFilterOptions(@Req() req) {
    try {
      const brandId = req.user.id;
      
      const [productTypes, tags] = await Promise.all([
        this.productsService.getProductTypes(brandId),
        this.productsService.getProductTags(brandId)
      ]);
      
      return {
        success: true,
        filters: {
          productTypes,
          tags,
          statuses: ['active', 'draft', 'archived']
        }
      };
    } catch (error) {
      this.logger.error(`Failed to fetch filter options: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Fix inactive products (admin endpoint)
  @Post('fix-inactive')
  async fixInactiveProducts() {
    try {
      const result = await this.productsService.fixInactiveProducts();
      return {
        success: true,
        message: `Fixed ${result.updated} inactive products`,
        updated: result.updated
      };
    } catch (error) {
      this.logger.error(`Failed to fix inactive products: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }
}
