import { IsString, <PERSON><PERSON><PERSON>al, IsNumber, IsArray, IsEnum, ValidateNested, IsBoolean } from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class CsvProductRowDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNumber()
  @Transform(({ value }) => parseFloat(value))
  price: number;

  @IsOptional()
  @IsString()
  sku?: string;

  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsString()
  product_type?: string;

  @IsOptional()
  @IsString()
  vendor?: string;

  @IsOptional()
  @IsString()
  tags?: string; // Comma-separated string

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? parseInt(value) : 0)
  inventory?: number;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  weight?: number;

  @IsOptional()
  @IsString()
  weight_unit?: string;

  @IsOptional()
  @IsString()
  barcode?: string;

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? parseFloat(value) : undefined)
  compare_at_price?: number;

  // Variant options
  @IsOptional()
  @IsString()
  option1_name?: string;

  @IsOptional()
  @IsString()
  option1_value?: string;

  @IsOptional()
  @IsString()
  option2_name?: string;

  @IsOptional()
  @IsString()
  option2_value?: string;

  @IsOptional()
  @IsString()
  option3_name?: string;

  @IsOptional()
  @IsString()
  option3_value?: string;
}

export class CsvImportValidationDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CsvProductRowDto)
  products: CsvProductRowDto[];

  @IsString()
  import_batch_id: string;
}

export class ProductImageUploadDto {
  @IsString()
  product_id: string;

  @IsArray()
  @IsString({ each: true })
  image_urls: string[];

  @IsOptional()
  @IsNumber()
  primary_image_index?: number;
}

export class BulkImageUploadDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductImageUploadDto)
  products: ProductImageUploadDto[];
}

export class CsvImportFinalizeDto {
  @IsString()
  import_batch_id: string;

  @IsOptional()
  @IsEnum(['qr', 'nfc', 'manual'])
  default_verification_type?: string;

  @IsOptional()
  @IsBoolean()
  publish_immediately?: boolean;
}

export class ProductVerificationUpdateDto {
  @IsArray()
  @IsString({ each: true })
  product_ids: string[];

  @IsEnum(['qr', 'nfc', 'manual'])
  verification_type: string;
}

export class CsvTemplateDto {
  name: string;
  description: string;
  price: number;
  sku: string;
  category: string;
  product_type: string;
  vendor: string;
  tags: string;
  inventory: number;
  weight: number;
  weight_unit: string;
  barcode: string;
  compare_at_price: number;
  option1_name: string;
  option1_value: string;
  option2_name: string;
  option2_value: string;
  option3_name: string;
  option3_value: string;
}
