import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class ProductVariant extends Document {
  @Prop()
  shopify_variant_id?: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true, type: Number })
  price: number;

  @Prop({ type: Number })
  compare_at_price?: number;

  @Prop()
  sku?: string;

  @Prop()
  barcode?: string;

  @Prop({ default: 0 })
  inventory_quantity: number;

  @Prop({ type: Number })
  weight?: number;

  @Prop()
  weight_unit?: string;

  @Prop()
  option1?: string;

  @Prop()
  option2?: string;

  @Prop()
  option3?: string;

  @Prop()
  image_id?: string;

  @Prop({ default: 1 })
  position: number;

  @Prop({ default: true })
  available: boolean;
}

@Schema({ timestamps: true })
export class ProductImage extends Document {
  @Prop()
  shopify_image_id?: string;

  @Prop({ required: true })
  src: string;

  @Prop()
  alt?: string;

  @Prop({ default: 1 })
  position: number;

  @Prop({ default: false })
  is_primary: boolean;

  @Prop()
  width?: number;

  @Prop()
  height?: number;
}

@Schema({ timestamps: true })
export class ProductOption extends Document {
  @Prop()
  shopify_option_id?: string;

  @Prop({ required: true })
  name: string;

  @Prop({ default: 1 })
  position: number;

  @Prop({ type: [String], default: [] })
  values: string[];
}

@Schema({ timestamps: true })
export class Product extends Document {
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  brand_id: Types.ObjectId;

  @Prop()
  shopify_product_id?: string;

  @Prop({ required: true })
  title: string;

  @Prop({ type: String })
  description: string;

  @Prop()
  vendor: string;

  @Prop()
  product_type?: string;

  @Prop()
  category?: string;

  @Prop()
  handle?: string;

  @Prop({ enum: ['active', 'archived', 'draft'], default: 'active' })
  status: string;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: [ProductImage], default: [] })
  images: ProductImage[];

  @Prop({ type: [ProductVariant], default: [] })
  variants: ProductVariant[];

  @Prop({ type: [ProductOption], default: [] })
  options: ProductOption[];

  @Prop()
  published_at: Date;

  @Prop()
  shopify_created_at: Date;

  @Prop()
  shopify_updated_at: Date;

  @Prop({ enum: ['shopify', 'csv', 'manual'], default: 'shopify' })
  import_source: string;

  @Prop()
  external_url: string; // Shopify product URL for buy-now

  @Prop({ default: 0 })
  campaigns_count: number;

  @Prop({ enum: ['qr', 'nfc', 'manual'] })
  verification_type?: string;

  @Prop({ default: true })
  is_active: boolean;

  @Prop()
  last_synced_at?: Date;

  // CSV import specific fields
  @Prop()
  import_batch_id?: string;

  @Prop({ type: Object })
  csv_data?: any;

  @Prop({ enum: ['pending_images', 'ready', 'published'] })
  import_status?: string;
}

export const ProductVariantSchema = SchemaFactory.createForClass(ProductVariant);
export const ProductImageSchema = SchemaFactory.createForClass(ProductImage);
export const ProductOptionSchema = SchemaFactory.createForClass(ProductOption);
export const ProductSchema = SchemaFactory.createForClass(Product);

// Create indexes for better performance
ProductSchema.index({ brand_id: 1, status: 1 });
ProductSchema.index({ brand_id: 1, import_source: 1 });
ProductSchema.index({ shopify_product_id: 1 }, { unique: true, sparse: true });
ProductSchema.index({ handle: 1 });
ProductSchema.index({ tags: 1 });
ProductSchema.index({ product_type: 1 });
ProductSchema.index({ category: 1 });
ProductSchema.index({ is_active: 1 });
ProductSchema.index({ import_batch_id: 1 });
ProductSchema.index({ import_status: 1 });
ProductSchema.index({ verification_type: 1 });
ProductSchema.index({ last_synced_at: 1 });
