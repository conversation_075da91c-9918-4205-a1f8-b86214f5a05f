import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  Request,
  BadRequestException,
  Logger,
  Res,
  SetMetadata,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CsvImportService } from '../services/csv-import.service';
import {
  CsvImportValidationDto,
  CsvImportFinalizeDto,
  ProductImageUploadDto,
  BulkImageUploadDto,
  ProductVerificationUpdateDto,
} from '../dto/csv-import.dto';
import { Response } from 'express';
import { randomUUID } from 'crypto';

@Controller('products/csv')
@UseGuards(JwtAuthGuard)
export class CsvImportController {
  private readonly logger = new Logger(CsvImportController.name);

  constructor(private readonly csvImportService: CsvImportService) {}

  @Get('template')
  @SetMetadata('isPublic', true)
  async downloadTemplate(@Res() res: Response) {
    try {
      const template = this.csvImportService.generateCsvTemplate();
      
      // Convert to CSV format
      const headers = Object.keys(template[0]);
      const csvContent = [
        headers.join(','),
        ...template.map(row => 
          headers.map(header => {
            const value = row[header];
            // Escape commas and quotes in CSV
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value || '';
          }).join(',')
        )
      ].join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="product-import-template.csv"');
      res.send(csvContent);
    } catch (error) {
      this.logger.error('Failed to generate CSV template:', error);
      throw new BadRequestException('Failed to generate template');
    }
  }

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadCsv(
    @UploadedFile() file: Express.Multer.File,
    @Request() req: any,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    if (file.mimetype !== 'text/csv' && !file.originalname.endsWith('.csv')) {
      throw new BadRequestException('File must be a CSV');
    }

    try {
      const brandId = req.user?._id || req.user?.userId;
      
      // Parse CSV file
      const products = await this.csvImportService.parseCsvFile(file.buffer);
      
      if (products.length === 0) {
        throw new BadRequestException('CSV file is empty or invalid');
      }

      // Validate data
      const validation = await this.csvImportService.validateCsvData(products, brandId);

      return {
        success: true,
        total_rows: products.length,
        valid_products: validation.valid.length,
        errors: validation.errors,
        preview: validation.valid.slice(0, 5), // First 5 valid products for preview
        all_valid_products: validation.valid, // All valid products for draft creation
        import_batch_id: randomUUID(),
      };
    } catch (error) {
      this.logger.error('CSV upload failed:', error);
      throw new BadRequestException(error.message || 'Failed to process CSV file');
    }
  }

  @Post('validate')
  async validateCsv(
    @Body() validationDto: CsvImportValidationDto,
    @Request() req: any,
  ) {
    try {
      const brandId = req.user?._id || req.user?.userId;
      const validation = await this.csvImportService.validateCsvData(
        validationDto.products,
        brandId
      );

      return {
        success: validation.errors.length === 0,
        valid_products: validation.valid.length,
        errors: validation.errors,
      };
    } catch (error) {
      this.logger.error('CSV validation failed:', error);
      throw new BadRequestException('Validation failed');
    }
  }

  @Post('create-draft')
  async createDraftProducts(
    @Body() validationDto: CsvImportValidationDto,
    @Request() req: any,
  ) {
    try {
      this.logger.log(`Create-draft request - User object:`, req.user);
      this.logger.log(`Create-draft request - Headers:`, req.headers.authorization ? 'Token present' : 'No token');

      const brandId = req.user?._id || req.user?.userId;

      if (!brandId) {
        throw new BadRequestException('User not authenticated or brand ID missing');
      }

      this.logger.log(`Creating draft products for brand ${brandId}, batch: ${validationDto.import_batch_id}`);
      this.logger.log(`Received ${validationDto.products?.length || 0} products`);

      // Validate first
      const validation = await this.csvImportService.validateCsvData(
        validationDto.products,
        brandId
      );

      this.logger.log(`Validation result: ${validation.valid.length} valid, ${validation.errors.length} errors`);

      if (validation.errors.length > 0) {
        this.logger.error('Validation errors:', validation.errors);
        throw new BadRequestException('Cannot create products with validation errors');
      }

      // Create draft products
      const products = await this.csvImportService.createDraftProducts(
        validation.valid,
        brandId,
        validationDto.import_batch_id
      );

      this.logger.log(`Created ${products.length} draft products for brand ${brandId}`);

      return {
        success: true,
        created: products.length,
        import_batch_id: validationDto.import_batch_id,
        products: products.map(p => ({
          id: p._id,
          title: p.title,
          price: p.variants[0]?.price,
          sku: p.variants[0]?.sku,
          status: p.import_status,
        })),
      };
    } catch (error) {
      this.logger.error('Failed to create draft products:', error);
      throw new BadRequestException(error.message || 'Failed to create products');
    }
  }

  @Get('batch/:batchId')
  async getImportBatch(
    @Param('batchId') batchId: string,
    @Request() req: any,
  ) {
    try {
      const brandId = req.user?._id || req.user?.userId;
      const products = await this.csvImportService.getImportBatchProducts(batchId, brandId);

      return {
        success: true,
        import_batch_id: batchId,
        total: products.length,
        products: products.map(p => ({
          id: p._id,
          title: p.title,
          description: p.description,
          price: p.variants[0]?.price,
          sku: p.variants[0]?.sku,
          category: p.category,
          tags: p.tags,
          images: p.images,
          status: p.import_status,
          verification_type: p.verification_type,
        })),
      };
    } catch (error) {
      this.logger.error('Failed to get import batch:', error);
      throw new BadRequestException('Failed to get import batch');
    }
  }

  @Put('images')
  async updateProductImages(
    @Body() imageDto: ProductImageUploadDto,
    @Request() req: any,
  ) {
    try {
      const brandId = req.user?._id || req.user?.userId;
      
      // Verify product belongs to brand
      const product = await this.csvImportService.updateProductImages(
        imageDto.product_id,
        imageDto.image_urls,
        imageDto.primary_image_index
      );

      if (product.brand_id.toString() !== brandId) {
        throw new BadRequestException('Product not found');
      }

      return {
        success: true,
        product_id: product._id,
        images_count: product.images.length,
        status: product.import_status,
      };
    } catch (error) {
      this.logger.error('Failed to update product images:', error);
      throw new BadRequestException(error.message || 'Failed to update images');
    }
  }

  @Put('images/bulk')
  async updateBulkImages(
    @Body() bulkImageDto: BulkImageUploadDto,
    @Request() req: any,
  ) {
    try {
      const brandId = req.user?._id || req.user?.userId;
      const results = [];

      for (const productImage of bulkImageDto.products) {
        try {
          // First verify the product belongs to the brand before updating
          const existingProduct = await this.csvImportService.getProductById(productImage.product_id);
          if (!existingProduct || existingProduct.brand_id.toString() !== brandId) {
            results.push({
              product_id: productImage.product_id,
              success: false,
              error: 'Product not found or access denied',
            });
            continue;
          }

          const product = await this.csvImportService.updateProductImages(
            productImage.product_id,
            productImage.image_urls,
            productImage.primary_image_index
          );

          results.push({
            product_id: product._id,
            success: true,
            images_count: product.images.length,
          });
        } catch (error) {
          this.logger.error(`Failed to update images for product ${productImage.product_id}:`, error);
          results.push({
            product_id: productImage.product_id,
            success: false,
            error: error.message,
          });
        }
      }

      return {
        success: true,
        results,
        updated: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
      };
    } catch (error) {
      this.logger.error('Failed to update bulk images:', error);
      throw new BadRequestException('Failed to update bulk images');
    }
  }

  @Put('verification')
  async updateVerificationTypes(
    @Body() verificationDto: ProductVerificationUpdateDto,
    @Request() req: any,
  ) {
    try {
      const brandId = req.user?._id || req.user?.userId;
      
      const updateResult = await this.csvImportService.updateProductVerificationTypes(
        verificationDto.product_ids,
        brandId,
        verificationDto.verification_type
      );

      return {
        success: true,
        updated: updateResult.modifiedCount,
        verification_type: verificationDto.verification_type,
      };
    } catch (error) {
      this.logger.error('Failed to update verification types:', error);
      throw new BadRequestException('Failed to update verification types');
    }
  }

  @Post('finalize')
  async finalizeImport(
    @Body() finalizeDto: CsvImportFinalizeDto,
    @Request() req: any,
  ) {
    try {
      this.logger.log(`Finalize request - User object:`, req.user);
      this.logger.log(`Finalize request - Headers:`, req.headers.authorization ? 'Token present' : 'No token');

      const brandId = req.user?._id || req.user?.userId;

      if (!brandId) {
        throw new BadRequestException('User not authenticated or brand ID missing');
      }

      const result = await this.csvImportService.finalizeImport(
        finalizeDto.import_batch_id,
        brandId,
        finalizeDto
      );

      this.logger.log(`Finalized import batch ${finalizeDto.import_batch_id}: ${result.imported} products`);

      return result;
    } catch (error) {
      this.logger.error('Failed to finalize import:', error);
      throw new BadRequestException(error.message || 'Failed to finalize import');
    }
  }

  @Delete('batch/:batchId')
  async deleteImportBatch(
    @Param('batchId') batchId: string,
    @Request() req: any,
  ) {
    try {
      const brandId = req.user?._id || req.user?.userId;
      await this.csvImportService.deleteImportBatch(batchId, brandId);

      return {
        success: true,
        message: 'Import batch deleted successfully',
      };
    } catch (error) {
      this.logger.error('Failed to delete import batch:', error);
      throw new BadRequestException('Failed to delete import batch');
    }
  }
}
