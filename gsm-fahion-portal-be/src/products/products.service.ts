import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Product } from './schemas/product.schema';
import { ShopifyService } from '../shopify/shopify.service';

@Injectable()
export class ProductsService {
  private readonly logger = new Logger(ProductsService.name);

  constructor(
    @InjectModel(Product.name)
    private productModel: Model<Product>,
    private shopifyService: ShopifyService,
  ) {}

  // Sync products from Shopify to database
  async syncProductsFromShopify(brandId: string): Promise<{ synced: number; errors: string[] }> {
    const errors: string[] = [];
    let syncedCount = 0;
    let pageInfo: string | undefined;
    
    try {
      this.logger.log(`Starting product sync for brand: ${brandId}`);
      
      do {
        // Fetch products from Shopify in batches
        const result = await this.shopifyService.getBrandProducts(brandId, 50, pageInfo);
        
        for (const shopifyProduct of result.products) {
          try {
            await this.syncSingleProduct(brandId, shopifyProduct);
            syncedCount++;
          } catch (error) {
            this.logger.error(`Failed to sync product ${shopifyProduct.id}: ${error.message}`);
            errors.push(`Product ${shopifyProduct.title}: ${error.message}`);
          }
        }
        
        pageInfo = result.pageInfo?.next;
      } while (pageInfo);
      
      // Update sync statistics
      await this.shopifyService.updateSyncStats(brandId, syncedCount, syncedCount);
      
      this.logger.log(`Completed product sync for brand ${brandId}: ${syncedCount} products synced`);
      
      return { synced: syncedCount, errors };
    } catch (error) {
      this.logger.error(`Product sync failed for brand ${brandId}: ${error.message}`);
      throw error;
    }
  }

  // Sync single product from Shopify
  private async syncSingleProduct(brandId: string, shopifyProduct: any): Promise<Product> {
    const productData = {
      brand_id: new Types.ObjectId(brandId),
      shopify_product_id: shopifyProduct.id.toString(),
      title: shopifyProduct.title,
      description: shopifyProduct.body_html || shopifyProduct.description || '',
      vendor: shopifyProduct.vendor || '',
      product_type: shopifyProduct.product_type || '',
      handle: shopifyProduct.handle,
      status: shopifyProduct.status,
      tags: shopifyProduct.tags ? shopifyProduct.tags.split(',').map((tag: string) => tag.trim()) : [],
      published_at: shopifyProduct.published_at ? new Date(shopifyProduct.published_at) : null,
      shopify_created_at: new Date(shopifyProduct.created_at),
      shopify_updated_at: new Date(shopifyProduct.updated_at),
      import_source: 'shopify',
      external_url: `https://${await this.getShopDomain(brandId)}/products/${shopifyProduct.handle}`,
      last_synced_at: new Date(),
      
      // Transform images
      images: shopifyProduct.images?.map((img: any) => ({
        shopify_image_id: img.id.toString(),
        src: img.src,
        alt: img.alt || '',
        position: img.position || 1,
        width: img.width || 0,
        height: img.height || 0,
      })) || [],
      
      // Transform variants
      variants: shopifyProduct.variants?.map((variant: any) => ({
        shopify_variant_id: variant.id.toString(),
        title: variant.title,
        price: parseFloat(variant.price) || 0,
        compare_at_price: variant.compare_at_price ? parseFloat(variant.compare_at_price) : null,
        sku: variant.sku || '',
        inventory_quantity: variant.inventory_quantity || 0,
        weight: variant.weight || 0,
        option1: variant.option1 || '',
        option2: variant.option2 || '',
        option3: variant.option3 || '',
        image_id: variant.image_id?.toString() || '',
        position: variant.position || 1,
      })) || [],
      
      // Transform options
      options: shopifyProduct.options?.map((option: any) => ({
        name: option.name,
        position: option.position || 1,
        values: option.values || [],
      })) || [],
    };

    // Upsert product (update if exists, create if not)
    const product = await this.productModel.findOneAndUpdate(
      {
        brand_id: new Types.ObjectId(brandId),
        shopify_product_id: shopifyProduct.id.toString()
      },
      productData,
      { 
        upsert: true, 
        new: true,
        runValidators: true
      }
    );

    return product;
  }

  // Get shop domain for external URL
  private async getShopDomain(brandId: string): Promise<string> {
    const connection = await this.shopifyService.getShopifyConnection(brandId);
    return connection?.shop_domain || 'shop.myshopify.com';
  }

  // Get products for a brand
  async getProductsByBrand(
    brandId: string,
    options: {
      page?: number;
      limit?: number;
      search?: string;
      status?: string;
      product_type?: string;
      category?: string;
      tags?: string[];
      import_source?: string;
      verification_type?: string;
      import_status?: string;
      sort?: string;
      order?: string;
    } = {}
  ) {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      product_type,
      category,
      tags,
      import_source,
      verification_type,
      import_status,
      sort = 'created_at',
      order = 'desc'
    } = options;

    const query: any = {
      brand_id: new Types.ObjectId(brandId),
      is_active: true
    };

    // Add filters
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { 'variants.sku': { $regex: search, $options: 'i' } },
        { vendor: { $regex: search, $options: 'i' } }
      ];
    }

    if (status && status !== 'all') {
      query.status = status;
    }

    if (product_type && product_type !== 'all') {
      query.product_type = product_type;
    }

    if (category && category !== 'all') {
      query.category = category;
    }

    if (import_source && import_source !== 'all') {
      query.import_source = import_source;
    }

    if (verification_type && verification_type !== 'all') {
      if (verification_type === 'none') {
        query.verification_type = { $exists: false };
      } else {
        query.verification_type = verification_type;
      }
    }

    if (import_status && import_status !== 'all') {
      query.import_status = import_status;
    }

    if (tags && tags.length > 0) {
      query.tags = { $in: tags };
    }

    const skip = (page - 1) * limit;
    const sortOrder = order === 'desc' ? -1 : 1;

    const [products, total] = await Promise.all([
      this.productModel
        .find(query)
        .sort({ [sort]: sortOrder })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.productModel.countDocuments(query)
    ]);

    return {
      products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }



  // Get single product
  async getProductById(brandId: string, productId: string): Promise<Product | null> {
    return await this.productModel.findOne({
      brand_id: new Types.ObjectId(brandId),
      $or: [
        { _id: productId },
        { shopify_product_id: productId }
      ],
      is_active: true
    });
  }

  // Update product
  async updateProduct(brandId: string, productId: string, updateData: Partial<Product>): Promise<Product | null> {
    return await this.productModel.findOneAndUpdate(
      {
        brand_id: new Types.ObjectId(brandId),
        _id: productId,
        is_active: true
      },
      updateData,
      { new: true, runValidators: true }
    );
  }

  // Delete product (soft delete)
  async deleteProduct(brandId: string, productId: string): Promise<boolean> {
    const result = await this.productModel.updateOne(
      {
        brand_id: new Types.ObjectId(brandId),
        _id: productId
      },
      { is_active: false }
    );

    return result.modifiedCount > 0;
  }

  // Fix existing products that should be visible but have is_active: false
  async fixInactiveProducts(): Promise<{ updated: number }> {
    const result = await this.productModel.updateMany(
      {
        is_active: false,
        status: { $in: ['draft', 'active'] }, // Only fix draft and active products
        import_source: { $exists: true } // Only fix imported products
      },
      {
        is_active: true
      }
    );

    this.logger.log(`Fixed ${result.modifiedCount} inactive products`);
    return { updated: result.modifiedCount };
  }

  // Get product statistics
  async getProductStats(brandId: string) {
    const stats = await this.productModel.aggregate([
      { $match: { brand_id: new Types.ObjectId(brandId), is_active: true } },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          active: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },
          draft: { $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] } },
          archived: { $sum: { $cond: [{ $eq: ['$status', 'archived'] }, 1, 0] } }
        }
      }
    ]);

    return stats[0] || { total: 0, active: 0, draft: 0, archived: 0 };
  }

  // Get unique product types for filtering
  async getProductTypes(brandId: string): Promise<string[]> {
    return await this.productModel.distinct('product_type', {
      brand_id: new Types.ObjectId(brandId),
      is_active: true,
      product_type: { $nin: [null, ''] }
    });
  }

  // Get unique tags for filtering
  async getProductTags(brandId: string): Promise<string[]> {
    return await this.productModel.distinct('tags', {
      brand_id: new Types.ObjectId(brandId),
      is_active: true
    });
  }
}
