import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ProductsService } from './products.service';
import { ProductsController } from './products.controller';
import { CsvImportController } from './controllers/csv-import.controller';
import { CsvImportService } from './services/csv-import.service';
import { Product, ProductSchema } from './schemas/product.schema';
import { ShopifyModule } from '../shopify/shopify.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Product.name, schema: ProductSchema },
    ]),
    ShopifyModule,
  ],
  controllers: [ProductsController, CsvImportController],
  providers: [ProductsService, CsvImportService],
  exports: [ProductsService, CsvImportService],
})
export class ProductsModule {}
