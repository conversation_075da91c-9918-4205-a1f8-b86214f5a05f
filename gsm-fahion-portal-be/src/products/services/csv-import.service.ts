import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Product } from '../schemas/product.schema';
import { CsvProductRowDto, CsvImportValidationDto, CsvImportFinalizeDto } from '../dto/csv-import.dto';
import { Readable } from 'stream';
import { randomUUID } from 'crypto';

@Injectable()
export class CsvImportService {
  private readonly logger = new Logger(CsvImportService.name);

  constructor(
    @InjectModel(Product.name) private productModel: Model<Product>,
  ) {}

  async parseCsvFile(fileBuffer: Buffer): Promise<CsvProductRowDto[]> {
    try {
      const csvText = fileBuffer.toString('utf-8');
      const lines = csvText.split('\n').filter(line => line.trim());

      if (lines.length === 0) {
        throw new BadRequestException('CSV file is empty');
      }

      // Parse header
      const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
      const results: CsvProductRowDto[] = [];

      // Parse data rows
      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
        const data: any = {};

        headers.forEach((header, index) => {
          data[header] = values[index] || '';
        });

        // Transform CSV row to DTO
        const row: CsvProductRowDto = {
          name: data.name || data.title,
          description: data.description,
          price: parseFloat(data.price) || 0,
          sku: data.sku,
          category: data.category,
          product_type: data.product_type || data.type,
          vendor: data.vendor || data.brand,
          tags: data.tags,
          inventory: parseInt(data.inventory) || 0,
          weight: data.weight ? parseFloat(data.weight) : undefined,
          weight_unit: data.weight_unit || 'kg',
          barcode: data.barcode,
          compare_at_price: data.compare_at_price ? parseFloat(data.compare_at_price) : undefined,
          option1_name: data.option1_name,
          option1_value: data.option1_value,
          option2_name: data.option2_name,
          option2_value: data.option2_value,
          option3_name: data.option3_name,
          option3_value: data.option3_value,
        };
        results.push(row);
      }

      return results;
    } catch (error) {
      this.logger.error('CSV parsing error:', error);
      throw new BadRequestException('Failed to parse CSV file');
    }
  }

  async validateCsvData(products: CsvProductRowDto[], brandId: string): Promise<{
    valid: CsvProductRowDto[];
    errors: Array<{ row: number; errors: string[] }>;
  }> {
    const valid: CsvProductRowDto[] = [];
    const errors: Array<{ row: number; errors: string[] }> = [];

    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      const rowErrors: string[] = [];

      // Validate required fields
      if (!product.name || product.name.trim() === '') {
        rowErrors.push('Product name is required');
      }

      if (!product.price || product.price <= 0) {
        rowErrors.push('Valid price is required');
      }

      // Check for duplicate SKUs within the CSV
      if (product.sku) {
        const duplicateInCsv = products.find((p, index) => 
          index !== i && p.sku === product.sku
        );
        if (duplicateInCsv) {
          rowErrors.push(`Duplicate SKU found in CSV: ${product.sku}`);
        }

        // Check for existing SKU in database
        const existingProduct = await this.productModel.findOne({
          brand_id: new Types.ObjectId(brandId),
          'variants.sku': product.sku,
        });

        if (existingProduct) {
          rowErrors.push(`SKU already exists: ${product.sku}`);
        }
      }

      if (rowErrors.length > 0) {
        errors.push({ row: i + 1, errors: rowErrors });
      } else {
        valid.push(product);
      }
    }

    return { valid, errors };
  }

  async createDraftProducts(
    products: CsvProductRowDto[],
    brandId: string,
    importBatchId?: string
  ): Promise<Product[]> {
    const batchId = importBatchId || randomUUID();
    const createdProducts: Product[] = [];

    for (const csvProduct of products) {
      try {
        // Create product options from CSV data
        const options = [];
        if (csvProduct.option1_name && csvProduct.option1_value) {
          options.push({
            name: csvProduct.option1_name,
            values: [csvProduct.option1_value],
            position: 1,
          });
        }
        if (csvProduct.option2_name && csvProduct.option2_value) {
          options.push({
            name: csvProduct.option2_name,
            values: [csvProduct.option2_value],
            position: 2,
          });
        }
        if (csvProduct.option3_name && csvProduct.option3_value) {
          options.push({
            name: csvProduct.option3_name,
            values: [csvProduct.option3_value],
            position: 3,
          });
        }

        // Create default variant
        const variant = {
          title: csvProduct.name,
          price: csvProduct.price,
          compare_at_price: csvProduct.compare_at_price,
          sku: csvProduct.sku,
          barcode: csvProduct.barcode,
          inventory_quantity: csvProduct.inventory || 0,
          weight: csvProduct.weight,
          weight_unit: csvProduct.weight_unit || 'kg',
          option1: csvProduct.option1_value,
          option2: csvProduct.option2_value,
          option3: csvProduct.option3_value,
          position: 1,
          available: true,
        };

        // Create product
        const product = new this.productModel({
          brand_id: new Types.ObjectId(brandId),
          title: csvProduct.name,
          description: csvProduct.description,
          vendor: csvProduct.vendor,
          product_type: csvProduct.product_type,
          category: csvProduct.category,
          handle: this.generateHandle(csvProduct.name),
          status: 'draft',
          tags: csvProduct.tags ? csvProduct.tags.split(',').map(tag => tag.trim()) : [],
          import_source: 'csv',
          import_batch_id: batchId,
          import_status: 'pending_images',
          csv_data: csvProduct,
          options,
          variants: [variant],
          images: [],
          campaigns_count: 0,
          is_active: true, // Set to true so draft products appear in products list
        });

        const savedProduct = await product.save();
        createdProducts.push(savedProduct);

        this.logger.log(`Created draft product: ${csvProduct.name} (ID: ${savedProduct._id}, Batch: ${savedProduct.import_batch_id})`);
      } catch (error) {
        this.logger.error(`Failed to create product ${csvProduct.name}:`, error);
        throw new BadRequestException(`Failed to create product: ${csvProduct.name}`);
      }
    }

    return createdProducts;
  }



  async updateProductImages(
    productId: string,
    imageUrls: string[],
    primaryImageIndex = 0
  ): Promise<Product> {
    this.logger.log(`Updating images for product ${productId}: ${imageUrls.length} images`);

    const product = await this.productModel.findById(productId);
    if (!product) {
      throw new BadRequestException('Product not found');
    }

    const images = imageUrls.map((url, index) => ({
      src: url,
      alt: `${product.title} - Image ${index + 1}`,
      position: index + 1,
      is_primary: index === primaryImageIndex,
    }));

    product.images = images as any;
    product.import_status = 'ready';

    const savedProduct = await product.save();
    this.logger.log(`Successfully updated ${images.length} images for product: ${product.title}`);

    return savedProduct;
  }

  async getProductById(productId: string): Promise<Product | null> {
    try {
      return await this.productModel.findById(productId);
    } catch (error) {
      this.logger.error(`Failed to get product by ID ${productId}:`, error);
      return null;
    }
  }

  async finalizeImport(
    importBatchId: string,
    brandId: string,
    options: CsvImportFinalizeDto
  ): Promise<{ success: boolean; imported: number; errors: string[] }> {
    this.logger.log(`Finalizing import for batch: ${importBatchId}, brand: ${brandId}`);

    // First, let's check what products exist for this batch regardless of brand
    const allBatchProducts = await this.productModel.find({
      import_batch_id: importBatchId,
    }).select('brand_id import_batch_id import_source status title');

    this.logger.log(`All products for batch ${importBatchId}:`, allBatchProducts.map(p => ({
      id: p._id,
      brand_id: p.brand_id.toString(),
      import_batch_id: p.import_batch_id,
      import_source: p.import_source,
      status: p.status,
      title: p.title
    })));

    const products = await this.productModel.find({
      brand_id: new Types.ObjectId(brandId),
      import_batch_id: importBatchId,
    });

    this.logger.log(`Found ${products.length} products for batch ${importBatchId} and brand ${brandId}`);

    // If no products found, let's check if there are any products for this brand
    if (products.length === 0) {
      const allBrandProducts = await this.productModel.find({
        brand_id: new Types.ObjectId(brandId),
      }).select('import_batch_id import_source status').limit(10);

      this.logger.log(`All brand products (last 10):`, allBrandProducts.map(p => ({
        id: p._id,
        import_batch_id: p.import_batch_id,
        import_source: p.import_source,
        status: p.status
      })));

      throw new BadRequestException('No products found for this import batch');
    }

    const errors: string[] = [];
    let imported = 0;

    for (const product of products) {
      try {
        // Warn if product has no images but don't block import
        if (product.images.length === 0) {
          this.logger.warn(`Product "${product.title}" has no images - importing anyway`);
          // Add a warning but don't add to errors array to prevent blocking
        }

        // Set verification type if provided
        if (options.default_verification_type) {
          product.verification_type = options.default_verification_type;
        }

        // Publish if requested
        if (options.publish_immediately) {
          product.status = 'active';
          product.is_active = true;
          product.published_at = new Date();
        } else {
          // Set to draft if not publishing immediately
          product.status = 'draft';
          product.is_active = true; // Keep active so it shows in products list as "pending"
        }

        product.import_status = 'published';
        product.import_source = 'csv';
        await product.save();
        imported++;

        this.logger.log(`Finalized product: ${product.title} (${product.images.length} images)`);
      } catch (error) {
        this.logger.error(`Failed to finalize product ${product.title}:`, error);
        errors.push(`Failed to finalize product "${product.title}": ${error.message}`);
      }
    }

    return {
      success: errors.length === 0,
      imported,
      errors,
    };
  }

  async getImportBatchProducts(importBatchId: string, brandId: string): Promise<Product[]> {
    return await this.productModel.find({
      brand_id: new Types.ObjectId(brandId),
      import_batch_id: importBatchId,
    }).sort({ createdAt: 1 });
  }

  async deleteImportBatch(importBatchId: string, brandId: string): Promise<void> {
    await this.productModel.deleteMany({
      brand_id: new Types.ObjectId(brandId),
      import_batch_id: importBatchId,
    });
  }

  async updateProductVerificationTypes(productIds: string[], brandId: string, verificationType: string): Promise<any> {
    return await this.productModel.updateMany(
      {
        _id: { $in: productIds },
        brand_id: brandId,
      },
      {
        verification_type: verificationType,
      }
    );
  }

  private generateHandle(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
      .substring(0, 100);
  }

  generateCsvTemplate(): any[] {
    return [
      {
        name: 'Sample T-Shirt',
        description: 'A comfortable cotton t-shirt perfect for everyday wear',
        price: 29.99,
        sku: 'TSHIRT-001',
        category: 'Clothing',
        product_type: 'T-Shirts',
        vendor: 'Your Brand',
        tags: 'casual, cotton, comfortable',
        inventory: 100,
        weight: 0.2,
        weight_unit: 'kg',
        barcode: '1234567890123',
        compare_at_price: 39.99,
        option1_name: 'Size',
        option1_value: 'Medium',
        option2_name: 'Color',
        option2_value: 'Blue',
        option3_name: '',
        option3_value: '',
      },
      {
        name: 'Sample Jeans',
        description: 'Classic denim jeans with a modern fit',
        price: 79.99,
        sku: 'JEANS-001',
        category: 'Clothing',
        product_type: 'Jeans',
        vendor: 'Your Brand',
        tags: 'denim, casual, classic',
        inventory: 50,
        weight: 0.8,
        weight_unit: 'kg',
        barcode: '1234567890124',
        compare_at_price: 99.99,
        option1_name: 'Size',
        option1_value: '32',
        option2_name: 'Color',
        option2_value: 'Dark Blue',
        option3_name: 'Fit',
        option3_value: 'Slim',
      },
    ];
  }
}
