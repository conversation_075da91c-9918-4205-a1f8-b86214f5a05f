import { IsOptional, IsString, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

class SocialHandlesDto {
  @IsOptional()
  @IsString()
  instagram?: string;

  @IsOptional()
  @IsString()
  tiktok?: string;

  @IsOptional()
  @IsString()
  twitter?: string;

  @IsOptional()
  @IsString()
  facebook?: string;
}

export class UpdateProfileDto {
  @IsOptional()
  @IsString()
  brand_name?: string;

  @IsOptional()
  @IsString()
  website?: string;

  @IsOptional()
  @IsString()
  bio?: string;

  @IsOptional()
  @IsString()
  logo_url?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => SocialHandlesDto)
  social_handles?: SocialHandlesDto;

  @IsOptional()
  @IsString()
  business_name?: string;

  @IsOptional()
  @IsString()
  tax_id?: string;

  @IsOptional()
  @IsString()
  business_registration_doc_url?: string;
}
