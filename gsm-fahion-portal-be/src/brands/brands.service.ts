import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { BrandProfile, BrandProfileDocument } from '../schemas/brand-profile.schema';
import { User, UserDocument } from '../schemas/user.schema';
import { UpdateProfileDto } from './dto/update-profile.dto';

@Injectable()
export class BrandsService {
  constructor(
    @InjectModel(BrandProfile.name) private brandProfileModel: Model<BrandProfileDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {}

  async getProfile(userId: string) {
    const profile = await this.brandProfileModel
      .findOne({ user_id: userId })
      .populate('user_id', 'email status email_verified')
      .exec();

    if (!profile) {
      throw new NotFoundException('Brand profile not found');
    }

    return profile;
  }

  async updateProfile(userId: string, updateProfileDto: UpdateProfileDto) {
    const profile = await this.brandProfileModel.findOne({ user_id: userId }).exec();
    
    if (!profile) {
      throw new NotFoundException('Brand profile not found');
    }

    // Update profile fields
    Object.assign(profile, updateProfileDto);

    // Check if profile is completed
    const isCompleted = this.checkProfileCompletion(profile);
    profile.profile_completed = isCompleted;

    await profile.save();

    return await this.getProfile(userId);
  }

  private checkProfileCompletion(profile: BrandProfileDocument): boolean {
    const requiredFields = [
      'brand_name',
      'website',
      'bio',
      'business_name',
      'tax_id',
    ];

    return requiredFields.every(field => profile[field] && profile[field].trim() !== '');
  }

  async getAllBrands() {
    const profiles = await this.brandProfileModel
      .find()
      .populate('user_id', 'email status email_verified createdAt')
      .sort({ createdAt: -1 })
      .exec();

    return profiles;
  }
}
