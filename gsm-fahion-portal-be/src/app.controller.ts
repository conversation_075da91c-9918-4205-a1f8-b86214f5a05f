import { Controller, Get } from '@nestjs/common';

@Controller()
export class AppController {
  @Get()
  getHello(): object {
    return {
      message: 'Fashion Brand Onboarding Platform API',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
      endpoints: {
        auth: '/api/auth',
        admin: '/api/admin',
        brands: '/api/brands',
        campaigns: '/api/campaigns',
        products: '/api/products',
        shopify: '/api/shopify',
        upload: '/api/upload',
        creators: '/api/creators',
        'creators-profile': '/api/creators/profile',
        'creators-avatar': '/api/creators/profile/avatar',
      },
    };
  }

  @Get('health')
  getHealth(): object {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }
}
