import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor(private configService: ConfigService) {
    this.transporter = nodemailer.createTransport({
      host: this.configService.get('SMTP_HOST'),
      port: parseInt(this.configService.get('SMTP_PORT')),
      secure: this.configService.get('SMTP_SECURE') === 'true',
      auth: {
        user: this.configService.get('SMTP_USER'),
        pass: this.configService.get('SMTP_PASS'),
      },
    });
  }

  async sendOtpEmail(email: string, otp: string): Promise<void> {
    try {
      const mailOptions = {
        from: this.configService.get('SMTP_USER'),
        to: email,
        subject: 'Email Verification - Fashion Brand Portal',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">Email Verification</h2>
            <p>Thank you for registering with Fashion Brand Portal!</p>
            <p>Your verification code is:</p>
            <div style="background: #f5f5f5; padding: 20px; text-align: center; margin: 20px 0;">
              <h1 style="color: #6366f1; font-size: 32px; margin: 0;">${otp}</h1>
            </div>
            <p>This code will expire in 10 minutes.</p>
            <p>If you didn't request this verification, please ignore this email.</p>
          </div>
        `,
      };

      await this.transporter.sendMail(mailOptions);
      this.logger.log(`OTP email sent to ${email}`);
    } catch (error) {
      this.logger.error(`Failed to send OTP email to ${email}:`, error);
      throw new Error('Failed to send verification email');
    }
  }

  async sendApprovalEmail(email: string, brandName: string): Promise<void> {
    try {
      const mailOptions = {
        from: this.configService.get('SMTP_USER'),
        to: email,
        subject: 'Brand Application Approved - Fashion Brand Portal',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #10b981;">Congratulations! Your Brand Application is Approved</h2>
            <p>Dear ${brandName},</p>
            <p>We're excited to inform you that your brand application has been approved!</p>
            <p>You can now access all features of the Fashion Brand Portal and start creating campaigns.</p>
            <p>Welcome to our platform!</p>
            <p>Best regards,<br>Fashion Brand Portal Team</p>
          </div>
        `,
      };

      await this.transporter.sendMail(mailOptions);
      this.logger.log(`Approval email sent to ${email}`);
    } catch (error) {
      this.logger.error(`Failed to send approval email to ${email}:`, error);
      throw new Error('Failed to send approval email');
    }
  }

  async sendRejectionEmail(email: string, brandName: string, reason?: string): Promise<void> {
    try {
      const mailOptions = {
        from: this.configService.get('SMTP_USER'),
        to: email,
        subject: 'Brand Application Update - Fashion Brand Portal',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #ef4444;">Brand Application Update</h2>
            <p>Dear ${brandName},</p>
            <p>Thank you for your interest in joining Fashion Brand Portal.</p>
            <p>After reviewing your application, we need additional information or clarification.</p>
            ${reason ? `<p><strong>Reason:</strong> ${reason}</p>` : ''}
            <p>Please feel free to reapply or contact our support team for assistance.</p>
            <p>Best regards,<br>Fashion Brand Portal Team</p>
          </div>
        `,
      };

      await this.transporter.sendMail(mailOptions);
      this.logger.log(`Rejection email sent to ${email}`);
    } catch (error) {
      this.logger.error(`Failed to send rejection email to ${email}:`, error);
      throw new Error('Failed to send rejection email');
    }
  }
}
