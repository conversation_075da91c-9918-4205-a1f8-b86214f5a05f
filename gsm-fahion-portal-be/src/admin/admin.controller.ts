import {
  Controller,
  Get,
  Put,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../schemas/user.schema';
import { AdminService } from './admin.service';
import { CampaignsService } from '../campaigns/campaigns.service';
import { CampaignStatus } from '../campaigns/schemas/campaign.schema';

@Controller('admin')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
export class AdminController {
  constructor(
    private readonly adminService: AdminService,
    private readonly campaignsService: CampaignsService,
  ) {}

  @Get('brands')
  async getAllBrands() {
    return this.adminService.getAllBrands();
  }

  @Get('brands/:id')
  async getBrandById(@Param('id') id: string) {
    return this.adminService.getBrandById(id);
  }

  @Put('brands/:id/approve')
  async approveBrand(@Param('id') id: string) {
    return this.adminService.approveBrand(id);
  }

  @Put('brands/:id/reject')
  async rejectBrand(
    @Param('id') id: string,
    @Body('reason') reason?: string,
  ) {
    return this.adminService.rejectBrand(id, reason);
  }

  // Campaign management endpoints
  @Get('campaigns')
  async getAllCampaigns(
    @Query('page') page = 1,
    @Query('limit') limit = 20,
    @Query('status') status?: CampaignStatus,
    @Query('search') search?: string
  ) {
    const options = {
      page: parseInt(page.toString()),
      limit: parseInt(limit.toString()),
      status,
      search
    };

    return this.campaignsService.getAllCampaigns(options);
  }

  @Get('campaigns/:id')
  async getCampaignById(@Param('id') id: string) {
    return this.campaignsService.getCampaignByIdForAdmin(id);
  }

  @Put('campaigns/:id/approve')
  async approveCampaign(
    @Param('id') id: string,
    @Body('notes') notes?: string,
  ) {
    return this.campaignsService.approveCampaign(id, notes);
  }

  @Put('campaigns/:id/reject')
  async rejectCampaign(
    @Param('id') id: string,
    @Body('notes') notes?: string,
  ) {
    return this.campaignsService.rejectCampaign(id, notes);
  }
}
