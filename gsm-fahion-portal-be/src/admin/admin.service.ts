import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { User, UserDocument, UserStatus } from '../schemas/user.schema';
import { BrandProfile, BrandProfileDocument } from '../schemas/brand-profile.schema';
import { EmailService } from '../email/email.service';

@Injectable()
export class AdminService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(BrandProfile.name) private brandProfileModel: Model<BrandProfileDocument>,
    private emailService: EmailService,
  ) {}

  async getAllBrands() {
    const brands = await this.userModel
      .find({ role: 'brand' })
      .select('-password -otp -otp_expires -reset_token -reset_token_expires')
      .sort({ createdAt: -1 })
      .exec();

    // Get brand profiles for each user
    const brandsWithProfiles = await Promise.all(
      brands.map(async (brand) => {
        const profile = await this.brandProfileModel
          .findOne({ user_id: brand._id })
          .exec();
        
        return {
          ...brand.toObject(),
          profile,
        };
      }),
    );

    return brandsWithProfiles;
  }

  async approveBrand(brandId: string) {
    const user = await this.userModel.findById(brandId).exec();
    if (!user) {
      throw new NotFoundException('Brand not found');
    }

    if (user.status === UserStatus.APPROVED) {
      return { message: 'Brand is already approved' };
    }

    user.status = UserStatus.APPROVED;
    await user.save();

    // Get brand profile for email
    const profile = await this.brandProfileModel.findOne({ user_id: brandId }).exec();
    const brandName = profile?.brand_name || user.email;

    // Send approval email
    await this.emailService.sendApprovalEmail(user.email, brandName);

    return {
      message: 'Brand approved successfully',
      brand: {
        id: user._id,
        email: user.email,
        status: user.status,
      },
    };
  }

  async rejectBrand(brandId: string, reason?: string) {
    const user = await this.userModel.findById(brandId).exec();
    if (!user) {
      throw new NotFoundException('Brand not found');
    }

    if (user.status === UserStatus.REJECTED) {
      return { message: 'Brand is already rejected' };
    }

    user.status = UserStatus.REJECTED;
    await user.save();

    // Get brand profile for email
    const profile = await this.brandProfileModel.findOne({ user_id: brandId }).exec();
    const brandName = profile?.brand_name || user.email;

    // Send rejection email
    await this.emailService.sendRejectionEmail(user.email, brandName, reason);

    return {
      message: 'Brand rejected successfully',
      brand: {
        id: user._id,
        email: user.email,
        status: user.status,
      },
    };
  }

  async getBrandById(brandId: string) {
    const user = await this.userModel
      .findById(brandId)
      .select('-password -otp -otp_expires -reset_token -reset_token_expires')
      .exec();

    if (!user) {
      throw new NotFoundException('Brand not found');
    }

    const profile = await this.brandProfileModel
      .findOne({ user_id: brandId })
      .exec();

    return {
      ...user.toObject(),
      profile,
    };
  }
}
