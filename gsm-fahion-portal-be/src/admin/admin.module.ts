import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { AdminService } from './admin.service';
import { AdminController } from './admin.controller';
import { User, UserSchema } from '../schemas/user.schema';
import { BrandProfile, BrandProfileSchema } from '../schemas/brand-profile.schema';
import { EmailModule } from '../email/email.module';
import { CampaignsModule } from '../campaigns/campaigns.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: BrandProfile.name, schema: BrandProfileSchema },
    ]),
    EmailModule,
    CampaignsModule,
  ],
  controllers: [AdminController],
  providers: [AdminService],
})
export class AdminModule {}
