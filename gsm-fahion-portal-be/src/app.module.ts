import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { ThrottlerModule } from '@nestjs/throttler';

import { AppController } from './app.controller';
import { ShopifyAppController } from './shopify-app.controller';
import { DatabaseModule } from './database/database.module';
import { ShopifyConnection, ShopifyConnectionSchema } from './shopify/schemas/shopify-connection.schema';
import { AuthModule } from './auth/auth.module';
import { EmailModule } from './email/email.module';
import { UploadModule } from './upload/upload.module';
import { BrandsModule } from './brands/brands.module';
import { AdminModule } from './admin/admin.module';
import { ShopifyModule } from './shopify/shopify.module';
import { ProductsModule } from './products/products.module';
import { CampaignsModule } from './campaigns/campaigns.module';
import { CreatorsModule } from './creators/creators.module';

@Module({
  controllers: [AppController, ShopifyAppController],
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // Rate limiting
    ThrottlerModule.forRoot([
      {
        ttl: 60000, // 1 minute
        limit: 100, // 100 requests per minute
      },
    ]),

    // Database
    DatabaseModule,
    MongooseModule.forFeature([
      { name: ShopifyConnection.name, schema: ShopifyConnectionSchema },
    ]),

    // Feature modules
    AuthModule,
    EmailModule,
    UploadModule,
    BrandsModule,
    AdminModule,
    ShopifyModule,
    ProductsModule,
    CampaignsModule,
    CreatorsModule,
  ],
})
export class AppModule {}
