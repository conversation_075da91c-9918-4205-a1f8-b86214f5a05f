{"name": "fashion-crypto-portal-be", "version": "1.0.0", "main": "dist/main.js", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node dist/main", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "heroku-postbuild": "npm run build", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "keywords": ["<PERSON><PERSON><PERSON>", "fashion", "crypto", "onboarding"], "author": "", "license": "ISC", "description": "Fashion Brand Onboarding Platform Backend", "dependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/serve-static": "^5.0.3", "@nestjs/throttler": "^6.4.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cloudinary": "^2.7.0", "expo-linear-gradient": "^14.1.5", "mongoose": "^8.16.2", "multer": "^2.0.1", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "typescript": "^5.8.3", "expo-image-picker": "~16.1.4"}, "devDependencies": {"@nestjs/testing": "^11.1.3", "@types/bcryptjs": "^2.4.6", "@types/express": "^5.0.3", "@types/multer": "^2.0.0", "@types/node": "^24.0.10", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "nodemon": "^3.1.10", "ts-node": "^10.9.2"}}