# Fashion Brand Onboarding Platform - Backend

A complete NestJS backend application for fashion brand onboarding with JWT authentication, email verification, file uploads, and admin management.

## Features

- 🔐 JWT Authentication with email verification
- 📧 Email service with OTP verification
- 📁 File upload with Cloudinary integration
- 👥 Role-based access control (Brand/Admin)
- 🏢 Brand profile management
- 👨‍💼 Admin approval/rejection system
- 🌐 CORS enabled for any origin
- 📊 MongoDB with Mongoose ODM

## Environment Variables

Create a `.env` file in the root directory:

```env
# Database
MONGODB_URI=mongodb+srv://zohaib:<EMAIL>/gsm-portal

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=465
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=marv stlx nayp drwl

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=dzuxnbt8u
CLOUDINARY_API_KEY=229778449983798
CLOUDINARY_API_SECRET=J3kXcIIz_hNTYgzkzRfGGau2loM

# Application Configuration
PORT=3000
NODE_ENV=development

# Admin Credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=demo123
```

## Installation

```bash
# Install dependencies
npm install

# Start development server
npm run start:dev

# Build for production
npm run build

# Start production server
npm run start:prod
```

## API Endpoints

### Authentication

#### Brand Registration
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Email Verification
```http
POST /api/auth/verify-otp
Content-Type: application/json

{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

#### Brand Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Admin Login
```http
POST /api/admin/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "demo123"
}
```

### Brand Management

#### Get Brand Profile
```http
GET /api/brands/profile
Authorization: Bearer <jwt_token>
```

#### Update Brand Profile
```http
POST /api/brands/update-profile
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "brand_name": "Fashion Brand",
  "website": "https://fashionbrand.com",
  "bio": "Modern fashion brand...",
  "social_handles": {
    "instagram": "@fashionbrand",
    "tiktok": "@fashionbrand"
  },
  "business_name": "Fashion Brand LLC",
  "tax_id": "*********"
}
```

### File Upload

#### Upload Logo
```http
POST /api/upload/logo
Authorization: Bearer <jwt_token>
Content-Type: multipart/form-data

file: <image_file>
```

#### Upload Document
```http
POST /api/upload/document
Authorization: Bearer <jwt_token>
Content-Type: multipart/form-data

file: <document_file>
```

### Admin Management

#### Get All Brands
```http
GET /api/admin/brands
Authorization: Bearer <admin_jwt_token>
```

#### Approve Brand
```http
PUT /api/admin/brands/:id/approve
Authorization: Bearer <admin_jwt_token>
```

#### Reject Brand
```http
PUT /api/admin/brands/:id/reject
Authorization: Bearer <admin_jwt_token>
Content-Type: application/json

{
  "reason": "Additional documentation required"
}
```

## User Flow

1. **Brand Registration**: Brand registers with email and password
2. **Email Verification**: Brand receives OTP via email and verifies
3. **Login**: Brand logs in and receives JWT token
4. **Profile Setup**: Brand completes profile with business information
5. **Admin Review**: Admin reviews and approves/rejects the application
6. **Email Notification**: Brand receives approval/rejection email
7. **Access**: Approved brands can access full platform features

## Database Schema

### User Model
- email (unique)
- password (hashed)
- role (brand/admin)
- status (pending/approved/rejected)
- email_verified (boolean)
- otp & otp_expires (for verification)

### Brand Profile Model
- user_id (reference to User)
- brand_name, website, bio
- logo_url
- social_handles (instagram, tiktok, twitter, facebook)
- business_name, tax_id
- business_registration_doc_url
- profile_completed (boolean)

## Technologies Used

- **NestJS** - Node.js framework
- **MongoDB** - Database
- **Mongoose** - ODM
- **JWT** - Authentication
- **Nodemailer** - Email service
- **Cloudinary** - File storage
- **bcryptjs** - Password hashing
- **class-validator** - Input validation

## Development

The application runs on `http://localhost:3000/api` in development mode with hot reload enabled.

All API endpoints are prefixed with `/api` and CORS is configured to allow any origin for multi-domain deployments.

### Quick Test

You can test the API endpoints using the provided test script:

```bash
node test-api.js
```

Or manually test the health endpoint:

```bash
curl http://localhost:3000/api/health
```

### Project Structure

```
src/
├── auth/                 # Authentication module
│   ├── dto/             # Data transfer objects
│   ├── guards/          # Auth guards (JWT, Roles)
│   ├── decorators/      # Custom decorators
│   └── jwt.strategy.ts  # JWT strategy
├── admin/               # Admin management module
├── brands/              # Brand profile module
├── email/               # Email service module
├── upload/              # File upload module
├── schemas/             # MongoDB schemas
├── common/              # Shared utilities
└── database/            # Database configuration
```

## Status

✅ **COMPLETED** - The backend is fully functional and ready for production use with all required features implemented:

- Authentication with email verification
- Brand profile management
- Admin approval system
- File upload with Cloudinary
- Email notifications
- Role-based access control
- CORS configuration for multi-domain deployment
