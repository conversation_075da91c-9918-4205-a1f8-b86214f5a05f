/**
 * PRODUCTION AUTHENTICATION CONTEXT
 *
 * This authentication system connects to the real backend API:
 * - Real user registration and login
 * - Email OTP verification
 * - Password reset functionality
 * - JWT token management
 * - Creator profile integration
 *
 * Integrated with the NestJS backend for full functionality.
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ApiService from '../services/api';
import config from '../config/environment';

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);

  // Debug state changes
  useEffect(() => {
    console.log('🔐 Auth state changed:', { isLoading, isAuthenticated, user: user?.email });
  }, [isLoading, isAuthenticated, user]);

  // Check if user is logged in on app start
  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      const { token, user: userData, profile: profileData } = await ApiService.getStoredUserData();

      if (token && userData) {
        setIsAuthenticated(true);
        setUser(userData);
        setProfile(profileData);
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email, password, rememberMe = false) => {
    try {
      setIsLoading(true);

      const response = await ApiService.login(email, password);

      if (response.access_token && response.user) {
        // Store auth data
        await ApiService.storeAuthData(response.access_token, response.user);

        setIsAuthenticated(true);
        setUser(response.user);

        // Try to fetch profile data
        try {
          const profileResponse = await ApiService.getProfile();
          if (profileResponse.profile) {
            setProfile(profileResponse.profile);
            await AsyncStorage.setItem(config.APP_CONFIG.PROFILE_STORAGE_KEY, JSON.stringify(profileResponse.profile));
          }
        } catch (profileError) {
          console.log('Could not fetch profile:', profileError.message);
        }

        return { success: true };
      } else {
        return { success: false, error: 'Invalid response from server' };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.message || 'Login failed' };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);

      // Clear stored data
      await ApiService.clearAuthData();

      setIsAuthenticated(false);
      setUser(null);
      setProfile(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (email, password, confirmPassword, displayName) => {
    try {
      console.log('🔄 AuthContext signup called with:', { email, displayName });
      // Don't set isLoading for signup - it doesn't change auth state

      if (password !== confirmPassword) {
        console.log('❌ Password mismatch');
        return { success: false, error: 'Passwords do not match' };
      }

      console.log('📡 Calling API register...');
      const response = await ApiService.register(email, password, displayName);
      console.log('✅ API register response:', response);

      return {
        success: true,
        message: response.message || 'Account created successfully! Please check your email for verification.',
        email: response.email
      };
    } catch (error) {
      console.error('❌ Signup error:', error);
      return { success: false, error: error.message || 'Registration failed' };
    }
    // No finally block - signup doesn't change loading state
  };

  const forgotPassword = async (email) => {
    try {
      setIsLoading(true);

      const response = await ApiService.forgotPassword(email);

      return {
        success: true,
        message: response.message || 'Password reset code sent to your email.'
      };
    } catch (error) {
      console.error('Forgot password error:', error);
      return { success: false, error: error.message || 'Failed to send reset code' };
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (email, code, newPassword) => {
    try {
      setIsLoading(true);

      const response = await ApiService.resetPassword(email, code, newPassword);

      return {
        success: true,
        message: response.message || 'Password reset successfully.'
      };
    } catch (error) {
      console.error('Reset password error:', error);
      return { success: false, error: error.message || 'Failed to reset password' };
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOtp = async (email, otp) => {
    try {
      setIsLoading(true);

      const response = await ApiService.verifyOtp(email, otp);

      if (response.access_token && response.user) {
        // Store auth data
        await ApiService.storeAuthData(response.access_token, response.user);

        setIsAuthenticated(true);
        setUser(response.user);

        // Try to fetch profile data
        try {
          const profileResponse = await ApiService.getProfile();
          if (profileResponse.profile) {
            setProfile(profileResponse.profile);
            await AsyncStorage.setItem(config.APP_CONFIG.PROFILE_STORAGE_KEY, JSON.stringify(profileResponse.profile));
          }
        } catch (profileError) {
          console.log('Could not fetch profile:', profileError.message);
        }

        return { success: true, message: response.message };
      } else {
        return { success: false, error: 'Invalid response from server' };
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      return { success: false, error: error.message || 'OTP verification failed' };
    } finally {
      setIsLoading(false);
    }
  };

  const resendOtp = async (email) => {
    try {
      setIsLoading(true);

      const response = await ApiService.resendOtp(email);

      return {
        success: true,
        message: response.message || 'OTP sent successfully'
      };
    } catch (error) {
      console.error('Resend OTP error:', error);
      return { success: false, error: error.message || 'Failed to resend OTP' };
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = (newProfileData) => {
    setProfile(newProfileData);
  };

  const refreshProfile = async () => {
    try {
      const profileResponse = await ApiService.getProfile();
      if (profileResponse.profile) {
        setProfile(profileResponse.profile);
        await AsyncStorage.setItem(config.APP_CONFIG.PROFILE_STORAGE_KEY, JSON.stringify(profileResponse.profile));
      }
    } catch (error) {
      console.error('Failed to refresh profile:', error);
    }
  };

  const value = {
    isLoading,
    isAuthenticated,
    user,
    profile,
    login,
    logout,
    signup,
    forgotPassword,
    resetPassword,
    verifyOtp,
    resendOtp,
    updateProfile,
    refreshProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
