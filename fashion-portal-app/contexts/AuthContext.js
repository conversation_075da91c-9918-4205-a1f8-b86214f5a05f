/**
 * DEMO MODE AUTHENTICATION CONTEXT
 *
 * This authentication system is configured for testing/demo purposes:
 * - All login attempts succeed regardless of credentials
 * - All signup attempts succeed regardless of validation
 * - All password reset flows succeed
 * - No real validation is performed
 * - Visual flows remain intact for UI/UX testing
 *
 * Perfect for testing the main app functionality without authentication barriers.
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);

  // Check if user is logged in on app start
  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      const token = await AsyncStorage.getItem('userToken');
      const userData = await AsyncStorage.getItem('userData');
      
      if (token && userData) {
        setIsAuthenticated(true);
        setUser(JSON.parse(userData));
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email, password, rememberMe = false) => {
    try {
      setIsLoading(true);

      // DEMO MODE: Always succeeds instantly for testing
      // Show brief loading for visual feedback
      await new Promise(resolve => setTimeout(resolve, 800));

      // Mock user data - always the same for testing
      const userData = {
        id: 1,
        name: 'Demo User',
        email: email || '<EMAIL>',
        rewards: 5847,
        submissions: 20,
      };

      const token = 'demo_token_' + Date.now();

      // Store auth data for persistence
      await AsyncStorage.setItem('userToken', token);
      await AsyncStorage.setItem('userData', JSON.stringify(userData));

      if (rememberMe) {
        await AsyncStorage.setItem('rememberMe', 'true');
      }

      setIsAuthenticated(true);
      setUser(userData);

      // ALWAYS return success in demo mode
      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      // Even if there's an error, still succeed in demo mode
      return { success: true };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      
      // Clear stored data
      await AsyncStorage.multiRemove(['userToken', 'userData', 'rememberMe']);
      
      setIsAuthenticated(false);
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (userData) => {
    try {
      setIsLoading(true);

      // DEMO MODE: Always succeeds instantly for testing
      // Show brief loading for visual feedback
      await new Promise(resolve => setTimeout(resolve, 800));

      // Mock successful signup - always works
      const newUser = {
        id: Date.now(),
        name: userData?.name || 'Demo User',
        email: userData?.email || '<EMAIL>',
        rewards: 100, // Give new users some starting tokens
        submissions: 0,
      };

      const token = 'demo_signup_token_' + Date.now();

      // Store auth data
      await AsyncStorage.setItem('userToken', token);
      await AsyncStorage.setItem('userData', JSON.stringify(newUser));

      setIsAuthenticated(true);
      setUser(newUser);

      // ALWAYS return success in demo mode
      return { success: true };
    } catch (error) {
      console.error('Signup error:', error);
      // Even if there's an error, still succeed in demo mode
      return { success: true };
    } finally {
      setIsLoading(false);
    }
  };

  const forgotPassword = async (email) => {
    try {
      setIsLoading(true);

      // DEMO MODE: Always succeeds for testing
      await new Promise(resolve => setTimeout(resolve, 800));

      // Always return success in demo mode
      return { success: true, message: 'Password reset code sent to your email.' };
    } catch (error) {
      console.error('Forgot password error:', error);
      // Even if there's an error, still succeed in demo mode
      return { success: true, message: 'Password reset code sent to your email.' };
    } finally {
      setIsLoading(false);
    }
  };

  const resetPassword = async (code, newPassword) => {
    try {
      setIsLoading(true);

      // DEMO MODE: Always succeeds for testing
      await new Promise(resolve => setTimeout(resolve, 800));

      // Always return success in demo mode
      return { success: true, message: 'Password reset successfully.' };
    } catch (error) {
      console.error('Reset password error:', error);
      // Even if there's an error, still succeed in demo mode
      return { success: true, message: 'Password reset successfully.' };
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    isLoading,
    isAuthenticated,
    user,
    login,
    logout,
    signup,
    forgotPassword,
    resetPassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
