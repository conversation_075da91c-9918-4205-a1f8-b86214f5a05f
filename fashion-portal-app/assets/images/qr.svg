<svg width="51" height="50" viewBox="0 0 51 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="3.5" y="3" width="44" height="44"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(0.5px);clip-path:url(#bgblur_0_157_5792_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_157_5792)" data-figma-bg-blur-radius="1">
<circle cx="25.5" cy="25" r="20" fill="white" fill-opacity="0.6"/>
<circle cx="25.5" cy="25" r="20.5" stroke="white" stroke-opacity="0.5"/>
</g>
<g opacity="0.8">
<mask id="mask0_157_5792" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="5" y="5" width="41" height="40">
<circle cx="25.5" cy="25" r="20" fill="white" fill-opacity="0.3"/>
<circle cx="25.5" cy="25" r="19.85" stroke="white" stroke-opacity="0.5" stroke-width="0.3"/>
</mask>
<g mask="url(#mask0_157_5792)">
<g filter="url(#filter1_f_157_5792)">
<circle cx="15.3" cy="5.40002" r="20" fill="#FF7700"/>
</g>
<g filter="url(#filter2_f_157_5792)">
<circle cx="36.3" cy="42.4" r="20" fill="#8800FF"/>
</g>
</g>
</g>
<g filter="url(#filter3_f_157_5792)">
<circle cx="25.5" cy="25" r="10" fill="url(#paint0_linear_157_5792)"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.5 16C15.5 15.7348 15.6054 15.4804 15.7929 15.2929C15.9804 15.1054 16.2348 15 16.5 15H23.5C23.7652 15 24.0196 15.1054 24.2071 15.2929C24.3946 15.4804 24.5 15.7348 24.5 16V23C24.5 23.2652 24.3946 23.5196 24.2071 23.7071C24.0196 23.8946 23.7652 24 23.5 24H16.5C16.2348 24 15.9804 23.8946 15.7929 23.7071C15.6054 23.5196 15.5 23.2652 15.5 23V16ZM18.5 19C18.5 18.7348 18.6054 18.4804 18.7929 18.2929C18.9804 18.1054 19.2348 18 19.5 18H20.5C20.7652 18 21.0196 18.1054 21.2071 18.2929C21.3946 18.4804 21.5 18.7348 21.5 19V20C21.5 20.2652 21.3946 20.5196 21.2071 20.7071C21.0196 20.8946 20.7652 21 20.5 21H19.5C19.2348 21 18.9804 20.8946 18.7929 20.7071C18.6054 20.5196 18.5 20.2652 18.5 20V19ZM15.5 27C15.5 26.7348 15.6054 26.4804 15.7929 26.2929C15.9804 26.1054 16.2348 26 16.5 26H23.5C23.7652 26 24.0196 26.1054 24.2071 26.2929C24.3946 26.4804 24.5 26.7348 24.5 27V34C24.5 34.2652 24.3946 34.5196 24.2071 34.7071C24.0196 34.8946 23.7652 35 23.5 35H16.5C16.2348 35 15.9804 34.8946 15.7929 34.7071C15.6054 34.5196 15.5 34.2652 15.5 34V27ZM19.5 29C19.2348 29 18.9804 29.1054 18.7929 29.2929C18.6054 29.4804 18.5 29.7348 18.5 30V31C18.5 31.2652 18.6054 31.5196 18.7929 31.7071C18.9804 31.8946 19.2348 32 19.5 32H20.5C20.7652 32 21.0196 31.8946 21.2071 31.7071C21.3946 31.5196 21.5 31.2652 21.5 31V30C21.5 29.7348 21.3946 29.4804 21.2071 29.2929C21.0196 29.1054 20.7652 29 20.5 29H19.5ZM27.5 15C27.2348 15 26.9804 15.1054 26.7929 15.2929C26.6054 15.4804 26.5 15.7348 26.5 16V23C26.5 23.2652 26.6054 23.5196 26.7929 23.7071C26.9804 23.8946 27.2348 24 27.5 24H34.5C34.7652 24 35.0196 23.8946 35.2071 23.7071C35.3946 23.5196 35.5 23.2652 35.5 23V16C35.5 15.7348 35.3946 15.4804 35.2071 15.2929C35.0196 15.1054 34.7652 15 34.5 15H27.5ZM29.5 19C29.5 18.7348 29.6054 18.4804 29.7929 18.2929C29.9804 18.1054 30.2348 18 30.5 18H31.5C31.7652 18 32.0196 18.1054 32.2071 18.2929C32.3946 18.4804 32.5 18.7348 32.5 19V20C32.5 20.2652 32.3946 20.5196 32.2071 20.7071C32.0196 20.8946 31.7652 21 31.5 21H30.5C30.2348 21 29.9804 20.8946 29.7929 20.7071C29.6054 20.5196 29.5 20.2652 29.5 20V19Z" fill="white"/>
<path d="M27.5 26C27.2348 26 26.9804 26.1054 26.7929 26.2929C26.6054 26.4804 26.5 26.7348 26.5 27V28C26.5 28.2652 26.6054 28.5196 26.7929 28.7071C26.9804 28.8946 27.2348 29 27.5 29H28.5V31C28.5 31.2652 28.6054 31.5196 28.7929 31.7071C28.9804 31.8946 29.2348 32 29.5 32H30.5C30.7652 32 31.0196 31.8946 31.2071 31.7071C31.3946 31.5196 31.5 31.2652 31.5 31V29C31.5 28.7348 31.3946 28.4804 31.2071 28.2929C31.0196 28.1054 30.7652 28 30.5 28H29.5V27C29.5 26.7348 29.3946 26.4804 29.2071 26.2929C29.0196 26.1054 28.7652 26 28.5 26H27.5ZM27.5 32C27.2348 32 26.9804 32.1054 26.7929 32.2929C26.6054 32.4804 26.5 32.7348 26.5 33V34C26.5 34.2652 26.6054 34.5196 26.7929 34.7071C26.9804 34.8946 27.2348 35 27.5 35H28.5C28.7652 35 29.0196 34.8946 29.2071 34.7071C29.3946 34.5196 29.5 34.2652 29.5 34V33C29.5 32.7348 29.3946 32.4804 29.2071 32.2929C29.0196 32.1054 28.7652 32 28.5 32H27.5ZM31.5 27C31.5 26.7348 31.6054 26.4804 31.7929 26.2929C31.9804 26.1054 32.2348 26 32.5 26H33.5C33.7652 26 34.0196 26.1054 34.2071 26.2929C34.3946 26.4804 34.5 26.7348 34.5 27V28C34.5 28.2652 34.3946 28.5196 34.2071 28.7071C34.0196 28.8946 33.7652 29 33.5 29H32.5C32.2348 29 31.9804 28.8946 31.7929 28.7071C31.6054 28.5196 31.5 28.2652 31.5 28V27ZM32.5 32C32.2348 32 31.9804 32.1054 31.7929 32.2929C31.6054 32.4804 31.5 32.7348 31.5 33V34C31.5 34.2652 31.6054 34.5196 31.7929 34.7071C31.9804 34.8946 32.2348 35 32.5 35H33.5C33.7652 35 34.0196 34.8946 34.2071 34.7071C34.3946 34.5196 34.5 34.2652 34.5 34V33C34.5 32.7348 34.3946 32.4804 34.2071 32.2929C34.0196 32.1054 33.7652 32 33.5 32H32.5Z" fill="white"/>
<defs>
<filter id="filter0_i_157_5792" x="3.5" y="3" width="44" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_157_5792"/>
</filter>
<clipPath id="bgblur_0_157_5792_clip_path" transform="translate(-3.5 -3)"><circle cx="25.5" cy="25" r="20"/>
</clipPath><filter id="filter1_f_157_5792" x="-24.7" y="-34.6" width="80" height="80" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_157_5792"/>
</filter>
<filter id="filter2_f_157_5792" x="-3.69998" y="2.40002" width="80" height="80" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_157_5792"/>
</filter>
<filter id="filter3_f_157_5792" x="0.5" y="0" width="50" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="7.5" result="effect1_foregroundBlur_157_5792"/>
</filter>
<linearGradient id="paint0_linear_157_5792" x1="25.5" y1="15" x2="25.5" y2="35" gradientUnits="userSpaceOnUse">
<stop stop-color="#8800FF"/>
<stop offset="1" stop-color="#FF7700"/>
</linearGradient>
</defs>
</svg>
