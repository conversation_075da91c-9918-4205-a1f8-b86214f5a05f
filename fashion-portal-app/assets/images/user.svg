<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="3" y="3" width="44" height="44"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(0.5px);clip-path:url(#bgblur_0_157_5808_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_157_5808)" data-figma-bg-blur-radius="1">
<circle cx="25" cy="25" r="20" fill="white" fill-opacity="0.6"/>
<circle cx="25" cy="25" r="20.5" stroke="white" stroke-opacity="0.5"/>
</g>
<g opacity="0.8">
<mask id="mask0_157_5808" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="5" y="5" width="40" height="40">
<circle cx="25" cy="25" r="20" fill="white" fill-opacity="0.3"/>
<circle cx="25" cy="25" r="19.85" stroke="white" stroke-opacity="0.5" stroke-width="0.3"/>
</mask>
<g mask="url(#mask0_157_5808)">
<g filter="url(#filter1_f_157_5808)">
<circle cx="14.8" cy="5.40002" r="20" fill="#FF7700"/>
</g>
<g filter="url(#filter2_f_157_5808)">
<circle cx="35.8" cy="42.4" r="20" fill="#8800FF"/>
</g>
</g>
</g>
<g filter="url(#filter3_f_157_5808)">
<circle cx="25" cy="25" r="10" fill="url(#paint0_linear_157_5808)"/>
</g>
<path d="M33.5355 25.8119H31.6794C31.8686 26.3299 31.972 26.8889 31.972 27.4715V34.4863C31.972 34.7292 31.9297 34.9624 31.8527 35.1792H34.9212C36.0675 35.1792 37 34.2467 37 33.1005V29.2764C37 27.3661 35.4458 25.8119 33.5355 25.8119ZM18.028 27.4715C18.028 26.8889 18.1314 26.3299 18.3206 25.812H16.4645C14.5542 25.812 13 27.3662 13 29.2765V33.1006C13 34.2468 13.9325 35.1793 15.0787 35.1793H18.1473C18.0683 34.9568 18.028 34.7225 18.028 34.4864V27.4715ZM27.1216 24.007H22.8784C20.968 24.007 19.4139 25.5612 19.4139 27.4715V34.4864C19.4139 34.869 19.7241 35.1793 20.1068 35.1793H29.8932C30.2759 35.1793 30.5861 34.8691 30.5861 34.4864V27.4715C30.5861 25.5612 29.032 24.007 27.1216 24.007ZM25 14.8207C22.7026 14.8207 20.8335 16.6898 20.8335 18.9873C20.8335 20.5456 21.6936 21.9066 22.9638 22.6212C23.5663 22.9601 24.2609 23.1538 25 23.1538C25.7391 23.1538 26.4337 22.9601 27.0362 22.6212C28.3065 21.9066 29.1665 20.5456 29.1665 18.9873C29.1665 16.6898 27.2974 14.8207 25 14.8207ZM17.6837 18.7042C15.9655 18.7042 14.5676 20.102 14.5676 21.8202C14.5676 23.5384 15.9655 24.9363 17.6837 24.9363C18.1059 24.9366 18.5237 24.8507 18.9115 24.6837C19.5634 24.4031 20.1009 23.9063 20.4334 23.284C20.6744 22.8338 20.8002 22.3309 20.7997 21.8202C20.7997 20.1021 19.4019 18.7042 17.6837 18.7042ZM32.3163 18.7042C30.5981 18.7042 29.2003 20.102 29.2003 21.8202C29.1998 22.3309 29.3256 22.8338 29.5666 23.284C29.8991 23.9063 30.4366 24.4031 31.0885 24.6837C31.4763 24.8507 31.8941 24.9366 32.3163 24.9363C34.0345 24.9363 35.4324 23.5384 35.4324 21.8202C35.4324 20.102 34.0345 18.7042 32.3163 18.7042Z" fill="white"/>
<defs>
<filter id="filter0_i_157_5808" x="3" y="3" width="44" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_157_5808"/>
</filter>
<clipPath id="bgblur_0_157_5808_clip_path" transform="translate(-3 -3)"><circle cx="25" cy="25" r="20"/>
</clipPath><filter id="filter1_f_157_5808" x="-25.2" y="-34.6" width="80" height="80" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_157_5808"/>
</filter>
<filter id="filter2_f_157_5808" x="-4.19999" y="2.40002" width="80" height="80" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_157_5808"/>
</filter>
<filter id="filter3_f_157_5808" x="0" y="0" width="50" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="7.5" result="effect1_foregroundBlur_157_5808"/>
</filter>
<linearGradient id="paint0_linear_157_5808" x1="25" y1="15" x2="25" y2="35" gradientUnits="userSpaceOnUse">
<stop stop-color="#8800FF"/>
<stop offset="1" stop-color="#FF7700"/>
</linearGradient>
</defs>
</svg>
