/**
 * Colors configuration for the GSM Mobile app
 * This file defines all color constants used throughout the application
 */

// Define color palette
const primary = {
  start: '#FF8C00', // Orange
  end: '#8A2BE2',   // Purple
};

const accent = {
  white: '#FFFFFF',
  black: '#000000',
  gold: '#FFD700',
};

const text = {
  primary: '#333333',
  secondary: '#666666',
  tertiary: '#999999',
  light: '#FFFFFF',
};

const background = {
  light: '#FFFFFF',
  dark: '#121212',
  gradient: ['#FF8C00', '#8A2BE2'], // Orange to Purple gradient
};

const status = {
  success: '#4CAF50',
  warning: '#FFC107',
  error: '#F44336',
  info: '#2196F3',
};

const shadow = {
  color: 'rgba(0, 0, 0, 0.1)',
};

const border = {
  light: '#E0E0E0',
  dark: '#424242',
};

// Main Colors object
const Colors = {
  primary,
  accent,
  text,
  background,
  status,
  shadow,
  border,
  // Additional direct properties for backward compatibility
  success: '#4CAF50',
  error: '#F44336',
  warning: '#FFC107',
};

// Ensure all properties are properly defined
if (!Colors.background || !Colors.background.light) {
  console.error('Colors.background.light is not properly defined');
}

if (!Colors.text || !Colors.text.light) {
  console.error('Colors.text.light is not properly defined');
}

export default Colors;