export const user = {
  name: "<PERSON>",
  rewards: 5847,
  submissions: 20,
  tokens: [
    { name: "GSM Fashion Token", amount: 1025 },
    { name: "BBD Luxury Token", amount: 800 },
  ],
  achievements: ["Early Adopter", "Fashion Forward", "Influencer"],
};

export const campaigns = [
  {
    id: 1,
    title: "Nike Shoes Campaign",
    description:
      "Share your experience with the latest Nike shoes and earn tokens",
    tokens: 325,
    image: require("../assets/images/nike.jpg"),
    status: "active",
    brand: "Nike",
    endDate: "2025-08-15",
  },
  {
    id: 2,
    title: "Adidas Summer Collection",
    description: "Showcase your summer style with Adidas apparel",
    tokens: 250,
    image: require("../assets/images/adidas.jpg"),
    status: "active",
    brand: "Adidas",
    endDate: "2025-08-30",
  },
  {
    id: 3,
    title: "Puma Fitness Challenge",
    description: "Show off your fitness routine with Puma gear",
    tokens: 400,
    image: require("../assets/images/puma.jpg"),
    status: "upcoming",
    brand: "Puma",
    endDate: "2025-09-15",
  },
];

export const products = [
  {
    id: 1,
    title: "Nike Air Max",
    description: "Latest edition of Nike Air Max shoes",
    tokenPrice: 500,
    image: require("../assets/images/nike-air-max.jpg"),
    brand: "Nike",
    category: "Shoes",
  },
  {
    id: 2,
    title: "Adidas Hoodie",
    description: "Comfortable Adidas hoodie for all seasons",
    tokenPrice: 350,
    image: require("../assets/images/adidas-hoodie.jpg"),
    brand: "Adidas",
    category: "Apparel",
  },
  {
    id: 3,
    title: "Puma Backpack",
    description: "Stylish and functional Puma backpack",
    tokenPrice: 275,
    image: require("../assets/images/puma-backpack.jpg"),
    brand: "Puma",
    category: "Accessories",
  },
];

export const brands = [
  {
    id: 1,
    name: "Nike",
    description: "Global sports and fitness brand",
    image: require("../assets/images/nike-logo.jpg"),
    followers: 15243,
    campaigns: 8,
  },
  {
    id: 2,
    name: "Adidas",
    description: "Sportswear and athletic accessories",
    image: require("../assets/images/adidas-logo.jpg"),
    followers: 12876,
    campaigns: 6,
  },
  {
    id: 3,
    name: "Puma",
    description: "Athletic and casual sportswear",
    image: require("../assets/images/puma-logo.jpg"),
    followers: 9854,
    campaigns: 5,
  },
];

export const alerts = [
  {
    id: 1,
    title: "Tokens Earned",
    usd: "$312.50 USD",
    claimable: "500 Claimable",
    date: "2025-07-03",
    read: false,
  },
  {
    id: 2,
    title: "Campaign Invitation",
    usd: "$450.00 USD",
    claimable: "200 Claimable",
    date: "2025-07-03",
    read: true,
  },
  {
    id: 3,
    title: "Tokens Earned",
    usd: "$312.50 USD",
    claimable: "500 Claimable",
    date: "2025-06-25",
    read: true,
  },
  {
    id: 4,
    title: "800 Luxury Token",
    usd: "$450.00 USD",
    claimable: "200 Claimable",
    date: "2025-06-25",
    read: true,
  },
];

export const achievements = [
  {
    id: 1,
    icon: require("../assets/images/home/<USER>"),
    title: "Early Adopter",
    description: "One of the first 1000 users",
    type: "Epic",
    typeColor: "#A259FF",
    typeBorder: "#A259FF",
    typeBg: "#F6F0FF",
  },
  {
    id: 2,
    icon: require("../assets/images/home/<USER>"),
    title: "Fashion Forward",
    description: "Joined 20+ campaigns",
    type: "Rare",
    typeColor: "#FF8A00",
    typeBorder: "#FF8A00",
    typeBg: "#FFF6E5",
  },
  {
    id: 3,
    icon: require("../assets/images/home/<USER>"),
    title: "Influencer",
    description: "500+ followers",
    type: "Common",
    typeColor: "#3B82F6",
    typeBorder: "#3B82F6",
    typeBg: "#EAF4FF",
  },
];

export const activity = [
  {
    id: 1,
    icon: require("../assets/images/home/<USER>"),
    iconBg: "#A259FF",
    title: "Earned 500 FASHION tokens",
    subtitle: "Nike Web3 Collection",
    timeAgo: "2 days ago",
  },
  {
    id: 2,
    icon: require("../assets/images/home/<USER>"),
    iconBg: "#FF8A00",
    title: "Followed Adidas brand",
    subtitle: "",
    timeAgo: "3 days ago",
  },
  {
    id: 3,
    icon: require("../assets/images/home/<USER>"),
    iconBg: "#22B07D",
    title: "Submitted campaign entry",
    subtitle: "Gucci Digital Experience",
    timeAgo: "5 days ago",
  },
];

export default {
  user,
  campaigns,
  products,
  brands,
  alerts,
  achievements,
  activity,
};
