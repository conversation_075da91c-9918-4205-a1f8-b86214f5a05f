import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export default {
  // Typography
  typography: {
    fontFamily: {
      regular: 'Lexend_400Regular',  // We'll replace this with a custom font later
      medium: 'Lexend_700Bold',
      bold: 'Lexend_700Bold',
      display: 'BakbakOne_400Regular', // For headings/titles
    },

    fontSize: {
      tiny: 10,
      small: 12,
      medium: 14,
      regular: 16,
      large: 18,
      xlarge: 20,
      xxlarge: 24,
      title: 28,
      header: 32,
    },
    lineHeight: {
      tiny: 14,
      small: 18,
      medium: 20,
      regular: 24,
      large: 28,
      xlarge: 30,
      xxlarge: 36,
    },
  },

  // Spacing
  spacing: {
    tiny: 4,
    small: 8,
    medium: 12,
    regular: 16,
    large: 20,
    xlarge: 24,
    xxlarge: 32,
    section: 40,
  },

  // Border Radius
  borderRadius: {
    small: 4,
    medium: 8,
    large: 12,
    xlarge: 16,
    xxlarge: 24,
    round: 50,
  },

  // Shadows
  shadows: {
    small: {
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 2,
    },
    medium: {
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 4.65,
      elevation: 4,
    },
    large: {
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.37,
      shadowRadius: 7.49,
      elevation: 8,
    },
  },

  // Screen dimensions
  screen: {
    width,
    height,
  },
};