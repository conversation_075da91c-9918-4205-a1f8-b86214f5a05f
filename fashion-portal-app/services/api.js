import AsyncStorage from '@react-native-async-storage/async-storage';
import config from '../config/environment';

class ApiService {
  constructor() {
    this.baseURL = config.API_BASE_URL;
    this.timeout = config.API_TIMEOUT;
    this.enableLogging = config.ENABLE_LOGGING;
  }

  // Helper method to get stored token
  async getToken() {
    try {
      return await AsyncStorage.getItem(config.APP_CONFIG.TOKEN_STORAGE_KEY);
    } catch (error) {
      if (this.enableLogging) {
        console.error('Error getting token:', error);
      }
      return null;
    }
  }

  // Helper method to make HTTP requests
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const token = await this.getToken();
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };

    if (token) {
      defaultHeaders.Authorization = `Bearer ${token}`;
    }

    const requestOptions = {
      timeout: this.timeout,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
      ...options,
    };

    if (this.enableLogging) {
      console.log(`API Request: ${options.method || 'GET'} ${url}`, requestOptions);
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...requestOptions,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const responseData = await response.json();

      if (this.enableLogging) {
        console.log(`API Response: ${response.status}`, responseData);
      }

      if (!response.ok) {
        throw new Error(responseData.message || `HTTP error! status: ${response.status}`);
      }

      return responseData;
    } catch (error) {
      if (this.enableLogging) {
        console.error(`API Error: ${url}`, error);
      }
      throw error;
    }
  }

  // Authentication Methods
  async register(email, password, displayName) {
    return this.makeRequest(config.ENDPOINTS.CREATOR_REGISTER, {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
        display_name: displayName,
      }),
    });
  }

  async login(email, password) {
    return this.makeRequest(config.ENDPOINTS.CREATOR_LOGIN, {
      method: 'POST',
      body: JSON.stringify({
        email,
        password,
      }),
    });
  }

  async verifyOtp(email, otp) {
    return this.makeRequest(config.ENDPOINTS.CREATOR_VERIFY_OTP, {
      method: 'POST',
      body: JSON.stringify({
        email,
        otp,
      }),
    });
  }

  async forgotPassword(email) {
    return this.makeRequest(config.ENDPOINTS.CREATOR_FORGOT_PASSWORD, {
      method: 'POST',
      body: JSON.stringify({
        email,
      }),
    });
  }

  async resetPassword(email, resetToken, newPassword) {
    return this.makeRequest(config.ENDPOINTS.CREATOR_RESET_PASSWORD, {
      method: 'POST',
      body: JSON.stringify({
        email,
        reset_token: resetToken,
        new_password: newPassword,
      }),
    });
  }

  async resendOtp(email) {
    return this.makeRequest(`${config.ENDPOINTS.CREATOR_RESEND_OTP}?email=${encodeURIComponent(email)}`, {
      method: 'POST',
    });
  }

  // Profile Methods
  async getProfile() {
    return this.makeRequest(config.ENDPOINTS.CREATOR_PROFILE, {
      method: 'GET',
    });
  }

  async updateProfile(profileData) {
    return this.makeRequest(config.ENDPOINTS.CREATOR_PROFILE, {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  }

  async getStats() {
    return this.makeRequest(config.ENDPOINTS.CREATOR_STATS, {
      method: 'GET',
    });
  }

  // Helper method to store authentication data
  async storeAuthData(token, user, profile = null) {
    try {
      await AsyncStorage.multiSet([
        [config.APP_CONFIG.TOKEN_STORAGE_KEY, token],
        [config.APP_CONFIG.USER_STORAGE_KEY, JSON.stringify(user)],
        ...(profile ? [[config.APP_CONFIG.PROFILE_STORAGE_KEY, JSON.stringify(profile)]] : []),
      ]);
    } catch (error) {
      if (this.enableLogging) {
        console.error('Error storing auth data:', error);
      }
      throw error;
    }
  }

  // Helper method to clear authentication data
  async clearAuthData() {
    try {
      await AsyncStorage.multiRemove([
        config.APP_CONFIG.TOKEN_STORAGE_KEY,
        config.APP_CONFIG.USER_STORAGE_KEY,
        config.APP_CONFIG.PROFILE_STORAGE_KEY,
      ]);
    } catch (error) {
      if (this.enableLogging) {
        console.error('Error clearing auth data:', error);
      }
      throw error;
    }
  }

  // Helper method to get stored user data
  async getStoredUserData() {
    try {
      const [token, user, profile] = await AsyncStorage.multiGet([
        config.APP_CONFIG.TOKEN_STORAGE_KEY,
        config.APP_CONFIG.USER_STORAGE_KEY,
        config.APP_CONFIG.PROFILE_STORAGE_KEY,
      ]);

      return {
        token: token[1],
        user: user[1] ? JSON.parse(user[1]) : null,
        profile: profile[1] ? JSON.parse(profile[1]) : null,
      };
    } catch (error) {
      if (this.enableLogging) {
        console.error('Error getting stored user data:', error);
      }
      return { token: null, user: null, profile: null };
    }
  }
}

// Export singleton instance
export default new ApiService();
