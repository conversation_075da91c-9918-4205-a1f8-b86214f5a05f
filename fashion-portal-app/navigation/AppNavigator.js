import React from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createStackNavigator } from "@react-navigation/stack";
import { Ionicons } from "@expo/vector-icons";
import { useAuth } from "../contexts/AuthContext";
import { Image, View } from "react-native";

// Import screens
import SplashScreen from "../screens/SplashScreen";
import WelcomeScreen from "../screens/WelcomeScreen";
import WelcomeScreen2 from "../screens/WelcomeScreen2";
import LoginScreen from "../screens/LoginScreen";
import SignupScreen from "../screens/SignupScreen";
import ForgotPasswordScreen1 from "../screens/ForgotPasswordScreen1";
import ForgotPasswordScreen2 from "../screens/ForgotPasswordScreen2";
import ForgotPasswordScreen3 from "../screens/ForgotPasswordScreen3";
import OTPVerificationScreen from "../screens/OTPVerificationScreen";
import EditProfileScreen from "../screens/EditProfileScreen";
import DashboardScreen from "../screens/DashboardScreen";
import CampaignsScreen from "../screens/CampaignsScreen";
import CampaignDetailsScreen from "../screens/CampaignDetailsScreen";
import MyRewardsScreen from "../screens/MyRewardsScreen";
import ExploreScreen from "../screens/ExploreScreen";
import ProductDetailsScreen from "../screens/ProductDetailsScreen";
// import SellProductsScreen from "../screens/SellProductsScreen";
import ProfileScreen from "../screens/ProfileScreen";
import AlertsScreen from "../screens/AlertsScreen";
import MessageCenterScreen from "../screens/MessageCenterScreen";
import TransactionHistoryScreen from "../screens/TransactionHistoryScreen";

// Import constants
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";

// Create navigators
const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

const icons = {
  home: {
    filled: require("../assets/images/home/<USER>"),
    outline: require("../assets/images/home/<USER>"),
  },
  explore: {
    filled: require("../assets/images/home/<USER>"),
    outline: require("../assets/images/home/<USER>"),
  },
  rewards: {
    filled: require("../assets/images/home/<USER>"),
    outline: require("../assets/images/home/<USER>"),
  },
  profile: {
    filled: require("../assets/images/home/<USER>"),
    outline: require("../assets/images/home/<USER>"),
  },
  alerts: {
    filled: require("../assets/images/home/<USER>"),
    outline: require("../assets/images/home/<USER>"),
  },
};

// Auth Stack Navigator
const AuthStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: Colors.background.light },
      }}
    >
      <Stack.Screen name="Welcome" component={WelcomeScreen} />
      <Stack.Screen name="Welcome2" component={WelcomeScreen2} />
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Signup" component={SignupScreen} />
      <Stack.Screen name="OTPVerification" component={OTPVerificationScreen} />
      <Stack.Screen name="ForgotPassword1" component={ForgotPasswordScreen1} />
      <Stack.Screen name="ForgotPassword2" component={ForgotPasswordScreen2} />
      <Stack.Screen name="ForgotPassword3" component={ForgotPasswordScreen3} />
    </Stack.Navigator>
  );
};

// Dashboard Stack Navigator
const DashboardStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Dashboard" component={DashboardScreen} />
      <Stack.Screen name="MessageCenter" component={MessageCenterScreen} />
      <Stack.Screen
        name="TransactionHistory"
        component={TransactionHistoryScreen}
      />
      <Stack.Screen name="CampaignDetails" component={CampaignDetailsScreen} />
    </Stack.Navigator>
  );
};

// Campaigns Stack Navigator
const CampaignsStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Campaigns" component={CampaignsScreen} />
      <Stack.Screen name="CampaignDetails" component={CampaignDetailsScreen} />
    </Stack.Navigator>
  );
};

// Explore Stack Navigator
const ExploreStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Explore" component={ExploreScreen} />
      <Stack.Screen name="ProductDetails" component={ProductDetailsScreen} />
    </Stack.Navigator>
  );
};

// Rewards Stack Navigator
const RewardsStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="MyRewards" component={MyRewardsScreen} />
    </Stack.Navigator>
  );
};

// Profile Stack Navigator
const ProfileStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Profile" component={ProfileScreen} />
      <Stack.Screen name="EditProfile" component={EditProfileScreen} />
    </Stack.Navigator>
  );
};
const AlertsStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Alerts" component={AlertsScreen} />
    </Stack.Navigator>
  );
};

// Main Tab Navigator
const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarActiveTintColor: Colors.primary.end,
        tabBarInactiveTintColor: Colors.text.secondary,
        tabBarStyle: {
          backgroundColor: "#fff",
          borderTopWidth: 0,
          elevation: 10,
          shadowColor: Colors.shadow.color,
          shadowOffset: { width: 0, height: -3 },
          shadowOpacity: 0.1,
          shadowRadius: 5,
          height: 76,
          paddingTop: 5,
          paddingBottom: 5,
          borderTopLeftRadius: 0,
          borderTopRightRadius: 0,
        },
        tabBarLabelStyle: {
          color: "#333333",
          fontSize: 12,
          fontFamily: Theme.typography.fontFamily.regular,
          marginTop: 14,
        },
        tabBarIcon: ({ focused, color, size }) => {
          let icon;
          if (route.name === "DashboardTab") {
            icon = focused ? icons.home.filled : icons.home.outline;
          } else if (route.name === "ExploreTab") {
            icon = focused ? icons.explore.filled : icons.explore.outline;
          } else if (route.name === "RewardsTab") {
            icon = focused ? icons.rewards.filled : icons.rewards.outline;
          } else if (route.name === "ProfileTab") {
            icon = focused ? icons.profile.filled : icons.profile.outline;
          } else if (route.name === "Alerts") {
            icon = focused ? icons.alerts.filled : icons.alerts.outline;
          }
          return (
            <Image
              source={icon}
              style={{
                width: 32,
                height: 32,
                resizeMode: "contain",
                marginTop: 8,
              }}
            />
          );
        },
      })}
    >
      <Tab.Screen
        name="DashboardTab"
        component={DashboardStack}
        options={{ tabBarLabel: "Home" }}
      />
      <Tab.Screen
        name="ExploreTab"
        component={ExploreStack}
        options={{ tabBarLabel: "Explore" }}
      />
      <Tab.Screen
        name="RewardsTab"
        component={RewardsStack}
        options={{ tabBarLabel: "Rewards" }}
      />
      <Tab.Screen
        name="ProfileTab"
        component={ProfileStack}
        options={{ tabBarLabel: "Profile" }}
      />
      <Tab.Screen
        name="Alerts"
        component={AlertsStack}
        options={{ tabBarLabel: "Alerts" }}
      />
    </Tab.Navigator>
  );
};

// Main App Navigator
const AppNavigator = () => {
  const { isLoading, isAuthenticated } = useAuth();
  const [showSplash, setShowSplash] = React.useState(true);

  React.useEffect(() => {
    // Show splash screen for 2 seconds
    const timer = setTimeout(() => {
      setShowSplash(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  // Show splash screen first
  if (showSplash) {
    return <SplashScreen />;
  }

  // Show loading if auth is still checking
  if (isLoading) {
    return <SplashScreen />;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!isAuthenticated ? (
          <Stack.Screen name="Auth" component={AuthStack} />
        ) : (
          <Stack.Screen name="Main" component={TabNavigator} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
