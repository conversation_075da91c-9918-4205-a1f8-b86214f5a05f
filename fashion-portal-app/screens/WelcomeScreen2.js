// import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  Image,
  ImageBackground,
  SafeAreaView,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";
import Button from "../components/Button";

const WelcomeScreen2 = ({ navigation }) => {
  const handleLogin = () => {
    navigation.navigate("Login");
  };

  const handleSignup = () => {
    navigation.navigate("Signup");
  };

  return (
    <ImageBackground
      source={require("../assets/images/home/<USER>")}
      style={styles.background}
    >
      <StatusBar style="light" />
      <SafeAreaView style={styles.container}>
        <View style={styles.overlay}>
          <View style={styles.content}>
            {/* <View style={styles.logoContainer}> */}
              <Image
                source={require("../assets/icon.png")}
                style={styles.logo}
                resizeMode="contain"
              />
            {/* </View> */}

            <Text style={styles.title}>Join the GSM Community</Text>
            <Text style={styles.subtitle}>
              Connect with brands, earn rewards, and monetize your social
              influence
            </Text>

            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>500+</Text>
                <Text style={styles.statLabel}>Brands</Text>
              </View>

              <View style={styles.statDivider} />

              <View style={styles.statItem}>
                <Text style={styles.statValue}>10K+</Text>
                <Text style={styles.statLabel}>Users</Text>
              </View>

              <View style={styles.statDivider} />

              <View style={styles.statItem}>
                <Text style={styles.statValue}>$2M+</Text>
                <Text style={styles.statLabel}>Rewards</Text>
              </View>
            </View>

            <View style={styles.buttonContainer}>
              <Button
                title="Login"
                onPress={handleLogin}
                style={styles.button}
              />

              <Button
                title="Sign Up"
                onPress={handleSignup}
                style={styles.button}
                gradient={false}
                outlined={true}
                textStyle={styles.signupButtonText}
              />
            </View>

            <Text style={styles.termsText}>
              By continuing, you agree to our Terms of Service and Privacy
              Policy
            </Text>
          </View>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  background: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  container: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
    padding: Theme.spacing.large,
  },
  content: {
    width: "100%",
    maxWidth: 400,
    alignItems: "center",
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Theme.spacing.large,
  },
  logo: {
    width: 70,
    height: 70,
    marginBottom: Theme.spacing.large,
  },
  title: {
    fontSize: Theme.typography.fontSize.xxlarge,
    fontWeight: "bold",
    color: Colors.accent.white,
    marginBottom: Theme.spacing.medium,
    textAlign: "center",
  },
  subtitle: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.accent.white,
    opacity: 0.9,
    marginBottom: Theme.spacing.xlarge,
    textAlign: "center",
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: Theme.borderRadius.large,
    padding: Theme.spacing.large,
    marginBottom: Theme.spacing.xlarge,
  },
  statItem: {
    alignItems: "center",
  },
  statValue: {
    fontSize: Theme.typography.fontSize.xlarge,
    fontWeight: "bold",
    color: Colors.accent.white,
  },
  statLabel: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.accent.white,
    opacity: 0.8,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
  },
  buttonContainer: {
    width: "100%",
    marginBottom: Theme.spacing.large,
  },
  button: {
    marginBottom: Theme.spacing.medium,
  },
  signupButtonText: {
    color: Colors.accent.white,
  },
  termsText: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.accent.white,
    opacity: 0.7,
    textAlign: "center",
  },
});

export default WelcomeScreen2;
