import { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  SafeAreaView,
  Image,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { Ionicons } from "@expo/vector-icons";
import GradientBackground from "../components/GradientBackground";
import Button from "../components/Button";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";
import { useAuth } from "../contexts/AuthContext";

const SignupScreen = ({ navigation }) => {
  const { signup, isLoading } = useAuth();
  const [userName, setUserName] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);

  const handleSignup = async () => {
    // Validate required fields
    if (!email || !password || !confirmPassword) {
      alert('Please fill in all required fields');
      return;
    }

    if (password !== confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      alert('Password must be at least 6 characters long');
      return;
    }

    // Create display name from first and last name
    const displayName = `${firstName || ""} ${lastName || ""}`.trim() || userName || "Creator";

    try {
      const result = await signup(email, password, confirmPassword, displayName);

      if (result.success) {
        // Navigate to OTP verification screen
        navigation.navigate('OTPVerification', {
          email: email,
          message: result.message
        });
      } else {
        alert(result.error || 'Registration failed');
      }
    } catch (error) {
      alert('Registration failed. Please try again.');
    }
  };

  const handleLogin = () => {
    navigation.navigate("Login");
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <GradientBackground>
      <StatusBar style="light" />
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.keyboardAvoidingView}
        >
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
          >
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons
                name="arrow-back"
                size={24}
                color={Colors.text.primary}
              />
            </TouchableOpacity>

            {/* <View style={styles.logoContainer}> */}
            <Image
              source={require("../assets/icon.png")}
              style={styles.logo}
              resizeMode="contain"
            />
            {/* </View> */}

            <Text style={styles.title}>Signup</Text>
            <Text style={styles.subtitle}>
              Lets get start your new journey with us!
            </Text>

            <View style={styles.formContainer}>
              {/* <View style={styles.nameRow}> */}
              <View style={[styles.inputContainer, styles.nameInput]}>
                <Ionicons
                  name="person-outline"
                  size={20}
                  color={Colors.text.secondary}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="User Name"
                  placeholderTextColor={Colors.text.secondary}
                  value={userName}
                  onChangeText={setUserName}
                />
              </View>
              <View style={[styles.inputContainer]}>
                <Ionicons
                  name="person-outline"
                  size={20}
                  color={Colors.text.secondary}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="First Name"
                  placeholderTextColor={Colors.text.secondary}
                  value={firstName}
                  onChangeText={setFirstName}
                />
              </View>

              <View style={[styles.inputContainer]}>
                <Ionicons
                  name="person-outline"
                  size={20}
                  color={Colors.text.secondary}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Last Name"
                  placeholderTextColor={Colors.text.secondary}
                  value={lastName}
                  onChangeText={setLastName}
                />
              </View>
              {/* </View> */}

              <View style={styles.inputContainer}>
                <Ionicons
                  name="mail-outline"
                  size={20}
                  color={Colors.text.secondary}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Email"
                  placeholderTextColor={Colors.text.secondary}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>

              <View style={styles.inputContainer}>
                <Ionicons
                  name="lock-closed-outline"
                  size={20}
                  color={Colors.text.secondary}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Password"
                  placeholderTextColor={Colors.text.secondary}
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Ionicons
                    name={showPassword ? "eye-outline" : "eye-off-outline"}
                    size={20}
                    color={Colors.text.secondary}
                  />
                </TouchableOpacity>
              </View>
              <View style={styles.inputContainer}>
                <Ionicons
                  name="lock-closed-outline"
                  size={20}
                  color={Colors.text.secondary}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Confirm Password"
                  placeholderTextColor={Colors.text.secondary}
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Ionicons
                    name={showPassword ? "eye-outline" : "eye-off-outline"}
                    size={20}
                    color={Colors.text.secondary}
                  />
                </TouchableOpacity>
              </View>

              <Button
                title={isLoading ? "SIGNING UP..." : "SIGNUP!"}
                onPress={handleSignup}
                disabled={isLoading}
                style={styles.signupButton}
              />

              <View style={styles.loginContainer}>
                <Text style={styles.loginText}>Already have an account? </Text>
                <TouchableOpacity onPress={handleLogin}>
                  <Text style={styles.loginLink}>Login</Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: Theme.spacing.large,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Theme.spacing.large,
  },

  logo: {
    alignSelf: "center",
    width: 70,
    height: 70,
    marginBottom: Theme.spacing.large,
  },
  title: {
    fontFamily: Theme.typography.fontFamily.display,
    fontSize: Theme.typography.fontSize.title,
    fontWeight: "normal",
    color: Colors.text.primary,
    textAlign: "left",
  },
  subtitle: {
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.primary,
    textAlign: "left",
    marginBottom: 30,
  },
  formContainer: {
    width: "100%",
  },
  nameRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: Theme.spacing.medium,
  },
  // nameInput: {
  //   width: "48%",
  //   marginBottom: 0,
  // },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.background.light,
    borderWidth: 1,
    borderColor: "#D5B9A0",
    borderRadius: Theme.borderRadius.medium,
    marginBottom: Theme.spacing.medium,
    height: 50,
  },
  inputIcon: {
    marginHorizontal: Theme.spacing.medium,
  },
  input: {
    flex: 1,
    height: "100%",
    color: Colors.text.primary,
    fontSize: Theme.typography.fontSize.medium,
    outlineStyle: "none",
  },
  passwordToggle: {
    padding: Theme.spacing.medium,
  },
  termsText: {
    color: Colors.text.secondary,
    fontSize: Theme.typography.fontSize.small,
    textAlign: "center",
    marginBottom: Theme.spacing.large,
  },
  signupButton: {
    marginBottom: Theme.spacing.large,
  },
  loginContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  loginText: {
    color: Colors.text.secondary,
    fontSize: Theme.typography.fontSize.medium,
  },
  loginLink: {
    color: Colors.primary.end,
    fontSize: Theme.typography.fontSize.medium,
    fontWeight: "bold",
  },
});

export default SignupScreen;
