import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { Ionicons } from "@expo/vector-icons";
import GradientBackground from "../components/GradientBackground";
import Button from "../components/Button";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";

const ForgotPasswordScreen2 = ({ navigation }) => {
  const handleCheckEmail = () => {
    // For demo purposes, navigate to reset password screen
    navigation.navigate("ForgotPassword3");
  };

  const handleResendEmail = () => {
    // DEMO MODE: Always succeeds
    alert("Reset link has been resent to your email");
  };

  const handleBack = () => {
    navigation.navigate("Login");
  };

  return (
    <GradientBackground>
      <StatusBar style="light" />
      <SafeAreaView style={styles.container}>
        <View style={styles.content}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
          </TouchableOpacity>

          <Image
            source={require("../assets/icon.png")}
            style={styles.logo}
            resizeMode="contain"
          />

          <Text style={styles.title}>Check Your Email</Text>
          <Text style={styles.subtitle}>
            We've sent a password reset link to your email address. Please check
            your inbox.
          </Text>

          <View style={styles.cardContainer}>
            <View style={styles.card}>
              <Ionicons
                name="checkmark-circle-outline"
                size={40}
                color={Colors.status.success}
                style={styles.cardIcon}
              />
              <Text style={styles.cardTitle}>Email Sent Successfully</Text>
              <Text style={styles.cardText}>
                If you don't see the email in your inbox, please check your spam
                folder.
              </Text>

              <Button
                title="I've Reset My Password"
                onPress={handleCheckEmail}
                style={styles.checkEmailButton}
              />

              <TouchableOpacity
                style={styles.resendContainer}
                onPress={handleResendEmail}
              >
                <Text style={styles.resendText}>
                  Didn't receive the email?{" "}
                </Text>
                <Text style={styles.resendLink}>Resend</Text>
              </TouchableOpacity>
            </View>
          </View>

          <TouchableOpacity
            style={styles.backToLoginContainer}
            onPress={handleBack}
          >
            <Text style={styles.backToLoginText}>Back to Login</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: Theme.spacing.large,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Theme.spacing.large,
  },
  iconContainer: {
    alignSelf: "center",
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Theme.spacing.large,
  },
  logo: {
    alignSelf: "center",
    width: 70,
    height: 70,
    marginBottom: Theme.spacing.large,
  },
  title: {
    fontFamily: Theme.typography.fontFamily.display,
    fontSize: Theme.typography.fontSize.title,
    fontWeight: "normal",
    color: Colors.text.primary,
    textAlign: "left",
  },
  subtitle: {
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.primary,
    textAlign: "left",
    marginBottom: 30,
  },
  cardContainer: {
    width: "100%",
    marginBottom: Theme.spacing.xlarge,
  },
  card: {
    backgroundColor: Colors.background.light,
    borderRadius: Theme.borderRadius.large,
    padding: Theme.spacing.large,
    alignItems: "center",
    ...Theme.shadows.medium,
  },
  cardIcon: {
    marginBottom: Theme.spacing.medium,
  },
  cardTitle: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: "bold",
    color: Colors.text.primary,
    marginBottom: Theme.spacing.small,
    textAlign: "center",
  },
  cardText: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.secondary,
    marginBottom: Theme.spacing.large,
    textAlign: "center",
  },
  checkEmailButton: {
    marginBottom: Theme.spacing.medium,
  },
  resendContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Theme.spacing.small,
  },
  resendText: {
    color: Colors.text.secondary,
    fontSize: Theme.typography.fontSize.small,
  },
  resendLink: {
    color: Colors.primary.end,
    fontSize: Theme.typography.fontSize.small,
    fontWeight: "bold",
  },
  backToLoginContainer: {
    alignItems: "center",
  },
  backToLoginText: {
    color: Colors.accent.white,
    fontSize: Theme.typography.fontSize.medium,
    fontWeight: "bold",
  },
});

export default ForgotPasswordScreen2;
