import { useRef, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from "react-native";
import { StatusBar } from "expo-status-bar";
// import { Ionicons } from "@expo/vector-icons";
// import { LinearGradient } from "expo-linear-gradient";
import MockData from "../constants/MockData";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";
import GradientBackground from "../components/GradientBackground";
// import Button from "../components/Button";
import FeaturedCampaignsSlider from "../components/FeaturedCampaignsSlider";
import StatCardsRow from "../components/StatCardsRow";
const DashboardScreen = ({ navigation }) => {
  const { user, campaigns } = MockData;
  // const [activeCampaignIndex, setActiveCampaignIndex] = useState(0);
  // const campaignScrollRef = useRef(null);

  return (
    <View style={styles.container}>
      <GradientBackground>
        <StatusBar style="dark" />
        <SafeAreaView style={styles.safeArea}>
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Gradient Header */}
            <View style={styles.headerGradient}>
              <View style={styles.headerRow}>
                <View style={{ flex: 1, position: "relative", zIndex: 10 }}>
                  <Text style={styles.greeting}>
                    Hi, {user.name.split(" ")[0]}
                  </Text>
                  <Text style={styles.subtitle}>
                    Ready to earn rewards and discover amazing fashion
                    campaigns?
                  </Text>
                </View>
                <Image
                  source={require("../assets/images/home/<USER>")}
                  // source={{
                  //   uri: user.profileImage || "../assets/images/home/<USER>",
                  // }}
                  style={styles.profileImage}
                />
              </View>
              {/* Stat Cards Row - now horizontally scrollable with 3 cards */}
              <StatCardsRow />
            </View>

            {/* Quick Access Buttons */}
            <View style={styles.quickAccessGrid}>
              <TouchableOpacity
                style={[styles.quickAccessBtn, { borderColor: "#FF8A00" }]}
                onPress={() => navigation.navigate("CampaignsTab")}
              >
                <View style={styles.quickAccessRow}>
                  <Image
                    source={require("../assets/images/horn.png")}
                    style={{ width: 18, height: 18 }}
                  />
                  <Text style={styles.quickAccessText}>Find Campaigns</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.quickAccessBtn}
                onPress={() => navigation.navigate("RewardsTab")}
              >
                <View style={styles.quickAccessRow}>
                  <Image
                    source={require("../assets/images/cup.png")}
                    style={{ width: 18, height: 18 }}
                  />
                  <Text style={styles.quickAccessText}>My Rewards</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.quickAccessBtn}
                onPress={() => navigation.navigate("ExploreTab")}
              >
                <View style={styles.quickAccessRow}>
                  <Image
                    source={require("../assets/images/explore.png")}
                    style={{ width: 18, height: 18 }}
                  />
                  <Text style={styles.quickAccessText}>Explore</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.quickAccessBtn}
                onPress={() => navigation.navigate("Alerts")}
              >
                <View style={styles.quickAccessRow}>
                  <Image
                    source={require("../assets/images/sell.png")}
                    style={{ width: 18, height: 18 }}
                  />
                  <Text style={styles.quickAccessText}>Alerts</Text>
                </View>
              </TouchableOpacity>
            </View>

            {/* Featured Campaigns */}
            <FeaturedCampaignsSlider />

            {/* Wallet Status Card */}
            <View style={styles.walletCard}>
              <View style={styles.walletLeft}>
                <Image
                  source={require("../assets/images/placeholder.jpg")}
                  style={styles.walletIcon}
                />
                <View>
                  <Text style={styles.walletTitle}>Wallet Status</Text>
                  <Text style={styles.walletSubtitle}>
                    Connected to MetaMask
                  </Text>
                </View>
              </View>
              <View style={styles.walletStatusBadge}>
                <Text style={styles.walletStatusText}>Connected</Text>
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </GradientBackground>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "transparent",
  },
  signupButton: {
    width: 150,
  },
  safeArea: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  headerGradient: {
    paddingTop: 16,
    marginBottom: 16,
  },

  headerRow: {
    // flexDirection: "row",
    // alignItems: "center",
    marginBottom: 18,
    position: "relative",
    // borderColor: "black",
    // borderWidth: 2,
    paddingHorizontal: 20,
  },
  greeting: {
    fontFamily: Theme.typography.fontFamily.display,
    fontSize: Theme.typography.fontSize.title,
    fontWeight: "normal",
    color: Colors.text.primary,
    textAlign: "left",
    marginTop: 20,
    marginBottom: 4,
  },
  subtitle: {
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.primary,
    textAlign: "left",
    paddingRight: 70,
    // marginBottom: 30,
  },
  profileImage: {
    position: "absolute",
    right: -60,
    width: 240,
    height: 240,
    top: -50,
  },
  quickAccessGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    // marginTop: -32,
    // marginBottom: 24,
  },
  quickAccessBtn: {
    alignItems: "center",
    justifyContent: "center",
    width: "48.5%",
    paddingVertical: 16,
    borderRadius: 10,
    backgroundColor: "#fff",
    borderWidth: 2,
    borderColor: "#eee",
    marginBottom: 16,
    shadowColor: "#000",
    shadowOpacity: 0.06,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
  },
  quickAccessRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  quickAccessText: {
    fontSize: 14,
    color: "#333333",
    marginLeft: 8,
  },
  walletCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 20,
    marginHorizontal: 16,
    padding: 18,
    marginBottom: 24,
    justifyContent: "space-between",
    shadowColor: "#000",
    shadowOpacity: 0.08,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
  },
  walletLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  walletIcon: {
    width: 44,
    height: 44,
    borderRadius: 22,
    marginRight: 12,
  },
  walletTitle: {
    fontSize: 17,
    fontWeight: "bold",
    color: "#222",
  },
  walletSubtitle: {
    fontSize: 14,
    color: "#888",
    marginTop: 2,
  },
  walletStatusBadge: {
    backgroundColor: "#E6F9ED",
    borderRadius: 12,
    paddingHorizontal: 14,
    paddingVertical: 6,
  },
  walletStatusText: {
    color: "#00C566",
    fontWeight: "bold",
    fontSize: 15,
  },
});

export default DashboardScreen;
