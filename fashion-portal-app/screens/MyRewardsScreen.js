import { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";
import GradientBackground from "../components/GradientBackground";
import Button from "../components/Button";
import CompleteIcon from "../assets/images/complete.svg";
import QrIcon from "../assets/images/qr.svg";
import HistoryIcon from "../assets/images/history.svg";
import fashionIcon from "../assets/images/fashion.png";
import luxuryIcon from "../assets/images/luxury.png";

const TOKENS = [
  {
    icon: fashionIcon,
    name: "1,250 Fashion Token",
    usd: "$312.50 USD",
    claimable: 500,
    claimableText: "500 Claimable",
    claimBtn: "CLAIM 500 TOKENS",
    color: "#B08D57",
  },
  {
    icon: luxuryIcon,
    name: "800 Luxury Token",
    usd: "$450.00 USD",
    claimable: 200,
    claimableText: "200 Claimable",
    claimBtn: "CLAIM 500 TOKENS",
    color: "#C0A16B",
  },
  {
    icon: luxuryIcon,
    name: "1000 Green Token",
    usd: "$176.50 USD",
    claimable: 0,
    claimableText: "",
    claimBtn: null,
    color: "#1B5E20",
  },
];
const History = [
  {
    icon: null,
    name: "Claimed 500 FASHION",
    usd: "Nike Web3 Collection",
    claimable: "",
    claimableText: "2024-01-15",
    claimBtn: "Completed",
    color: "#B08D57",
  },
  {
    icon: null,
    name: "Claimed 500 FASHION",
    usd: "Nike Web3 Collection",
    claimable: "",
    claimableText: "2024-01-15",
    claimBtn: "Pending",
    color: "#B08D57",
  },
  {
    icon: null,
    name: "Claimed 500 FASHION",
    usd: "Nike Web3 Collection",
    claimable: "",
    claimableText: "2024-01-15",
    claimBtn: "Failed",
    color: "#B08D57",
  },
];

const MyRewardsScreen = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState("Tokens");

  return (
    <View style={styles.container}>
      <GradientBackground>
        <StatusBar style="dark" />
        <SafeAreaView style={styles.safeArea}>
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {/* Header Row */}
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
                paddingHorizontal: 20,
                marginTop: 24,
                position: "relative",
                zIndex: 10,
              }}
            >
              <TouchableOpacity onPress={() => navigation.goBack()}>
                <Ionicons name="arrow-back" size={28} color="#222" />
              </TouchableOpacity>
              {/* Large faded profile image, like DashboardScreen */}
              <Image
                source={require("../assets/images/home/<USER>")}
                style={{
                  position: "absolute",
                  right: -60,
                  top: -50,
                  width: 240,
                  height: 240,
                  opacity: 1,
                }}
              />
            </View>

            {/* Heading */}
            <View
              style={{ paddingHorizontal: 20, marginTop: 10, marginBottom: 8 }}
            >
              <Text
                style={[
                  styles.greeting,
                  {
                    fontWeight: "bold",
                    marginTop: 0,
                    fontFamily: Theme.typography.fontFamily.display,
                  },
                ]}
              >
                My Rewards
              </Text>
              <Text
                style={[styles.subtitle, { color: "#444", paddingRight: 0 }]}
              >
                Track and claim your earned rewards
              </Text>
            </View>

            {/* Stat Cards Row - EXACT copy from DashboardScreen, only content changed */}
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.statsScrollContent}
              style={{ marginTop: 18 }}
            >
              <LinearGradient
                colors={["#FF983D", "#FF7700"]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={[
                  styles.statCardExact,
                  { marginRight: 10, marginLeft: 20 },
                ]}
              >
                <View style={styles.statCardBottomRow}>
                  <Text style={styles.statLabelExact}>Total Earned</Text>
                  <Text style={styles.statValueExact}>$2,692</Text>
                </View>
                <View style={styles.statIconCircle}>
                  {/* <Ionicons name="star" size={24} color="#FFD700" /> */}
                  <Image
                    source={require("../assets/images/coin.png")}
                    style={{ width: 18, height: 18 }}
                  />
                </View>
              </LinearGradient>
              <LinearGradient
                colors={["#BA6CFF", "#8800FF"]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={[styles.statCardExact, { marginRight: 10 }]}
              >
                <View style={styles.statCardBottomRow}>
                  <Text style={styles.statLabelExact}>Pending Claims</Text>
                  <Text style={styles.statValueExact}>3</Text>
                </View>
                <View style={styles.statIconCircle}>
                  <Image
                    source={require("../assets/images/wallet.png")}
                    style={{ width: 18, height: 18 }}
                  />
                </View>
              </LinearGradient>
              {/* Third Stat Card Example */}
              <LinearGradient
                colors={["#00C566", "#00B2A9"]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={[styles.statCardExact, { marginRight: 10 }]}
              >
                <View style={styles.statCardBottomRow}>
                  <Text style={styles.statLabelExact}>Completed Tasks</Text>
                  <Text style={styles.statValueExact}>12</Text>
                </View>
                <View style={styles.statIconCircle}>
                  <Ionicons name="checkmark-circle" size={24} color="#00C566" />
                </View>
              </LinearGradient>
            </ScrollView>

            {/* Tabs (Tokens / History) */}
            <View style={styles.tabsContainer}>
              <TouchableOpacity
                style={[
                  styles.tabButton,
                  activeTab === "Tokens" && styles.tabButtonActive,
                ]}
                onPress={() => setActiveTab("Tokens")}
              >
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Image
                    source={require("../assets/icon.png")}
                    style={{ width: 18, height: 18, marginRight: 8 }}
                  />
                  <Text
                    style={[
                      styles.tabButtonText,
                      activeTab === "Tokens" && styles.tabButtonTextActive,
                    ]}
                  >
                    Tokens
                  </Text>
                </View>
                {activeTab === "Tokens" && (
                  <View style={styles.tabButtonIndicator} />
                )}
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.tabButton,
                  activeTab === "History" && styles.tabButtonActive,
                ]}
                onPress={() => setActiveTab("History")}
              >
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Image
                    source={require("../assets/images/clock.png")}
                    style={{ width: 18, height: 18, marginRight: 8 }}
                  />
                  <Text
                    style={[
                      styles.tabButtonText,
                      activeTab === "History" && styles.tabButtonTextActive,
                    ]}
                  >
                    History
                  </Text>
                </View>
                {activeTab === "History" && (
                  <View style={styles.tabButtonIndicator} />
                )}
              </TouchableOpacity>
            </View>

            {/* Tab Content */}
            {activeTab === "Tokens" && (
              <View style={{ paddingHorizontal: 16, marginBottom: 10 }}>
                <Text
                  style={{
                    fontSize: 30,
                    color: "121212",
                    marginBottom: 12,
                    fontFamily: Theme.typography.fontFamily.display,
                  }}
                >
                  Tokens
                </Text>
                {TOKENS.map((token, idx) => (
                  <View
                    key={idx}
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      backgroundColor: "#fff",
                      borderRadius: 20,
                      padding: 12,
                      marginBottom: 16,
                      shadowColor: "#000",
                      shadowOpacity: 0.06,
                      shadowRadius: 6,
                      shadowOffset: { width: 0, height: 2 },
                    }}
                  >
                    <Image
                      source={token.icon}
                      style={{ width: 24, height: 24, marginRight: 16 }}
                      resizeMode="contain"
                    />
                    <View>
                      <Text
                        style={{
                          fontSize: 16,
                          color: "#121212",
                          fontFamily: Theme.typography.fontFamily.display,
                        }}
                      >
                        {token.name}
                      </Text>
                      <Text
                        style={{
                          fontSize: 12,
                          color: "#666666",
                          marginTop: 2,
                          fontFamily: Theme.typography.fontFamily.regular,
                        }}
                      >
                        {token.usd}
                      </Text>
                      {token.claimableText ? (
                        <Text
                          style={{
                            fontSize: 12,
                            color: "#8800FF",
                            marginTop: 2,
                            fontFamily: Theme.typography.fontFamily.regular,
                          }}
                        >
                          {token.claimableText}
                        </Text>
                      ) : null}
                    </View>
                    {token.claimBtn ? (
                      <View style={{ marginLeft: "auto" }}>
                        <Button
                          title={token.claimBtn}
                          style={styles.signupButton}
                          textStyle={{
                            fontSize: 10,
                            textAlign: "center",
                            fontFamily: Theme.typography.fontFamily.medium,
                          }}
                          onPress={() => {}}
                        />
                      </View>
                    ) : null}
                  </View>
                ))}
              </View>
            )}
            {activeTab === "History" && (
              <View style={{ paddingHorizontal: 16, marginBottom: 10 }}>
                <Text
                  style={{
                    fontSize: 30,
                    color: "#121212",
                    marginBottom: 12,
                    fontFamily: Theme.typography.fontFamily.display,
                  }}
                >
                  Transaction History
                </Text>
                {History.map((item, idx) => {
                  // Status badge color logic
                  let badgeColor = "#4AC96B",
                    badgeBg = "#E6F9ED",
                    iconBg = "#4AC96B",
                    icon = null;
                  if (item.claimBtn === "Pending") {
                    badgeColor = "#FF983D";
                    badgeBg = "#FFF3E6";
                    iconBg = "#FF983D";
                  } else if (item.claimBtn === "Failed") {
                    badgeColor = "#FF5C8A";
                    badgeBg = "#FFE6EC";
                    iconBg = "#FF5C8A";
                  }
                  // Icon logic (SVGs or fallback to Ionicons)
                  let iconName = "checkmark";
                  if (item.claimBtn === "Pending") iconName = "time";
                  if (item.claimBtn === "Failed") iconName = "close";
                  return (
                    <View
                      key={idx}
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                        backgroundColor: "#fff",
                        borderRadius: 20,
                        padding: 18,
                        marginBottom: 18,
                        shadowColor: "#000",
                        shadowOpacity: 0.06,
                        shadowRadius: 6,
                        shadowOffset: { width: 0, height: 2 },
                      }}
                    >
                      {/* Icon circle */}
                      <View
                        style={{
                          width: 44,
                          height: 44,
                          borderRadius: 22,
                          backgroundColor: iconBg,
                          alignItems: "center",
                          justifyContent: "center",
                          marginRight: 16,
                        }}
                      >
                        <Ionicons name={iconName} size={28} color="#fff" />
                      </View>
                      {/* Main content */}
                      <View style={{ flex: 1 }}>
                        <Text
                          style={{
                            fontSize: 16,
                            color: "#121212",
                            fontFamily: Theme.typography.fontFamily.display,
                          }}
                        >
                          {item.name}
                        </Text>
                        <Text
                          style={{
                            fontSize: 15,
                            color: "#666",
                            marginTop: 2,
                            fontFamily: Theme.typography.fontFamily.regular,
                          }}
                        >
                          {item.usd}
                        </Text>
                        {item.claimableText ? (
                          <Text
                            style={{
                              fontSize: 13,
                              color: "#888",
                              marginTop: 2,
                              fontFamily: Theme.typography.fontFamily.regular,
                            }}
                          >
                            {item.claimableText}
                          </Text>
                        ) : null}
                      </View>
                      {/* Status badge */}
                      <View
                        style={{
                          backgroundColor: badgeBg,
                          borderRadius: 16,
                          paddingHorizontal: 16,
                          paddingVertical: 6,
                          alignItems: "center",
                          justifyContent: "center",
                          minWidth: 80,
                        }}
                      >
                        <Text
                          style={{
                            color: badgeColor,
                            fontWeight: "bold",
                            fontSize: 14,
                            textAlign: "center",
                          }}
                        >
                          {item.claimBtn}
                        </Text>
                      </View>
                    </View>
                  );
                })}
              </View>
            )}
          </ScrollView>
        </SafeAreaView>
      </GradientBackground>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "none",
  },
  safeArea: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  greeting: {
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.title,
    fontWeight: "normal",
    color: Colors.text.primary,
    textAlign: "left",
    marginTop: 20,
    marginBottom: 4,
  },
  subtitle: {
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.primary,
    textAlign: "left",
    paddingRight: 100,
  },
  statsScrollContent: {
    flexDirection: "row",
    paddingLeft: 2,
    paddingRight: 0,
    marginTop: 18,
    marginBottom: 8,
  },
  statCardExact: {
    width: 190,
    borderRadius: 10,
    paddingVertical: 15,
    paddingHorizontal: 15,
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  statCardBottomRow: {
    // flexDirection: "row",
    // alignItems: "center",
    // justifyContent: "space-between",
  },
  statLabelExact: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "400",
    marginBottom: 0,
    opacity: 0.95,
  },
  statValueExact: {
    color: "#fff",
    fontSize: 30,
    fontWeight: "normal",
    letterSpacing: 1,
    fontFamily: Theme.typography.fontFamily.regular,
  },
  statIconCircle: {
    width: 38,
    height: 38,
    borderRadius: 19,
    backgroundColor: "#fff",
    alignItems: "center",
    justifyContent: "center",
    marginLeft: 8,
  },
  tabsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
  },
  tabButton: {
    flex: 1,
    borderWidth: 2,
    borderColor: "transparent",
    borderRadius: 10,
    backgroundColor: "#fff",
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 6,
    shadowColor: "#000",
    shadowOpacity: 0.06,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
    height: 56, // fixed height for all tabs
  },
  tabButtonActive: {
    borderColor: "#FF8A00",
  },
  tabButtonText: {
    fontSize: 14,
    color: "#333333",
    ontFamily: Theme.typography.fontFamily.regular,
    fontWeight: "normal",
  },
  tabButtonTextActive: {
    color: "#FF8A00",
    ontFamily: Theme.typography.fontFamily.regular,
    fontSize: 14,
    fontWeight: "normal",
  },
  tabButtonIndicator: {
    height: 2,
    backgroundColor: "#FF8A00",
    borderRadius: 1,
    marginTop: 4,
  },
  signupButton: {
    height: 35,
    width: 135,
    alignItems: "center",
    fontSize: 10,
    fontFamily: Theme.typography.fontFamily.medium,
    fontWeight: "600",
  },
});

export default MyRewardsScreen;
