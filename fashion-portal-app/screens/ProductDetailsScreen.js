import React, { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  TouchableOpacity, 
  SafeAreaView,
  Image,
  StatusBar
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Colors from '../constants/Colors';
import Theme from '../constants/Theme';
import Button from '../components/Button';

const ProductDetailsScreen = ({ navigation, route }) => {
  const { product } = route.params;
  const [isFollowing, setIsFollowing] = useState(false);
  const [selectedSize, setSelectedSize] = useState(null);
  const [selectedColor, setSelectedColor] = useState(null);
  
  const sizes = product.sizes || ['S', 'M', 'L', 'XL'];
  const colors = product.colors || ['Black', 'White', 'Blue', 'Red'];
  
  const handleBack = () => {
    navigation.goBack();
  };
  
  const handleFollow = () => {
    setIsFollowing(!isFollowing);
  };
  
  const handleBuy = () => {
    // In a real app, you would navigate to checkout or add to cart
    console.log('Buy product:', product.title);
  };
  
  const renderSizeItem = (size) => (
    <TouchableOpacity 
      key={size}
      style={[
        styles.sizeItem,
        selectedSize === size && styles.selectedSizeItem
      ]}
      onPress={() => setSelectedSize(size)}
    >
      <Text 
        style={[
          styles.sizeText,
          selectedSize === size && styles.selectedSizeText
        ]}
      >
        {size}
      </Text>
    </TouchableOpacity>
  );
  
  const renderColorItem = (color) => (
    <TouchableOpacity 
      key={color}
      style={[
        styles.colorItem,
        selectedColor === color && styles.selectedColorItem,
        { backgroundColor: color.toLowerCase() === 'white' ? '#f8f8f8' : color.toLowerCase() }
      ]}
      onPress={() => setSelectedColor(color)}
    >
      {selectedColor === color && (
        <Ionicons 
          name="checkmark" 
          size={16} 
          color={color.toLowerCase() === 'white' ? Colors.text.primary : Colors.accent.white} 
        />
      )}
    </TouchableOpacity>
  );
  
  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.imageContainer}>
          <Image 
            source={product.image ? { uri: product.image } : require('../assets/images/placeholder.jpg')}
            style={styles.image}
            resizeMode="cover"
          />
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.8)']}
            style={styles.gradient}
          />
          <SafeAreaView style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons name="arrow-back" size={24} color={Colors.accent.white} />
            </TouchableOpacity>
            <View style={styles.headerActions}>
              <TouchableOpacity style={styles.actionButton} onPress={handleFollow}>
                <Ionicons 
                  name={isFollowing ? "heart" : "heart-outline"} 
                  size={24} 
                  color={isFollowing ? Colors.error : Colors.accent.white} 
                />
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Ionicons name="share-outline" size={24} color={Colors.accent.white} />
              </TouchableOpacity>
            </View>
          </SafeAreaView>
        </View>
        
        <View style={styles.content}>
          <View style={styles.productInfo}>
            <View style={styles.brandContainer}>
              <Text style={styles.brand}>{product.brand}</Text>
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={16} color={Colors.status.warning} />
                <Text style={styles.rating}>{product.rating || '4.8'}</Text>
              </View>
            </View>
            <Text style={styles.title}>{product.title}</Text>
            <Text style={styles.price}>${product.price}</Text>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{product.description || 'This premium product offers exceptional quality and style. Perfect for any occasion, it combines comfort with durability and a modern aesthetic.'}</Text>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Size</Text>
            <View style={styles.sizeContainer}>
              {sizes.map(renderSizeItem)}
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Color</Text>
            <View style={styles.colorContainer}>
              {colors.map(renderColorItem)}
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Features</Text>
            <View style={styles.featuresList}>
              {(product.features || [
                'Premium quality materials',
                'Comfortable fit',
                'Durable construction',
                'Easy to maintain'
              ]).map((feature, index) => (
                <View key={index} style={styles.featureItem}>
                  <Ionicons name="checkmark-circle" size={20} color={Colors.primary.end} />
                  <Text style={styles.featureText}>{feature}</Text>
                </View>
              ))}
            </View>
          </View>
          
          <View style={styles.buySection}>
            <Button 
              title="BUY NOW" 
              onPress={handleBuy}
              style={styles.buyButton}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.light,
  },
  imageContainer: {
    height: 350,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 100,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: Theme.spacing.large,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: Theme.spacing.small,
  },
  content: {
    padding: Theme.spacing.large,
  },
  productInfo: {
    marginBottom: Theme.spacing.large,
  },
  brandContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.tiny,
  },
  brand: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.secondary,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.text.primary,
    marginLeft: Theme.spacing.tiny,
    fontWeight: 'bold',
  },
  title: {
    fontSize: Theme.typography.fontSize.xlarge,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: Theme.spacing.small,
  },
  price: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: 'bold',
    color: Colors.primary.end,
  },
  section: {
    marginBottom: Theme.spacing.large,
  },
  sectionTitle: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: Theme.spacing.medium,
  },
  description: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.secondary,
    lineHeight: 22,
  },
  sizeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  sizeItem: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: Colors.border.light,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Theme.spacing.medium,
    marginBottom: Theme.spacing.small,
  },
  selectedSizeItem: {
    backgroundColor: Colors.primary.end,
    borderColor: Colors.primary.end,
  },
  sizeText: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.primary,
  },
  selectedSizeText: {
    color: Colors.accent.white,
    fontWeight: 'bold',
  },
  colorContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  colorItem: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Theme.spacing.medium,
    marginBottom: Theme.spacing.small,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  selectedColorItem: {
    borderColor: Colors.primary.end,
    borderWidth: 2,
  },
  featuresList: {
    marginTop: Theme.spacing.small,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Theme.spacing.medium,
  },
  featureText: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.secondary,
    marginLeft: Theme.spacing.medium,
  },
  buySection: {
    marginTop: Theme.spacing.medium,
    marginBottom: Theme.spacing.xlarge,
  },
  buyButton: {
    marginTop: Theme.spacing.medium,
  },
});

export default ProductDetailsScreen;