import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Theme from '../constants/Theme';
import ApiService from '../services/api';
import config from '../config/environment';

const TestAPIScreen = ({ navigation }) => {
  const [testResults, setTestResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testPassword, setTestPassword] = useState('testpassword123');

  const addResult = (test, success, message) => {
    setTestResults(prev => [...prev, {
      id: Date.now(),
      test,
      success,
      message,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testAPIConnection = async () => {
    setIsLoading(true);
    addResult('API Connection', true, `Testing connection to: ${config.API_BASE_URL}`);
    
    try {
      // Test basic connectivity
      const response = await fetch(`${config.API_BASE_URL.replace('/api', '')}/health`);
      const data = await response.json();
      
      if (response.ok) {
        addResult('Health Check', true, `Server is running. Status: ${data.status}`);
      } else {
        addResult('Health Check', false, 'Server responded with error');
      }
    } catch (error) {
      addResult('Health Check', false, `Connection failed: ${error.message}`);
    }
    
    setIsLoading(false);
  };

  const testCreatorRegistration = async () => {
    setIsLoading(true);
    addResult('Registration Test', true, 'Starting creator registration test...');
    
    try {
      const response = await ApiService.register(
        testEmail,
        testPassword,
        'Test Creator'
      );
      
      addResult('Registration', true, response.message || 'Registration successful');
    } catch (error) {
      if (error.message.includes('already exists')) {
        addResult('Registration', true, 'User already exists (expected for repeated tests)');
      } else {
        addResult('Registration', false, error.message);
      }
    }
    
    setIsLoading(false);
  };

  const testCreatorLogin = async () => {
    setIsLoading(true);
    addResult('Login Test', true, 'Testing creator login...');
    
    try {
      const response = await ApiService.login(testEmail, testPassword);
      addResult('Login', true, 'Login successful - token received');
      
      // Test profile fetch
      try {
        const profileResponse = await ApiService.getProfile();
        addResult('Profile Fetch', true, 'Profile data retrieved successfully');
      } catch (profileError) {
        addResult('Profile Fetch', false, profileError.message);
      }
      
    } catch (error) {
      if (error.message.includes('verify your email')) {
        addResult('Login', true, 'Login blocked - email verification required (expected)');
      } else {
        addResult('Login', false, error.message);
      }
    }
    
    setIsLoading(false);
  };

  const testForgotPassword = async () => {
    setIsLoading(true);
    addResult('Forgot Password Test', true, 'Testing forgot password...');
    
    try {
      const response = await ApiService.forgotPassword(testEmail);
      addResult('Forgot Password', true, response.message);
    } catch (error) {
      addResult('Forgot Password', false, error.message);
    }
    
    setIsLoading(false);
  };

  const runAllTests = async () => {
    clearResults();
    await testAPIConnection();
    await testCreatorRegistration();
    await testCreatorLogin();
    await testForgotPassword();
    
    Alert.alert(
      'Tests Completed',
      'All API tests have been completed. Check the results below.',
      [{ text: 'OK' }]
    );
  };

  const renderResult = (result) => (
    <View key={result.id} style={[styles.resultItem, result.success ? styles.successItem : styles.errorItem]}>
      <View style={styles.resultHeader}>
        <Ionicons 
          name={result.success ? 'checkmark-circle' : 'close-circle'} 
          size={20} 
          color={result.success ? Colors.success : Colors.error} 
        />
        <Text style={styles.resultTitle}>{result.test}</Text>
        <Text style={styles.resultTime}>{result.timestamp}</Text>
      </View>
      <Text style={styles.resultMessage}>{result.message}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.title}>API Integration Test</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.configSection}>
          <Text style={styles.sectionTitle}>Configuration</Text>
          <Text style={styles.configText}>Environment: {config.ENVIRONMENT}</Text>
          <Text style={styles.configText}>API URL: {config.API_BASE_URL}</Text>
          <Text style={styles.configText}>Timeout: {config.API_TIMEOUT}ms</Text>
        </View>

        <View style={styles.testSection}>
          <Text style={styles.sectionTitle}>Test Credentials</Text>
          <TextInput
            style={styles.input}
            placeholder="Test Email"
            value={testEmail}
            onChangeText={setTestEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
          <TextInput
            style={styles.input}
            placeholder="Test Password"
            value={testPassword}
            onChangeText={setTestPassword}
            secureTextEntry
          />
        </View>

        <View style={styles.buttonSection}>
          <TouchableOpacity 
            style={[styles.button, styles.primaryButton]} 
            onPress={runAllTests}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>
              {isLoading ? 'Running Tests...' : 'Run All Tests'}
            </Text>
          </TouchableOpacity>

          <View style={styles.individualButtons}>
            <TouchableOpacity style={styles.button} onPress={testAPIConnection} disabled={isLoading}>
              <Text style={styles.buttonText}>Test Connection</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.button} onPress={testCreatorRegistration} disabled={isLoading}>
              <Text style={styles.buttonText}>Test Registration</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.button} onPress={testCreatorLogin} disabled={isLoading}>
              <Text style={styles.buttonText}>Test Login</Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearResults}>
            <Text style={styles.buttonText}>Clear Results</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.resultsSection}>
          <Text style={styles.sectionTitle}>Test Results</Text>
          {testResults.length === 0 ? (
            <Text style={styles.noResults}>No tests run yet</Text>
          ) : (
            testResults.map(renderResult)
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.light,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.medium,
    paddingVertical: Theme.spacing.medium,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  backButton: {
    marginRight: Theme.spacing.medium,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: Theme.spacing.medium,
  },
  configSection: {
    marginVertical: Theme.spacing.medium,
    padding: Theme.spacing.medium,
    backgroundColor: Colors.background.white,
    borderRadius: Theme.borderRadius.medium,
    ...Theme.shadows.small,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: Theme.spacing.small,
  },
  configText: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginBottom: 4,
  },
  testSection: {
    marginVertical: Theme.spacing.medium,
    padding: Theme.spacing.medium,
    backgroundColor: Colors.background.white,
    borderRadius: Theme.borderRadius.medium,
    ...Theme.shadows.small,
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.border.light,
    borderRadius: Theme.borderRadius.small,
    padding: Theme.spacing.small,
    marginBottom: Theme.spacing.small,
    fontSize: 16,
  },
  buttonSection: {
    marginVertical: Theme.spacing.medium,
  },
  button: {
    backgroundColor: Colors.primary.end,
    padding: Theme.spacing.medium,
    borderRadius: Theme.borderRadius.medium,
    alignItems: 'center',
    marginBottom: Theme.spacing.small,
  },
  primaryButton: {
    backgroundColor: Colors.primary.start,
  },
  clearButton: {
    backgroundColor: Colors.text.secondary,
  },
  buttonText: {
    color: Colors.accent.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  individualButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  resultsSection: {
    marginVertical: Theme.spacing.medium,
    marginBottom: Theme.spacing.large * 2,
  },
  noResults: {
    textAlign: 'center',
    color: Colors.text.secondary,
    fontStyle: 'italic',
    marginTop: Theme.spacing.medium,
  },
  resultItem: {
    padding: Theme.spacing.medium,
    borderRadius: Theme.borderRadius.medium,
    marginBottom: Theme.spacing.small,
    borderLeftWidth: 4,
  },
  successItem: {
    backgroundColor: '#f0f9ff',
    borderLeftColor: Colors.success,
  },
  errorItem: {
    backgroundColor: '#fef2f2',
    borderLeftColor: Colors.error,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    flex: 1,
  },
  resultTime: {
    fontSize: 12,
    color: Colors.text.secondary,
  },
  resultMessage: {
    fontSize: 14,
    color: Colors.text.secondary,
    marginLeft: 28,
  },
});

export default TestAPIScreen;
