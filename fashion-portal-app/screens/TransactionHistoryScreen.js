import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Theme from '../constants/Theme';
import MockData from '../constants/MockData';

// Mock transaction data
const mockTransactions = [
  {
    id: 1,
    type: 'earned',
    title: 'Nike Shoes Campaign',
    amount: 325,
    tokenType: 'GSM Fashion Token',
    date: '2025-07-05',
    status: 'completed',
  },
  {
    id: 2,
    type: 'redeemed',
    title: 'Adidas Hoodie Purchase',
    amount: -350,
    tokenType: 'GSM Fashion Token',
    date: '2025-07-03',
    status: 'completed',
  },
  {
    id: 3,
    type: 'earned',
    title: 'Product Review Bonus',
    amount: 125,
    tokenType: 'BBD Luxury Token',
    date: '2025-06-28',
    status: 'completed',
  },
  {
    id: 4,
    type: 'earned',
    title: 'Adidas Summer Collection',
    amount: 250,
    tokenType: 'GSM Fashion Token',
    date: '2025-06-25',
    status: 'completed',
  },
  {
    id: 5,
    type: 'pending',
    title: 'Puma Fitness Challenge',
    amount: 400,
    tokenType: 'GSM Fashion Token',
    date: '2025-06-20',
    status: 'pending',
  },
];

const TransactionItem = ({ transaction }) => {
  const getIconName = (type, status) => {
    if (status === 'pending') return 'time-outline';
    switch (type) {
      case 'earned':
        return 'add-circle-outline';
      case 'redeemed':
        return 'remove-circle-outline';
      default:
        return 'swap-horizontal-outline';
    }
  };

  const getIconColor = (type, status) => {
    if (status === 'pending') return Colors.status.warning;
    switch (type) {
      case 'earned':
        return Colors.status.success;
      case 'redeemed':
        return Colors.status.error;
      default:
        return Colors.text.secondary;
    }
  };

  const getAmountColor = (type, status) => {
    if (status === 'pending') return Colors.status.warning;
    return type === 'earned' ? Colors.status.success : Colors.status.error;
  };

  return (
    <View style={styles.transactionItem}>
      <View style={styles.transactionHeader}>
        <View style={[styles.iconContainer, { backgroundColor: `${getIconColor(transaction.type, transaction.status)}20` }]}>
          <Ionicons
            name={getIconName(transaction.type, transaction.status)}
            size={20}
            color={getIconColor(transaction.type, transaction.status)}
          />
        </View>
        <View style={styles.transactionContent}>
          <View style={styles.transactionTitleRow}>
            <Text style={styles.transactionTitle} numberOfLines={1}>
              {transaction.title}
            </Text>
            <Text style={[styles.transactionAmount, { color: getAmountColor(transaction.type, transaction.status) }]}>
              {transaction.amount > 0 ? '+' : ''}{transaction.amount}
            </Text>
          </View>
          <View style={styles.transactionDetailsRow}>
            <Text style={styles.transactionToken} numberOfLines={1}>
              {transaction.tokenType}
            </Text>
            <Text style={styles.transactionDate}>
              {new Date(transaction.date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
              })}
            </Text>
          </View>
          {transaction.status === 'pending' && (
            <View style={styles.statusContainer}>
              <Text style={styles.statusText}>Pending</Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const TransactionHistoryScreen = ({ navigation }) => {
  const [filter, setFilter] = useState('all'); // all, earned, redeemed, pending

  const handleBack = () => {
    navigation.goBack();
  };

  const filteredTransactions = mockTransactions.filter(transaction => {
    if (filter === 'all') return true;
    if (filter === 'pending') return transaction.status === 'pending';
    return transaction.type === filter;
  });

  const renderTransaction = ({ item }) => (
    <TransactionItem transaction={item} />
  );

  const renderFilterButton = (filterType, label) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        filter === filterType && styles.filterButtonActive
      ]}
      onPress={() => setFilter(filterType)}
    >
      <Text style={[
        styles.filterButtonText,
        filter === filterType && styles.filterButtonTextActive
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="receipt-outline" size={64} color={Colors.text.tertiary} />
      <Text style={styles.emptyTitle}>No Transactions</Text>
      <Text style={styles.emptyText}>
        Your transaction history will appear here when you earn or redeem tokens.
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Transaction History</Text>
          <View style={styles.headerRight} />
        </View>

        <View style={styles.filterContainer}>
          {renderFilterButton('all', 'All')}
          {renderFilterButton('earned', 'Earned')}
          {renderFilterButton('redeemed', 'Redeemed')}
          {renderFilterButton('pending', 'Pending')}
        </View>

        <FlatList
          data={filteredTransactions}
          renderItem={renderTransaction}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
        />
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.light,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Theme.spacing.large,
    paddingVertical: Theme.spacing.medium,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.light,
    alignItems: 'center',
    justifyContent: 'center',
    ...Theme.shadows.small,
  },
  headerTitle: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  headerRight: {
    width: 40,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: Theme.spacing.large,
    paddingVertical: Theme.spacing.medium,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  filterButton: {
    paddingHorizontal: Theme.spacing.medium,
    paddingVertical: Theme.spacing.small,
    borderRadius: Theme.borderRadius.medium,
    marginRight: Theme.spacing.small,
    backgroundColor: Colors.background.light,
    borderWidth: 1,
    borderColor: Colors.border.light,
  },
  filterButtonActive: {
    backgroundColor: Colors.primary.end,
    borderColor: Colors.primary.end,
  },
  filterButtonText: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.text.secondary,
    fontWeight: 'bold',
  },
  filterButtonTextActive: {
    color: Colors.accent.white,
  },
  listContent: {
    padding: Theme.spacing.large,
  },
  transactionItem: {
    backgroundColor: Colors.background.light,
    borderRadius: Theme.borderRadius.large,
    marginBottom: Theme.spacing.medium,
    ...Theme.shadows.small,
  },
  transactionHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: Theme.spacing.medium,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Theme.spacing.medium,
  },
  transactionContent: {
    flex: 1,
  },
  transactionTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.tiny,
  },
  transactionTitle: {
    flex: 1,
    fontSize: Theme.typography.fontSize.medium,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginRight: Theme.spacing.small,
  },
  transactionAmount: {
    fontSize: Theme.typography.fontSize.medium,
    fontWeight: 'bold',
  },
  transactionDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  transactionToken: {
    flex: 1,
    fontSize: Theme.typography.fontSize.small,
    color: Colors.text.secondary,
    marginRight: Theme.spacing.small,
  },
  transactionDate: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.text.secondary,
  },
  statusContainer: {
    marginTop: Theme.spacing.tiny,
  },
  statusText: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.status.warning,
    fontWeight: 'bold',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Theme.spacing.xxlarge,
  },
  emptyTitle: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginTop: Theme.spacing.medium,
    marginBottom: Theme.spacing.small,
  },
  emptyText: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.secondary,
    textAlign: 'center',
    paddingHorizontal: Theme.spacing.large,
  },
});

export default TransactionHistoryScreen;
