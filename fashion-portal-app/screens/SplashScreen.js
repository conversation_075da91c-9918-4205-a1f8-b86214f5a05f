import React, { useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  Animated,
  Image,
  ImageBackground,
} from "react-native";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";

const SplashScreen = () => {
  // Animation values
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const scaleAnim = React.useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    // Start animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <ImageBackground
      source={require("../assets/images/home/<USER>")}
      style={styles.gradient}
    >
      <View style={styles.container}>
        {/* <View style={styles.logoContainer}>
            <View style={styles.glowEffect}>
              <View style={styles.iconCircle}>
                <Text style={styles.iconText}>GSM</Text>
              </View>
            </View>
          </View> */}
        <View style={styles.logoContainer}>
          <Image
            source={require("../assets/images/home/<USER>")}
            style={styles.shadow}
          />
          <Image
            source={require("../assets/images/home/<USER>")}
            style={styles.logo}
          />
        </View>
        <Image
          source={require("../assets/images/home/<USER>")}
          style={styles.gsmlogo}
        />
        {/* <Text style={styles.title}>GSM</Text> */}
        <Text style={styles.subtitle}>
          © 2025 Get Style Money (GSM). All Rights Reserved.
        </Text>
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  gradient: {
    width: "100%",
    height: "100%",
  },
  content: {
    alignItems: "center",
    justifyContent: "center",
  },
  logoContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Theme.spacing.large,
    position: "relative",
    // borderWidth: 2,
    // borderColor: "red",
    width: 400,
    height: 600,
    overflow: "hidden",
  },
  logo: {
    width: 150,
    height: 150,
  },
  gsmlogo: {
    width: 150,
    height: 65,
  },
  shadow: {
    position: "absolute",
    top: 0,
    left: -100,
    width: 600,
    height: 600,
    // borderWidth: 2,
    // borderColor: "red",
  },
  glowEffect: {
    width: 140,
    height: 140,
    borderRadius: 70,
    backgroundColor: "rgba(255, 140, 0, 0.3)", // Orange glow
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#FF8C00",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 20,
    elevation: 20,
  },
  iconCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#FF8C00", // Orange circle
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#FF8C00",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 1,
    shadowRadius: 15,
    elevation: 15,
  },
  iconText: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.accent.white,
  },
  title: {
    fontSize: 48,
    fontWeight: "bold",
    color: Colors.accent.white,
    marginBottom: Theme.spacing.small,
  },
  subtitle: {
    fontFamily: Theme.typography.fontFamily.regular,
    color: "white",
    fontSize: 12,
    marginTop: 40,
    opacity: 0.8,
  },
});

export default SplashScreen;
