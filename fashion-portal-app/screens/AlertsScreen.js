import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  Image,
  ScrollView,
  TouchableOpacity,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { Ionicons } from "@expo/vector-icons";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";
import GradientBackground from "../components/GradientBackground";
import MockData from "../constants/MockData";
import Button from "../components/Button";

const AlertsScreen = ({ navigation }) => {
  const [alerts] = useState(MockData.alerts);

  const handleBack = () => {
    navigation.goBack();
  };

  // Group alerts by date (assuming alerts have a 'date' property in 'YYYY-MM-DD' format)
  const groupedAlerts = alerts.reduce((acc, alert) => {
    const date = alert.date || "Unknown Date";
    if (!acc[date]) acc[date] = [];
    acc[date].push(alert);
    return acc;
  }, {});

  // Sort dates descending
  const sortedDates = Object.keys(groupedAlerts).sort((a, b) =>
    b.localeCompare(a)
  );

  return (
    <GradientBackground>
      <StatusBar style="light" />
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.headerGradient}>
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons
                name="arrow-back"
                size={28}
                color={Colors.text.primary}
              />
            </TouchableOpacity>
            <View style={styles.headerRow}>
              <View style={{ flex: 1, position: "relative", zIndex: 10 }}>
                <Text style={styles.greeting}>Alerts</Text>
                <Text style={styles.subtitle}>
                  Track your daily notification and alerts
                </Text>
              </View>
            </View>
            <Image
              source={require("../assets/images/home/<USER>")}
              style={styles.profileImage}
            />
          </View>

          {/* Alert Cards by Date */}
          {sortedDates.map((date) => (
            <View key={date} style={{ marginBottom: 10, marginHorizontal: 20 }}>
              <Text
                style={[
                  styles.subtitle,
                  {
                    marginTop: 20,
                    marginBottom: 10,
                    fontWeight: "bold",
                    fontSize: 16,
                  },
                ]}
              >
                {date.split("-").reverse().join("-")}
              </Text>
              {groupedAlerts[date].map((alert) => (
                <View key={alert.id} style={styles.alertCard}>
                  <Image
                    source={require("../assets/images/token.png")}
                    style={styles.tokenImage}
                  />
                  <View style={{ flex: 1, marginLeft: 12 }}>
                    <Text style={styles.tokenTitle}>{alert.title}</Text>
                    <Text style={styles.tokenUsd}>{alert.usd}</Text>
                    <Text style={styles.tokenClaimable}>{alert.claimable}</Text>
                  </View>
                  <View style={{ justifyContent: "center", minWidth: 120 }}>
                    <Button
                      title="CLAIM 500 TOKENS"
                      style={styles.signupButton}
                      textStyle={{
                        fontSize: 10,
                        textAlign: "center",
                        fontFamily: Theme.typography.fontFamily.medium,
                      }}
                      onPress={() => {}}
                    />

                    {/* <Button
                      title="CLAIM 500 TOKENS"
                      style={{
                        width: 120,
                        height: 36,
                        borderRadius: 12,
                        paddingHorizontal: 0,
                      }}
                      textStyle={{
                        fontSize: 12,
                        fontWeight: "bold",
                        letterSpacing: 0.5,
                      }}
                      gradient={true}
                      onPress={() => {}}
                    /> */}
                  </View>
                </View>
              ))}
            </View>
          ))}
        </SafeAreaView>
      </ScrollView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: Colors.background.light,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  safeArea: {
    flex: 1,
    padding: Theme.spacing.large,
  },
  headerGradient: {
    paddingTop: 16,
    paddingHorizontal: 20,
    position: "relative",
  },
  headerRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 18,
  },
  greeting: {
    fontFamily: Theme.typography.fontFamily.display,
    fontSize: Theme.typography.fontSize.title,
    fontWeight: "normal",
    color: Colors.text.primary,
    textAlign: "left",
    marginBottom: 4,
  },
  subtitle: {
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.primary,
    textAlign: "left",
    paddingRight: 70,
  },
  profileImage: {
    position: "absolute",
    right: -60,
    width: 240,
    height: 240,
    top: -30,
  },
  backButton: {
    marginTop: 15,
    marginBottom: 12,
  },
  alertCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 16,
    marginBottom: 10,
    shadowColor: "#000",
    shadowOpacity: 0.12, // Increased shadow opacity
    shadowRadius: 12, // Increased shadow radius
    shadowOffset: { width: 0, height: 6 }, // More pronounced shadow
    elevation: 6, // Increased elevation for Android
  },
  tokenImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
    resizeMode: "contain",
  },
  tokenTitle: {
    fontWeight: "bold",
    fontSize: 16,
    color: "#222",
    marginBottom: 2,
  },
  tokenUsd: {
    fontSize: 12,
    color: "#8800FF",
    marginTop: 2,
    fontFamily: Theme.typography.fontFamily.regular,
  },
  tokenClaimable: {
    fontSize: 12,
    color: "#666666",
    marginTop: 2,
    fontFamily: Theme.typography.fontFamily.regular,
  },
  signupButton: {
    height: 35,
    width: 135,
    alignItems: "center",
    fontSize: 10,
    fontFamily: Theme.typography.fontFamily.medium,
    fontWeight: "600",
  },
});

export default AlertsScreen;
