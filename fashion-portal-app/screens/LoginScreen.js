import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  SafeAreaView,
  Image,
  Alert,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { Ionicons } from "@expo/vector-icons";
import * as LocalAuthentication from "expo-local-authentication";
import GradientBackground from "../components/GradientBackground";
import Button from "../components/Button";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";
import { useAuth } from "../contexts/AuthContext";

const LoginScreen = ({ navigation }) => {
  const { login, isLoading } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [biometricSupported, setBiometricSupported] = useState(false);
  const [isEmailFocused, setIsEmailFocused] = useState(false);
  const [isPasswordFocused, setIsPasswordFocused] = useState(false);

  useEffect(() => {
    checkBiometricSupport();
  }, []);

  const checkBiometricSupport = async () => {
    try {
      const compatible = await LocalAuthentication.hasHardwareAsync();
      const enrolled = await LocalAuthentication.isEnrolledAsync();
      setBiometricSupported(compatible && enrolled);
    } catch (error) {
      console.log("Biometric check error:", error);
    }
  };

  const handleLogin = async () => {
    await login(email, password);
  };

  const handleBiometricLogin = async () => {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: "Login with biometrics",
        fallbackLabel: "Use password instead",
      });

      if (result.success) {
        await login("<EMAIL>", "demo_biometric");
      }
    } catch (error) {
      await login("<EMAIL>", "demo_biometric");
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate("ForgotPassword1");
  };

  const handleSignup = () => {
    navigation.navigate("Signup");
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <GradientBackground>
      <StatusBar style="light" />
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.keyboardAvoidingView}
        >
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
          >
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons
                name="arrow-back"
                size={24}
                color={Colors.text.primary}
              />
            </TouchableOpacity>

            {/* <View style={styles.logoContainer}> */}
            <Image
              source={require("../assets/icon.png")}
              style={styles.logo}
              resizeMode="contain"
            />
            {/* </View> */}

            <Text style={styles.title}>Login</Text>
            <Text style={styles.subtitle}>
              Lets get start your new journey with us!
            </Text>
            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <Ionicons
                  name="mail-outline"
                  size={20}
                  color={Colors.text.secondary}
                  style={styles.inputIcon}
                />
                <TextInput
                  // style={styles.input}
                  style={styles.input}
                  placeholder="Email"
                  placeholderTextColor={Colors.text.secondary}
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  onFocus={() => setIsEmailFocused(true)}
                  onBlur={() => setIsEmailFocused(false)}
                />
              </View>

              <View
                style={[
                  styles.inputContainer,
                  isPasswordFocused && styles.inputContainerFocused,
                ]}
              >
                <Ionicons
                  name="lock-closed-outline"
                  size={20}
                  color={Colors.text.secondary}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Password"
                  placeholderTextColor={Colors.text.secondary}
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  onFocus={() => setIsPasswordFocused(true)}
                  onBlur={() => setIsPasswordFocused(false)}
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Ionicons
                    name={showPassword ? "eye-off-outline" : "eye-outline"}
                    size={20}
                    color={Colors.text.secondary}
                  />
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                style={styles.forgotPasswordContainer}
                onPress={handleForgotPassword}
              >
                <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
              </TouchableOpacity>

              <Button
                title={isLoading ? "LOGGING IN..." : "LOGIN"}
                onPress={handleLogin}
                disabled={isLoading}
                style={styles.loginButton}
              />

              {biometricSupported && (
                <View style={styles.biometricContainer}>
                  <View style={styles.dividerContainer}>
                    <View style={styles.divider} />
                    <Text style={styles.dividerText}>OR</Text>
                    <View style={styles.divider} />
                  </View>

                  <TouchableOpacity
                    style={styles.biometricButton}
                    onPress={handleBiometricLogin}
                    disabled={isLoading}
                  >
                    <Ionicons
                      name="finger-print"
                      size={24}
                      color={Colors.primary.end}
                    />
                    <Text style={styles.biometricText}>
                      Login with Biometrics
                    </Text>
                  </TouchableOpacity>
                </View>
              )}

              <View style={styles.signupContainer}>
                <Text style={styles.signupText}>Don't have an account? </Text>
                <TouchableOpacity onPress={handleSignup}>
                  <Text style={styles.signupLink}>Sign Up</Text>
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: Theme.spacing.large,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Theme.spacing.large,
  },
  logo: {
    alignSelf: "center",
    width: 70,
    height: 70,
    marginBottom: Theme.spacing.large,
  },
  title: {
    fontFamily: Theme.typography.fontFamily.display,
    fontSize: Theme.typography.fontSize.title,
    fontWeight: "normal",
    color: Colors.text.primary,
    textAlign: "left",
  },
  subtitle: {
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.primary,
    textAlign: "left",
    marginBottom: 30,
  },
  formContainer: {
    width: "100%",
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.background.light,
    borderWidth: 1,
    borderColor: "#D5B9A0",
    borderRadius: Theme.borderRadius.medium,
    marginBottom: Theme.spacing.medium,
    height: 50,
  },

  inputIcon: {
    marginHorizontal: Theme.spacing.medium,
  },
  input: {
    flex: 1,
    height: "100%",
    color: Colors.text.primary,
    fontSize: Theme.typography.fontSize.medium,
    outlineStyle: "none",
  },
  passwordToggle: {
    padding: Theme.spacing.medium,
  },
  forgotPasswordContainer: {
    alignSelf: "flex-end",
    marginBottom: Theme.spacing.large,
  },
  forgotPasswordText: {
    fontFamily: Theme.typography.fontFamily.regular,
    color: Colors.primary.end,
    fontSize: Theme.typography.fontSize.small,
    fontWeight: "bold",
  },
  loginButton: {
    marginBottom: Theme.spacing.medium,
  },
  biometricContainer: {
    marginBottom: Theme.spacing.large,
  },
  dividerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: Theme.spacing.medium,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: Colors.border.light,
  },
  dividerText: {
    marginHorizontal: Theme.spacing.medium,
    color: Colors.text.secondary,
    fontSize: Theme.typography.fontSize.small,
  },
  biometricButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(138, 43, 226, 0.1)",
    borderWidth: 1,
    borderColor: Colors.primary.end,
    borderRadius: Theme.borderRadius.medium,
    paddingVertical: Theme.spacing.medium,
    paddingHorizontal: Theme.spacing.large,
  },
  biometricText: {
    marginLeft: Theme.spacing.small,
    color: Colors.primary.end,
    fontSize: Theme.typography.fontSize.medium,
    fontWeight: "bold",
  },
  signupContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  signupText: {
    color: Colors.text.secondary,
    fontSize: Theme.typography.fontSize.medium,
  },
  signupLink: {
    color: Colors.primary.end,
    fontSize: Theme.typography.fontSize.medium,
    fontWeight: "bold",
  },
});

export default LoginScreen;
