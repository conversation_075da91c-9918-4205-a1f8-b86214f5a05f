import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  SafeAreaView,
  Image,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { Ionicons } from "@expo/vector-icons";
import GradientBackground from "../components/GradientBackground";
import Button from "../components/Button";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";

const ForgotPasswordScreen3 = ({ navigation }) => {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleResetPassword = () => {
    // DEMO MODE: No validation required - always succeeds
    setIsLoading(true);

    // Brief loading for visual feedback
    setTimeout(() => {
      setIsLoading(false);
      // Navigate back to Login after successful reset
      navigation.reset({
        index: 0,
        routes: [{ name: "Login" }],
      });
    }, 800);
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <GradientBackground>
      <StatusBar style="light" />
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={styles.keyboardAvoidingView}
        >
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
          >
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons
                name="arrow-back"
                size={24}
                color={Colors.text.primary}
              />
            </TouchableOpacity>

            <Image
              source={require("../assets/icon.png")}
              style={styles.logo}
              resizeMode="contain"
            />

            <Text style={styles.title}>CREATE NEW PASSWORD</Text>
            <Text style={styles.subtitle}>Enter and confirm new password</Text>

            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <Ionicons
                  name="lock-closed-outline"
                  size={20}
                  color={Colors.text.secondary}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Password"
                  placeholderTextColor={Colors.text.secondary}
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Ionicons
                    name={showPassword ? "eye-outline" : "eye-off-outline"}
                    size={20}
                    color={Colors.text.secondary}
                  />
                </TouchableOpacity>
              </View>

              <View style={styles.inputContainer}>
                <Ionicons
                  name="lock-closed-outline"
                  size={20}
                  color={Colors.text.secondary}
                  style={styles.inputIcon}
                />
                <TextInput
                  style={styles.input}
                  placeholder="Confirm Password"
                  placeholderTextColor={Colors.text.secondary}
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry={!showConfirmPassword}
                  autoCapitalize="none"
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  <Ionicons
                    name={
                      showConfirmPassword ? "eye-outline" : "eye-off-outline"
                    }
                    size={20}
                    color={Colors.text.secondary}
                  />
                </TouchableOpacity>
              </View>

              {/* <Text style={styles.passwordRequirements}>
                Password must be at least 8 characters long and include a mix of
                letters, numbers, and special characters.
              </Text> */}

              <Button
                title={isLoading ? "RESETTING..." : "CREATE PASSWORD"}
                onPress={handleResetPassword}
                disabled={isLoading}
                style={styles.resetButton}
              />

              {/* <TouchableOpacity
                style={styles.backToLoginContainer}
                onPress={() => navigation.navigate("Login")}
              >
                <Text style={styles.backToLoginText}>Back to Login</Text>
              </TouchableOpacity> */}
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: Theme.spacing.large,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Theme.spacing.large,
  },
  iconContainer: {
    alignSelf: "center",
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Theme.spacing.large,
  },
  logo: {
    alignSelf: "center",
    width: 70,
    height: 70,
    marginBottom: Theme.spacing.large,
  },
  title: {
    fontFamily: Theme.typography.fontFamily.display,
    fontSize: Theme.typography.fontSize.title,
    fontWeight: "normal",
    color: Colors.text.primary,
    textAlign: "left",
  },
  subtitle: {
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.primary,
    textAlign: "left",
    marginBottom: 30,
  },
  formContainer: {
    width: "100%",
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.background.light,
    borderWidth: 1,
    borderColor: "#D5B9A0",
    borderRadius: Theme.borderRadius.medium,
    marginBottom: Theme.spacing.medium,
    height: 50,
  },
  inputIcon: {
    marginHorizontal: Theme.spacing.medium,
  },
  input: {
    flex: 1,
    height: "100%",
    color: Colors.text.primary,
    fontSize: Theme.typography.fontSize.medium,
    outlineStyle: "none",
  },
  passwordToggle: {
    padding: Theme.spacing.medium,
  },
  passwordRequirements: {
    color: Colors.text.secondary,
    fontSize: Theme.typography.fontSize.small,
    marginBottom: Theme.spacing.large,
  },
  resetButton: {
    marginBottom: Theme.spacing.large,
  },
  backToLoginContainer: {
    alignItems: "center",
  },
  backToLoginText: {
    color: Colors.primary.end,
    fontSize: Theme.typography.fontSize.medium,
    fontWeight: "bold",
  },
});

export default ForgotPasswordScreen3;
