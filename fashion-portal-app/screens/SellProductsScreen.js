import React, { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  TouchableOpacity, 
  SafeAreaView,
  TextInput,
  Image,
  KeyboardAvoidingView,
  Platform,
  Alert
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Theme from '../constants/Theme';
import Button from '../components/Button';

const SellProductsScreen = ({ navigation }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [price, setPrice] = useState('');
  const [category, setCategory] = useState('');
  const [images, setImages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  
  // Mock image selection
  const handleAddImage = () => {
    // In a real app, this would open the image picker
    // For demo purposes, we'll add a mock image
    const newImage = require('../assets/images/placeholder.jpg');
    setImages([...images, newImage]);
  };
  
  const handleRemoveImage = (index) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };
  
  const handleSubmit = () => {
    if (!title || !description || !price || !category || images.length === 0) {
      Alert.alert('Missing Information', 'Please fill in all fields and add at least one image.');
      return;
    }
    
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      
      // Show success modal
      Alert.alert(
        'Product Listed Successfully',
        'Your product has been listed and is now available in the marketplace.',
        [
          { 
            text: 'OK', 
            onPress: () => {
              // Reset form
              setTitle('');
              setDescription('');
              setPrice('');
              setCategory('');
              setImages([]);
              
              // Navigate back
              navigation.goBack();
            } 
          }
        ]
      );
    }, 1500);
  };
  
  const handleBack = () => {
    navigation.goBack();
  };
  
  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}
        >
          <ScrollView 
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.header}>
              <TouchableOpacity style={styles.backButton} onPress={handleBack}>
                <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
              </TouchableOpacity>
              <Text style={styles.title}>Sell Products</Text>
              <View style={styles.placeholder} />
            </View>
            
            <View style={styles.formContainer}>
              <Text style={styles.sectionTitle}>Upload Images</Text>
              <View style={styles.imagesContainer}>
                {images.map((image, index) => (
                  <View key={index} style={styles.imageWrapper}>
                    <Image source={image} style={styles.image} />
                    <TouchableOpacity 
                      style={styles.removeImageButton}
                      onPress={() => handleRemoveImage(index)}
                    >
                      <Ionicons name="close-circle" size={24} color={Colors.status.error} />
                    </TouchableOpacity>
                  </View>
                ))}
                
                <TouchableOpacity 
                  style={styles.addImageButton}
                  onPress={handleAddImage}
                >
                  <Ionicons name="add" size={40} color={Colors.text.secondary} />
                  <Text style={styles.addImageText}>Add Image</Text>
                </TouchableOpacity>
              </View>
              
              <Text style={styles.sectionTitle}>Product Details</Text>
              
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Title</Text>
                <TextInput
                  style={styles.input}
                  placeholder="Enter product title"
                  placeholderTextColor={Colors.text.secondary}
                  value={title}
                  onChangeText={setTitle}
                />
              </View>
              
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Description</Text>
                <TextInput
                  style={[styles.input, styles.textArea]}
                  placeholder="Enter product description"
                  placeholderTextColor={Colors.text.secondary}
                  value={description}
                  onChangeText={setDescription}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
              </View>
              
              <View style={styles.inputRow}>
                <View style={[styles.inputContainer, styles.halfInput]}>
                  <Text style={styles.inputLabel}>Price (Tokens)</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="Enter price"
                    placeholderTextColor={Colors.text.secondary}
                    value={price}
                    onChangeText={setPrice}
                    keyboardType="numeric"
                  />
                </View>
                
                <View style={[styles.inputContainer, styles.halfInput]}>
                  <Text style={styles.inputLabel}>Category</Text>
                  <TextInput
                    style={styles.input}
                    placeholder="Enter category"
                    placeholderTextColor={Colors.text.secondary}
                    value={category}
                    onChangeText={setCategory}
                  />
                </View>
              </View>
              
              <Button 
                title={isLoading ? "LISTING PRODUCT..." : "LIST PRODUCT"} 
                onPress={handleSubmit}
                disabled={isLoading}
                style={styles.submitButton}
              />
            </View>
          </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.light,
  },
  safeArea: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: Theme.spacing.large,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.large,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.light,
    alignItems: 'center',
    justifyContent: 'center',
    ...Theme.shadows.small,
  },
  title: {
    fontSize: Theme.typography.fontSize.xlarge,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  formContainer: {
    width: '100%',
  },
  sectionTitle: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: Theme.spacing.medium,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: Theme.spacing.large,
  },
  imageWrapper: {
    width: 100,
    height: 100,
    borderRadius: Theme.borderRadius.medium,
    overflow: 'hidden',
    marginRight: Theme.spacing.medium,
    marginBottom: Theme.spacing.medium,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  removeImageButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: Colors.background.light,
    borderRadius: 12,
  },
  addImageButton: {
    width: 100,
    height: 100,
    borderRadius: Theme.borderRadius.medium,
    borderWidth: 1,
    borderColor: Colors.text.secondary,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Theme.spacing.medium,
  },
  addImageText: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.text.secondary,
    marginTop: Theme.spacing.tiny,
  },
  inputContainer: {
    marginBottom: Theme.spacing.large,
  },
  inputLabel: {
    fontSize: Theme.typography.fontSize.medium,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: Theme.spacing.small,
  },
  input: {
    backgroundColor: Colors.background.light,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: Theme.borderRadius.medium,
    padding: Theme.spacing.medium,
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.primary,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Theme.spacing.medium,
  },
  halfInput: {
    width: '48%',
  },
  submitButton: {
    marginTop: Theme.spacing.medium,
  },
});

export default SellProductsScreen;