import React, { useState } from "react";
import {
  View,
  Text,
  Image,
  SafeAreaView,
  TextInput,
  Platform,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import { Ionicons } from "@expo/vector-icons";
import GradientBackground from "../components/GradientBackground";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";
import StatCardsRow from "../components/StatCardsRow";
import FeaturedCampaignsSlider from "../components/FeaturedCampaignsSlider";
import CampaignImageCard from "../components/CampaignImageCard";
import SelectDropdown from "../components/SelectDropdown";

const styles = {
  scrollContent: {
    paddingBottom: 32,
  },
  headerGradient: {
    paddingTop: 16,
    // marginBottom: 16,
    paddingHorizontal: 20,
    position: "relative",
  },
  headerRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 18,
  },
  greeting: {
    fontFamily: Theme.typography.fontFamily.display,
    fontSize: Theme.typography.fontSize.title,
    fontWeight: "normal",
    color: Colors.text.primary,
    textAlign: "left",
    // marginTop: 5,
    marginBottom: 4,
  },
  subtitle: {
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.primary,
    textAlign: "left",
    paddingRight: 70,
    // marginBottom: 30,
  },
  profileImage: {
    position: "absolute",
    right: -60,
    width: 240,
    height: 240,
    top: -30,
    // borderWidth: 2,
    // borderColor: "black",
  },
  backButton: {
    marginTop: 15,
    marginBottom: 12,
  },
};

export default function CampaignsScreen({ navigation }) {
  const [search, setSearch] = useState("");
  const [searchFocused, setSearchFocused] = useState(false);
  const [brand, setBrand] = useState("All Brands");
  const [status, setStatus] = useState("All Status");

  // UI constants
  const borderColor = "#D5B9A0";
  const borderRadius = 10;
  const pickerTextColor = "#666666";
  const pickerTextSize = 14;
  const pickerArrowColor = "#FF7700";
  const pickerArrowSize = 26;
  const pickerPadding = 20;

  // Custom dropdown icon for Android only
  const CustomDropdownIcon = () =>
    Platform.OS === "android" ? (
      <Ionicons
        name="chevron-down"
        size={pickerArrowSize}
        color={pickerArrowColor}
        style={{
          marginRight: pickerPadding,
        }}
      />
    ) : null;
  const handleBack = () => {
    navigation.goBack();
  };
  const options = [
    { label: "All Brands", value: "All Brands" },
    { label: "Nike", value: "Nike" },
    { label: "Adidas", value: "Adidas" },
    { label: "Puma", value: "Puma" },
  ];
  const statusOptions = [
    { label: "All Status", value: "All Status" },
    { label: "Active", value: "Active" },
    { label: "Upcoming", value: "Upcoming" },
  ];
  return (
    <GradientBackground>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <SafeAreaView style={{ flex: 1 }}>
          <View style={styles.headerGradient}>
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons
                name="arrow-back"
                size={28}
                color={Colors.text.primary}
              />
            </TouchableOpacity>
            <View style={styles.headerRow}>
              <View style={{ flex: 1, position: "relative", zIndex: 10 }}>
                <Text style={styles.greeting}>Campaigns</Text>
                <Text style={styles.subtitle}>
                  Discover and join exciting fashion campaigns
                </Text>
              </View>
            </View>
            <Image
              source={require("../assets/images/home/<USER>")}
              style={styles.profileImage}
            />
          </View>
          {/* Search Bar */}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              backgroundColor: "#fff",
              borderRadius: borderRadius,
              borderWidth: 1.5,
              borderColor: borderColor,
              marginHorizontal: 24,
              marginBottom: 14,
              marginTop: 20,
              height: 54,
              shadowColor: "#000",
              shadowOpacity: 0.06,
              shadowRadius: 6,
              elevation: 3,
            }}
          >
            <Ionicons
              name="search-outline"
              size={22}
              color="#bbb"
              style={{ marginLeft: pickerPadding, marginRight: 8 }}
            />
            <TextInput
              style={{
                flex: 1,
                fontSize: pickerTextSize,
                color: pickerTextColor,
                paddingVertical: 0,
                backgroundColor: "transparent",
                paddingLeft: 0,
                paddingRight: pickerPadding,
                outlineStyle: "none",
              }}
              placeholder="Search"
              placeholderTextColor={pickerTextColor}
              value={search}
              onChangeText={setSearch}
              onFocus={() => setSearchFocused(true)}
              onBlur={() => setSearchFocused(false)}
            />
          </View>

          {/* Dropdown Filters */}
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginHorizontal: 20,
              marginBottom: 8,
            }}
          >
            {/* Brand Dropdown */}
            <View
              style={{
                flex: 1,
                backgroundColor: "#fff",
                borderRadius: borderRadius,
                borderWidth: 1.5,
                borderColor: borderColor,
                marginRight: 10,
                height: 54,
                justifyContent: "center",
                shadowColor: "#000",
                shadowOpacity: 0.06,
                shadowRadius: 6,
                elevation: 3,
                overflow: "hidden",
                paddingRight: 10,
              }}
            >
              <SelectDropdown
                options={options}
                value={brand}
                onSelect={setBrand}
                placeholder="All Brands"
              />
            </View>
            {/* Status Dropdown */}
            <View
              style={{
                flex: 1,
                backgroundColor: "#fff",
                borderRadius: borderRadius,
                borderWidth: 1.5,
                borderColor: borderColor,
                marginLeft: 10,
                height: 54,
                justifyContent: "center",
                shadowColor: "#000",
                shadowOpacity: 0.06,
                shadowRadius: 6,
                elevation: 3,
                overflow: "hidden",
                paddingRight: 10,
              }}
            >
              <SelectDropdown
                options={statusOptions}
                value={status}
                onSelect={setStatus}
                placeholder="All Status"
              />
            </View>
          </View>
          {/* Stat Cards Row - now horizontally scrollable with 3 cards */}
          <View
            style={{
              position: "relative",
            }}
          >
            <StatCardsRow />
          </View>
          {/* Featured Campaigns */}
          <View
            style={{
              position: "relative",
            }}
          >
            <FeaturedCampaignsSlider />
          </View>
          {/* Custom Cards Row */}
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginHorizontal: 24,
              marginBottom: 18,
            }}
          >
            {/* Card 1 */}
            <View style={{ flex: 1, marginRight: 8 }}>
              <CampaignImageCard
                imageSource={require("../assets/images/adidas-hoodie.jpg")}
                badgeText="Active"
                badgeColor="#7ED957"
                badgeBg="rgba(180,255,180,0.25)"
                overlayColor="rgba(255, 140, 0, 0.25)"
              />
            </View>
            {/* Card 2 */}
            <View style={{ flex: 1, marginLeft: 8 }}>
              <CampaignImageCard
                imageSource={require("../assets/images/model-wearing-beautiful-shade-clothing.jpg")}
                badgeText="Active"
                badgeColor="#7ED957"
                badgeBg="rgba(180,255,180,0.25)"
                overlayColor="rgba(255, 140, 0, 0.25)"
              />
            </View>
          </View>
        </SafeAreaView>
      </ScrollView>
    </GradientBackground>
  );
}
