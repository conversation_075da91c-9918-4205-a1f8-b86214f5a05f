import React, { useState } from "react";
import {
  View,
  Image,
  SafeAreaView,
  TouchableOpacity,
  StyleSheet,
  ImageBackground,
  Text,
  ScrollView,
} from "react-native";
import { Ionicons, MaterialIcons, Feather } from "@expo/vector-icons";
import GradientBackground from "../components/GradientBackground";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";
import Button from "../components/Button";
import StatCardsRow from "../components/StatCardsRow";
import CampaignImageCard from "../components/CampaignImageCard";
import BrandShowcaseCard from "../components/BrandShowcaseCard";
import SubmissionCard from "../components/SubmissionCard";
import AchievementCard from "../components/AchievementCard";
import ActivityCard from "../components/ActivityCard";
import mockData from "../constants/MockData";

const styles = {
  headerBackground: {
    flex: 1,
    overflow: "hidden",
  },
  headerGradient: {
    paddingTop: 16,
    paddingHorizontal: 20,
    position: "relative",
  },
  backButton: {
    marginTop: 15,
    marginBottom: 12,
  },
  profileContainer: {
    alignItems: "center",
    justifyContent: "flex-start",
    marginTop: 0,
    marginBottom: 18,
  },
  profileImage: {
    width: 110,
    height: 110,
    borderRadius: 55,
    marginBottom: 10,
  },
  nameRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 2,
  },
  name: {
    fontSize: 30,
    color: "#121212",
    marginRight: 6,
    fontFamily: Theme.typography.fontFamily.display,
  },
  verifiedIcon: {
    marginTop: 2,
  },
  username: {
    fontSize: 14,
    color: "#8800FF",
    fontFamily: Theme.typography.fontFamily.regular,
    marginBottom: 8,
    textAlign: "center",
  },
  bio: {
    fontSize: 14,
    color: "#333333",
    textAlign: "center",
    fontFamily: Theme.typography.fontFamily.regular,
    marginBottom: 12,
    paddingHorizontal: 24,
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 18,
    flexWrap: "nowrap",
    maxWidth: 440,
    alignSelf: "center",
    width: "100%",
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 4,
    flexShrink: 1,
  },
  infoText: {
    fontSize: 12,
    color: "#333333",
    marginLeft: 2,
    fontFamily: Theme.typography.fontFamily.regular,
    lineHeight: 16,
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 18,
  },
  walletButton: {
    backgroundColor: "#FF9100",
    paddingVertical: 12,
    paddingHorizontal: 22,
    borderRadius: 12,
    minWidth: 150,
    alignItems: "center",
    borderWidth: 0,
  },
  walletButtonText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 15,
    letterSpacing: 0.5,
    fontFamily: Theme.typography.fontFamily.display,
  },
  editButton: {
    backgroundColor: "#fff",
    borderColor: "#FF9100",
    borderWidth: 2,
    paddingVertical: 12,
    paddingHorizontal: 22,
    borderRadius: 12,
    minWidth: 150,
    alignItems: "center",
  },
  editButtonText: {
    color: "#FF9100",
    fontWeight: "bold",
    fontSize: 15,
    letterSpacing: 0.5,
    fontFamily: Theme.typography.fontFamily.display,
  },
};

export default function ProfileScreen({ navigation }) {
  const [activeTab, setActiveTab] = useState("Submissions");
  const [followingBrandShowcase, setFollowingBrandShowcase] = useState(false);

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <ImageBackground
      source={require("../assets/images/home/<USER>")}
      style={styles.headerBackground}
    >
      <SafeAreaView style={{ flex: 1 }}>
        <ScrollView
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.headerGradient}>
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons
                name="arrow-back"
                size={28}
                color={Colors.text.primary}
              />
            </TouchableOpacity>
          </View>
          <View style={{ paddingHorizontal: 20 }}>
            <View style={styles.profileContainer}>
              <View style={styles.nameRow}>
                <Text style={styles.name}>Alex Chen</Text>
                <MaterialIcons
                  name="verified"
                  size={16}
                  color="#00BA00"
                  style={styles.verifiedIcon}
                />
              </View>
              <Text style={styles.username}>@alexchen</Text>
              <Text style={styles.bio}>
                Fashion enthusiast and Web3 collector. Love discovering new
                brands and earning rewards through authentic experiences. 🔥
              </Text>
              <View style={styles.infoRow}>
                <View style={styles.infoItem}>
                  <Ionicons name="location-sharp" size={14} color="#FF5C5C" />
                  <Text style={styles.infoText}>San Francisco, CA</Text>
                </View>
                <View style={styles.infoItem}>
                  <Feather name="calendar" size={14} color="#FF9100" />
                  <Text style={styles.infoText}>Joined March 2023</Text>
                </View>
                <View style={styles.infoItem}>
                  <Ionicons name="earth" size={14} color="#3B82F6" />
                  <Text style={styles.infoText}>alexchen.co</Text>
                </View>
              </View>
              <View style={styles.buttonRow}>
                <View style={{ marginRight: 10 }}>
                  <Button
                    title="CONECT TO WALLET"
                    gradient={true}
                    outlined={false}
                    style={{ width: "auto", minWidth: 0 }}
                    textStyle={{
                      fontFamily: Theme.typography.fontFamily.regular,
                      fontSize: 12,
                      fontWeight: "600",
                    }}
                    buttonStyle={{
                      height: 36,
                      minHeight: 36,
                      maxHeight: 36,
                      minWidth: 0,
                      paddingHorizontal: 18,
                      overflow: "hidden",
                    }}
                  />
                </View>
                <View>
                  <Button
                    title="EDIT PROFILE"
                    gradient={false}
                    outlined={true}
                    style={{ width: "auto", minWidth: 0 }}
                    textStyle={{
                      fontFamily: Theme.typography.fontFamily.regular,
                      fontSize: 12,
                      fontWeight: "600",
                    }}
                    buttonStyle={{
                      height: 36,
                      minHeight: 36,
                      maxHeight: 36,
                      minWidth: 0,
                      paddingHorizontal: 18,
                      overflow: "hidden",
                    }}
                  />
                </View>
              </View>
            </View>
          </View>
          <View style={{ marginTop: -36 }}>
            <StatCardsRow />
          </View>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              paddingHorizontal: 16,
              paddingVertical: 6,
            }}
          >
            <TouchableOpacity
              style={[
                {
                  borderWidth: 2,
                  borderColor:
                    activeTab === "Submissions" ? "#FF8A00" : "transparent",
                  borderRadius: 10,
                  backgroundColor: "#fff",
                  alignItems: "center",
                  justifyContent: "center",
                  marginHorizontal: 4,
                  height: 44,
                  paddingVertical: 0,
                  paddingHorizontal: 8,
                  width: "32%",
                  position: "relative",
                },
              ]}
              onPress={() => setActiveTab("Submissions")}
              activeOpacity={0.85}
            >
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Image
                  source={require("../assets/images/home/<USER>")}
                  style={{
                    width: 18,
                    height: 18,
                    marginRight: 6,
                    resizeMode: "contain",
                  }}
                />
                <Text
                  style={{
                    fontSize: 12,
                    color: activeTab === "Submissions" ? "#FF8A00" : "#333",
                    fontFamily: Theme.typography.fontFamily.regular,
                    fontWeight: "400",
                  }}
                >
                  Submissions
                </Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                {
                  borderWidth: 2,
                  borderColor:
                    activeTab === "Achievements" ? "#FF8A00" : "transparent",
                  borderRadius: 10,
                  backgroundColor: "#fff",
                  alignItems: "center",
                  justifyContent: "center",
                  marginHorizontal: 4,
                  height: 44,
                  paddingVertical: 0,
                  paddingHorizontal: 8,
                  width: "36%",
                  position: "relative",
                },
              ]}
              onPress={() => setActiveTab("Achievements")}
              activeOpacity={0.85}
            >
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Image
                  source={require("../assets/images/home/<USER>")}
                  style={{
                    width: 18,
                    height: 18,
                    marginRight: 6,
                    resizeMode: "contain",
                  }}
                />
                <Text
                  style={{
                    fontSize: 12,
                    color: activeTab === "Achievements" ? "#FF8A00" : "#333",
                    fontFamily: Theme.typography.fontFamily.regular,
                    fontWeight: "400",
                  }}
                >
                  Achievements
                </Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                {
                  borderWidth: 2,
                  borderColor:
                    activeTab === "Activity" ? "#FF8A00" : "transparent",
                  borderRadius: 10,
                  backgroundColor: "#fff",
                  alignItems: "center",
                  justifyContent: "center",
                  marginHorizontal: 4,
                  height: 44,
                  paddingVertical: 0,
                  paddingHorizontal: 8,
                  width: "28%",
                  position: "relative",
                },
              ]}
              onPress={() => setActiveTab("Activity")}
              activeOpacity={0.85}
            >
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Image
                  source={require("../assets/images/home/<USER>")}
                  style={{
                    width: 18,
                    height: 18,
                    marginRight: 6,
                    resizeMode: "contain",
                  }}
                />
                <Text
                  style={{
                    fontSize: 12,
                    color: activeTab === "Activity" ? "#FF8A00" : "#333",
                    fontFamily: Theme.typography.fontFamily.regular,
                    fontWeight: "400",
                  }}
                >
                  Activity
                </Text>
              </View>
            </TouchableOpacity>
          </View>
          {activeTab === "Submissions" && (
            <View
              style={{ marginHorizontal: 24, marginTop: 8, marginBottom: 18 }}
            >
              <SubmissionCard
                backgroundImage={require("../assets/images/3d-shoe-fire-with-flames.jpg")}
                title="Nike Web3 Collection"
                tokens="500 Fashion Tokens"
                date="2024-01-15"
                badgeText="Approved"
                badgeColor="#4AC96B"
                badgeBg="rgba(180,255,180,0.25)"
                tokenColor="#FF7700"
              />
            </View>
          )}
          {activeTab === "Achievements" && (
            <View style={{ padding: 16, paddingTop: 24 }}>
              {mockData.achievements.map((item) => (
                <AchievementCard
                  key={item.id}
                  icon={item.icon}
                  title={item.title}
                  description={item.description}
                  type={item.type}
                  typeColor={item.typeColor}
                  typeBorder={item.typeBorder}
                  typeBg={item.typeBg}
                />
              ))}
            </View>
          )}
          {activeTab === "Activity" && (
            <View style={{ padding: 16, paddingTop: 24 }}>
              {mockData.activity.map((item) => (
                <ActivityCard
                  key={item.id}
                  icon={item.icon}
                  iconBg={item.iconBg}
                  title={item.title}
                  subtitle={item.subtitle}
                  timeAgo={item.timeAgo}
                />
              ))}
            </View>
          )}
        </ScrollView>
      </SafeAreaView>
    </ImageBackground>
  );
}
