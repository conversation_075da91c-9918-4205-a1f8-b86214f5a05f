import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  TouchableOpacity,
  Platform,
  ImageBackground,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { StatusBar } from "expo-status-bar";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";

const WelcomeScreen = ({ navigation }) => {
  const handleGetStarted = () => {
    navigation.navigate("Welcome2");
  };
  const handleLogin = () => {
    navigation.navigate("Login");
  };

  return (
    <ImageBackground
      source={require("../assets/images/home/<USER>")}
      style={styles.background}
    >
      <StatusBar style="light" />
      <SafeAreaView style={styles.container}>
        <View style={styles.flexGrow} />
        <View style={styles.popupWrapper}>
          <ImageBackground
            source={require("../assets/images/home/<USER>")}
            style={styles.popupBg}
          >
            <View style={styles.popupContent}>
              <Text style={styles.title}>Welcome to GSM!</Text>
              <Text style={styles.subtitle}>
                Earn rewards by wearing outfits and clicking pics.
              </Text>
              <TouchableOpacity
                onPress={handleGetStarted}
                style={styles.buttonWrapper}
              >
                <View style={styles.button}>
                  <LinearGradient
                    colors={["#FF7A00", "#A259FF"]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.buttonGradient}
                  >
                    <Text style={styles.buttonText}>GET STARTED</Text>
                  </LinearGradient>
                </View>
              </TouchableOpacity>
              <View style={styles.loginRow}>
                <Text style={styles.loginText}>Already have an account? </Text>
                <TouchableOpacity onPress={handleLogin}>
                  <Text style={styles.loginLink}>Login</Text>
                </TouchableOpacity>
              </View>
              <Text style={styles.copyright}>
                © 2025 Get Style Money (GSM). All Rights Reserved.
              </Text>
            </View>
          </ImageBackground>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "flex-end",
  },
  flexGrow: {
    flex: 1,
  },
  popupWrapper: {
    borderRadius: 28,
    marginHorizontal: 0,
    marginBottom: Platform.OS === "ios" ? 28 : 20,
    alignSelf: "center",
    width: "94%",
    overflow: "hidden",
    elevation: 10,
    backgroundColor: "white",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.18,
    shadowRadius: 8,
  },
  popupBg: {
    width: "100%",
    paddingTop: 28,
    paddingBottom: 20,
    alignItems: "center",
    borderRadius: 28,
  },
  popupContent: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 24,
  },
  title: {
    fontFamily: Theme.typography.fontFamily.display, // Bakbak One
    fontSize: 28,
    fontWeight: "normal",
    color: "#121212",
    marginBottom: 4,
    textAlign: "center",
  },
  subtitle: {
    fontFamily: Theme.typography.fontFamily.regular, // Lexend
    fontSize: 15,
    color: "#333333",
    marginBottom: 18,
    textAlign: "center",
    opacity: 0.95,
  },
  buttonWrapper: {
    width: "100%",
    marginBottom: 12,
  },
  button: {
    borderRadius: 16,
    overflow: "hidden",
    width: "100%",
  },
  buttonGradient: {
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: "center",
    width: "100%",
  },
  buttonText: {
    fontFamily: Theme.typography.fontFamily.regular, // Lexend Bold
    color: "#fff",
    // fontWeight: "bold",
    fontSize: 16,
    letterSpacing: 1,
  },
  loginRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  loginText: {
    fontFamily: Theme.typography.fontFamily.regular, // Lexend
    color: "black",
    fontSize: 14,
    opacity: 0.9,
  },
  loginLink: {
    fontFamily: Theme.typography.fontFamily.bold, // Lexend Bold
    color: Colors.primary.end,
    fontSize: Theme.typography.fontSize.small,
    fontWeight: "bold",
  },
  copyright: {
    fontFamily: Theme.typography.fontFamily.regular, // Lexend
    color: "#333333",
    fontSize: 12,
    marginTop: 8,
    textAlign: "center",
  },
  background: {
    flex: 1,
  },
});

export default WelcomeScreen;
