import React, { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  TouchableOpacity, 
  SafeAreaView,
  Image,
  StatusBar
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Colors from '../constants/Colors';
import Theme from '../constants/Theme';
import Button from '../components/Button';

const CampaignDetailsScreen = ({ navigation, route }) => {
  const { campaign } = route.params;
  const [isJoined, setIsJoined] = useState(false);
  
  const handleBack = () => {
    navigation.goBack();
  };
  
  const handleJoinCampaign = () => {
    setIsJoined(true);
    // In a real app, you would make an API call to join the campaign
  };
  
  const renderReward = (reward, index) => (
    <View key={index} style={styles.rewardItem}>
      <View style={styles.rewardIconContainer}>
        <Ionicons name="gift-outline" size={24} color={Colors.primary.end} />
      </View>
      <View style={styles.rewardContent}>
        <Text style={styles.rewardTitle}>{reward.title}</Text>
        <Text style={styles.rewardDescription}>{reward.description}</Text>
      </View>
    </View>
  );
  
  const renderRequirement = (requirement, index) => (
    <View key={index} style={styles.requirementItem}>
      <View style={styles.requirementIconContainer}>
        <Ionicons name="checkmark-circle-outline" size={24} color={Colors.primary.end} />
      </View>
      <Text style={styles.requirementText}>{requirement}</Text>
    </View>
  );
  
  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.imageContainer}>
          <Image 
            source={campaign.image ? { uri: campaign.image } : require('../assets/images/placeholder.jpg')}
            style={styles.image}
            resizeMode="cover"
          />
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.8)']}
            style={styles.gradient}
          />
          <SafeAreaView style={styles.header}>
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons name="arrow-back" size={24} color={Colors.accent.white} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.shareButton}>
              <Ionicons name="share-outline" size={24} color={Colors.accent.white} />
            </TouchableOpacity>
          </SafeAreaView>
          <View style={styles.campaignInfo}>
            <Text style={styles.brand}>{campaign.brand}</Text>
            <Text style={styles.title}>{campaign.title}</Text>
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Ionicons name="people-outline" size={16} color={Colors.accent.white} />
                <Text style={styles.statText}>{campaign.participants} Participants</Text>
              </View>
              <View style={styles.statItem}>
                <Ionicons name="time-outline" size={16} color={Colors.accent.white} />
                <Text style={styles.statText}>{campaign.duration}</Text>
              </View>
            </View>
          </View>
        </View>
        
        <View style={styles.content}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About Campaign</Text>
            <Text style={styles.description}>{campaign.description || 'Join this exciting campaign to earn rewards and tokens while promoting amazing products!'}</Text>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Rewards</Text>
            <View style={styles.rewardsList}>
              {campaign.rewards ? (
                campaign.rewards.map(renderReward)
              ) : (
                [
                  { title: 'GSM Tokens', description: 'Earn up to 500 GSM tokens for completing all tasks' },
                  { title: 'Exclusive Discount', description: '25% off on all products from this brand' },
                  { title: 'VIP Access', description: 'Early access to new product launches' }
                ].map(renderReward)
              )}
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Requirements</Text>
            <View style={styles.requirementsList}>
              {campaign.requirements ? (
                campaign.requirements.map(renderRequirement)
              ) : (
                [
                  'Follow brand on social media',
                  'Share campaign with friends',
                  'Post product review',
                  'Create content featuring product'
                ].map(renderRequirement)
              )}
            </View>
          </View>
          
          <View style={styles.joinSection}>
            {isJoined ? (
              <View style={styles.joinedContainer}>
                <View style={styles.joinedIconContainer}>
                  <Ionicons name="checkmark-circle" size={40} color={Colors.success} />
                </View>
                <Text style={styles.joinedTitle}>You've Joined This Campaign!</Text>
                <Text style={styles.joinedDescription}>
                  Check your tasks in the dashboard to start earning rewards.
                </Text>
                <Button 
                  title="VIEW TASKS" 
                  onPress={() => navigation.navigate('DashboardTab')}
                  style={styles.viewTasksButton}
                />
              </View>
            ) : (
              <Button 
                title="JOIN CAMPAIGN" 
                onPress={handleJoinCampaign}
                style={styles.joinButton}
              />
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.light,
  },
  imageContainer: {
    height: 300,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 160,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: Theme.spacing.large,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  shareButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  campaignInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: Theme.spacing.large,
  },
  brand: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.accent.white,
    opacity: 0.9,
    marginBottom: Theme.spacing.tiny,
  },
  title: {
    fontSize: Theme.typography.fontSize.xlarge,
    fontWeight: 'bold',
    color: Colors.accent.white,
    marginBottom: Theme.spacing.medium,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: Theme.spacing.large,
  },
  statText: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.accent.white,
    marginLeft: Theme.spacing.tiny,
  },
  content: {
    padding: Theme.spacing.large,
  },
  section: {
    marginBottom: Theme.spacing.xlarge,
  },
  sectionTitle: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: Theme.spacing.medium,
  },
  description: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.secondary,
    lineHeight: 22,
  },
  rewardsList: {
    marginTop: Theme.spacing.small,
  },
  rewardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background.light,
    borderRadius: Theme.borderRadius.medium,
    padding: Theme.spacing.medium,
    marginBottom: Theme.spacing.medium,
    ...Theme.shadows.small,
  },
  rewardIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(138, 43, 226, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Theme.spacing.medium,
  },
  rewardContent: {
    flex: 1,
  },
  rewardTitle: {
    fontSize: Theme.typography.fontSize.medium,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: Theme.spacing.tiny,
  },
  rewardDescription: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.text.secondary,
  },
  requirementsList: {
    marginTop: Theme.spacing.small,
  },
  requirementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Theme.spacing.medium,
  },
  requirementIconContainer: {
    marginRight: Theme.spacing.medium,
  },
  requirementText: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.secondary,
  },
  joinSection: {
    marginTop: Theme.spacing.medium,
    marginBottom: Theme.spacing.xlarge,
  },
  joinButton: {
    marginTop: Theme.spacing.medium,
  },
  joinedContainer: {
    alignItems: 'center',
    backgroundColor: Colors.background.light,
    borderRadius: Theme.borderRadius.large,
    padding: Theme.spacing.large,
    ...Theme.shadows.medium,
  },
  joinedIconContainer: {
    marginBottom: Theme.spacing.medium,
  },
  joinedTitle: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginBottom: Theme.spacing.small,
    textAlign: 'center',
  },
  joinedDescription: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Theme.spacing.large,
  },
  viewTasksButton: {
    width: '100%',
  },
});

export default CampaignDetailsScreen;