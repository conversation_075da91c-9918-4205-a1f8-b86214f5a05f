import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  Image,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import GradientBackground from '../components/GradientBackground';
import Button from '../components/Button';
import Colors from '../constants/Colors';
import Theme from '../constants/Theme';
import { useAuth } from '../contexts/AuthContext';

const OTPVerificationScreen = ({ navigation, route }) => {
  console.log('🎬 OTPVerificationScreen loaded');
  console.log('📧 Route params:', route.params);

  const { verifyOtp, resendOtp, isLoading } = useAuth();
  const { email, message } = route.params || {};

  console.log('📧 Email from params:', email);
  console.log('💬 Message from params:', message);

  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [resendTimer, setResendTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);

  const inputRefs = useRef([]);

  useEffect(() => {
    console.log('⏰ OTPVerificationScreen useEffect started');

    // Start countdown timer
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      console.log('🧹 OTPVerificationScreen cleanup');
      clearInterval(timer);
    };
  }, []);

  const handleOtpChange = (value, index) => {
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (e, index) => {
    // Handle backspace
    if (e.nativeEvent.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOtp = async () => {
    const otpCode = otp.join('');
    
    if (otpCode.length !== 6) {
      Alert.alert('Error', 'Please enter the complete 6-digit code');
      return;
    }

    try {
      const result = await verifyOtp(email, otpCode);
      
      if (result.success) {
        Alert.alert(
          'Success',
          'Email verified successfully! Welcome to GSM Fashion Portal.',
          [
            {
              text: 'Continue',
              onPress: () => {
                // Navigation will be handled by AuthContext state change
                // The app will automatically navigate to the main app
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Invalid verification code');
      }
    } catch (error) {
      Alert.alert('Error', 'Verification failed. Please try again.');
    }
  };

  const handleResendOtp = async () => {
    if (!canResend) return;

    try {
      const result = await resendOtp(email);
      
      if (result.success) {
        Alert.alert('Success', 'Verification code sent successfully!');
        setResendTimer(60);
        setCanResend(false);
        setOtp(['', '', '', '', '', '']);
        
        // Restart timer
        const timer = setInterval(() => {
          setResendTimer((prev) => {
            if (prev <= 1) {
              setCanResend(true);
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        Alert.alert('Error', result.error || 'Failed to resend code');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to resend code. Please try again.');
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <GradientBackground>
      <StatusBar style="light" />
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}
        >
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
          </TouchableOpacity>

          <Image
            source={require('../assets/icon.png')}
            style={styles.logo}
            resizeMode="contain"
          />

          <Text style={styles.title}>Verify Your Email</Text>
          <Text style={styles.subtitle}>
            We've sent a 6-digit verification code to
          </Text>
          <Text style={styles.email}>{email}</Text>
          
          {message && (
            <Text style={styles.message}>{message}</Text>
          )}

          <View style={styles.otpContainer}>
            {otp.map((digit, index) => (
              <TextInput
                key={index}
                ref={(ref) => (inputRefs.current[index] = ref)}
                style={[
                  styles.otpInput,
                  digit ? styles.otpInputFilled : null,
                ]}
                value={digit}
                onChangeText={(value) => handleOtpChange(value, index)}
                onKeyPress={(e) => handleKeyPress(e, index)}
                keyboardType="numeric"
                maxLength={1}
                textAlign="center"
                selectTextOnFocus
              />
            ))}
          </View>

          <Button
            title={isLoading ? 'VERIFYING...' : 'VERIFY EMAIL'}
            onPress={handleVerifyOtp}
            disabled={isLoading || otp.join('').length !== 6}
            style={styles.verifyButton}
          />

          <View style={styles.resendContainer}>
            <Text style={styles.resendText}>
              Didn't receive the code?{' '}
            </Text>
            <TouchableOpacity
              onPress={handleResendOtp}
              disabled={!canResend || isLoading}
            >
              <Text
                style={[
                  styles.resendLink,
                  (!canResend || isLoading) && styles.resendLinkDisabled,
                ]}
              >
                {canResend ? 'Resend Code' : `Resend in ${resendTimer}s`}
              </Text>
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
    paddingHorizontal: Theme.spacing.large,
    justifyContent: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: Theme.spacing.large,
    zIndex: 1,
  },
  logo: {
    width: 80,
    height: 80,
    alignSelf: 'center',
    marginBottom: Theme.spacing.large,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.text.primary,
    textAlign: 'center',
    marginBottom: Theme.spacing.small,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Theme.spacing.small,
  },
  email: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary.start,
    textAlign: 'center',
    marginBottom: Theme.spacing.medium,
  },
  message: {
    fontSize: 14,
    color: Colors.text.secondary,
    textAlign: 'center',
    marginBottom: Theme.spacing.medium,
    fontStyle: 'italic',
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: Theme.spacing.large,
    paddingHorizontal: Theme.spacing.medium,
  },
  otpInput: {
    width: 45,
    height: 55,
    borderWidth: 2,
    borderColor: Colors.border.light,
    borderRadius: Theme.borderRadius.medium,
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text.primary,
    backgroundColor: Colors.background.light,
  },
  otpInputFilled: {
    borderColor: Colors.primary.start,
    backgroundColor: Colors.background.light,
  },
  verifyButton: {
    marginTop: Theme.spacing.large,
  },
  resendContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: Theme.spacing.large,
  },
  resendText: {
    fontSize: 14,
    color: Colors.text.secondary,
  },
  resendLink: {
    fontSize: 14,
    color: Colors.primary.start,
    fontWeight: 'bold',
  },
  resendLinkDisabled: {
    color: Colors.text.tertiary,
  },
});

export default OTPVerificationScreen;
