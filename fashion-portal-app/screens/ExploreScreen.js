import { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  ScrollView,
  Platform,
  Image,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import { StatusBar } from "expo-status-bar";
import { Ionicons } from "@expo/vector-icons";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";
import GradientBackground from "../components/GradientBackground";
import FeaturedCampaignsSlider from "../components/FeaturedCampaignsSlider";
import CampaignImageCard from "../components/CampaignImageCard";
import BrandShowcaseCard from "../components/BrandShowcaseCard";
import SelectDropdown from "../components/SelectDropdown";

const TabButton = ({ title, isActive, onPress, src }) => {
  return (
    <TouchableOpacity
      style={[
        styles.tabButton,
        isActive ? styles.tabButtonActive : styles.tabButtonInactive,
        isActive && styles.tabButtonActiveBorder,
      ]}
      onPress={onPress}
      activeOpacity={0.85}
    >
      <View style={styles.tabButtonContent}>
        <Image source={src} style={{ width: 18, height: 18 }} />
        <Text
          style={[styles.tabButtonText, isActive && styles.tabButtonTextActive]}
        >
          {title}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const ExploreScreen = ({ navigation }) => {
  const [search, setSearch] = useState("");
  const [searchFocused, setSearchFocused] = useState(false);
  const [brand, setBrand] = useState("All Items");
  const [status, setStatus] = useState("All Prices");
  const [activeTab, setActiveTab] = useState("Products");

  // UI constants
  const borderColor = "#D5B9A0";
  const borderRadius = 10;
  const pickerTextColor = "#666666";
  const pickerTextSize = 14;
  const pickerArrowColor = "#FF7700";
  const pickerArrowSize = 26;
  const pickerPadding = 20;

  // Custom dropdown icon for Android only
  const CustomDropdownIcon = () =>
    Platform.OS === "android" ? (
      <Ionicons
        name="chevron-down"
        size={pickerArrowSize}
        color={pickerArrowColor}
        style={{
          marginRight: pickerPadding,
        }}
      />
    ) : null;
  // Track following state

  const [followingBrandShowcase, setFollowingBrandShowcase] = useState(false);

  const handleBack = () => {
    navigation.goBack();
  };

  const brandOptions = [
    { label: "All Items", value: "All Items" },
    { label: "Nike", value: "Nike" },
    { label: "Adidas", value: "Adidas" },
    { label: "Puma", value: "Puma" },
  ];
  const priceOptions = [
    { label: "All Prices", value: "All Prices" },
    { label: "Active", value: "Active" },
    { label: "Upcoming", value: "Upcoming" },
  ];

  return (
    <GradientBackground>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <StatusBar style="dark" />
        <SafeAreaView style={styles.safeArea}>
          <View style={styles.headerGradient}>
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <Ionicons
                name="arrow-back"
                size={28}
                color={Colors.text.primary}
              />
            </TouchableOpacity>
            <View style={styles.headerRow}>
              <View style={{ flex: 1, position: "relative", zIndex: 10 }}>
                <Text style={styles.greeting}>Marketplace</Text>
                <Text style={styles.subtitle}>
                  Discover and trade fashion items with crypto
                </Text>
              </View>
            </View>
            <Image
              source={require("../assets/images/home/<USER>")}
              style={styles.profileImage}
            />
          </View>

          {/* Search Bar */}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              backgroundColor: "#fff",
              borderRadius: borderRadius,
              borderWidth: 1.5,
              borderColor: borderColor,
              marginHorizontal: 20,
              marginBottom: 14,
              marginTop: 20,
              height: 54,
              shadowColor: "#000",
              shadowOpacity: 0.06,
              shadowRadius: 6,
              elevation: 3,
            }}
          >
            <Ionicons
              name="search-outline"
              size={22}
              color="#bbb"
              style={{ marginLeft: pickerPadding, marginRight: 8 }}
            />
            <TextInput
              style={{
                flex: 1,
                fontSize: pickerTextSize,
                color: pickerTextColor,
                paddingVertical: 0,
                backgroundColor: "transparent",
                paddingLeft: 0,
                paddingRight: pickerPadding,
                outlineStyle: "none",
              }}
              placeholder="Search"
              placeholderTextColor={pickerTextColor}
              value={search}
              onChangeText={setSearch}
              onFocus={() => setSearchFocused(true)}
              onBlur={() => setSearchFocused(false)}
            />
          </View>

          {/* Dropdown Filters */}
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              marginHorizontal: 20,
              marginBottom: 8,
            }}
          >
            {/* Brand Dropdown */}
            <View
              style={{
                flex: 1,
                backgroundColor: "#fff",
                borderRadius: borderRadius,
                borderWidth: 1.5,
                borderColor: borderColor,
                marginRight: 10,
                height: 54,
                justifyContent: "center",
                shadowColor: "#000",
                shadowOpacity: 0.06,
                shadowRadius: 6,
                elevation: 3,
                overflow: "hidden",
                paddingRight: 10,
              }}
            >
              <SelectDropdown
                options={brandOptions}
                value={brand}
                onSelect={setBrand}
                placeholder="Select Brand"
              />
            </View>
            {/* Status Dropdown */}
            <View
              style={{
                flex: 1,
                backgroundColor: "#fff",
                borderRadius: borderRadius,
                borderWidth: 1.5,
                borderColor: borderColor,
                marginLeft: 10,
                height: 54,
                justifyContent: "center",
                shadowColor: "#000",
                shadowOpacity: 0.06,
                shadowRadius: 6,
                elevation: 3,
                overflow: "hidden",
                paddingRight: 10,
              }}
            >
              <SelectDropdown
                options={priceOptions}
                value={status}
                onSelect={setStatus}
                placeholder="Select Status"
              />
            </View>
          </View>

          <View style={styles.tabsContainer}>
            <TabButton
              src={require("../assets/images/sell.svg")}
              title="Products"
              isActive={activeTab === "Products"}
              onPress={() => setActiveTab("Products")}
              icon="cube-outline"
            />
            <TabButton
              src={require("../assets/images/brand.svg")}
              title="Brands"
              isActive={activeTab === "Brands"}
              onPress={() => setActiveTab("Brands")}
              icon="color-palette-outline"
            />
          </View>

          {activeTab === "Products" ? (
            <View>
              {/* Featured Campaigns */}
              <FeaturedCampaignsSlider />
              {/* Two CampaignImageCards in a row, styled like CampaignsScreen */}
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  marginHorizontal: 24,
                  marginBottom: 18,
                }}
              >
                <View style={{ flex: 1, marginRight: 8 }}>
                  <CampaignImageCard
                    imageSource={require("../assets/images/adidas-hoodie.jpg")}
                    badgeText="Active"
                    badgeColor="#7ED957"
                    badgeBg="rgba(180,255,180,0.25)"
                    overlayColor="rgba(255, 140, 0, 0.25)"
                  />
                </View>
                <View style={{ flex: 1, marginLeft: 8 }}>
                  <CampaignImageCard
                    imageSource={require("../assets/images/model-wearing-beautiful-shade-clothing.jpg")}
                    badgeText="Active"
                    badgeColor="#7ED957"
                    badgeBg="rgba(180,255,180,0.25)"
                    overlayColor="rgba(255, 140, 0, 0.25)"
                  />
                </View>
              </View>
            </View>
          ) : (
            <View style={{ marginHorizontal: 20, marginBottom: 18 }}>
              {/* Brand Showcase Card */}
              <View style={{ marginBottom: 18 }}>
                <BrandShowcaseCard
                  backgroundImage={require("../assets/images/3d-shoe-fire-with-flames.jpg")}
                  logo={require("../assets/images/nike.png")}
                  name="Nike"
                  description="Leading athletic wear brand pioneering Web3 fashion experiences"
                  followers={15420}
                  campaigns={5}
                  isFollowing={followingBrandShowcase}
                  onFollow={() => setFollowingBrandShowcase((f) => !f)}
                />
              </View>
              <View style={{ marginBottom: 18 }}>
                <BrandShowcaseCard
                  backgroundImage={require("../assets/images/brand.jpg")}
                  logo={require("../assets/images/nike.png")}
                  name="Nike"
                  description="Leading athletic wear brand pioneering Web3 fashion experiences"
                  followers={15420}
                  campaigns={5}
                  isFollowing={followingBrandShowcase}
                  onFollow={() => setFollowingBrandShowcase((f) => !f)}
                />
              </View>
            </View>
          )}
        </SafeAreaView>
      </ScrollView>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  scrollContent: {
    paddingBottom: 32,
  },
  headerGradient: {
    paddingTop: 16,
    // marginBottom: 16,
    paddingHorizontal: 20,
    position: "relative",
  },
  headerRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 18,
  },
  greeting: {
    fontFamily: Theme.typography.fontFamily.display,
    fontSize: Theme.typography.fontSize.title,
    fontWeight: "normal",
    color: Colors.text.primary,
    textAlign: "left",
    // marginTop: 5,
    marginBottom: 4,
  },
  subtitle: {
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.primary,
    textAlign: "left",
    paddingRight: 70,
    // marginBottom: 30,
  },
  profileImage: {
    position: "absolute",
    right: -60,
    width: 240,
    height: 240,
    top: -30,
    // borderWidth: 2,
    // borderColor: "black",
  },
  backButton: {
    marginTop: 15,
    marginBottom: 12,
  },

  safeArea: {
    flex: 1,
    padding: Theme.spacing.large,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Theme.spacing.large,
  },
  title: {
    fontSize: Theme.typography.fontSize.xxlarge,
    fontWeight: "bold",
    color: Colors.text.primary,
  },
  filterIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.light,
    alignItems: "center",
    justifyContent: "center",
    ...Theme.shadows.small,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.background.light,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    borderRadius: Theme.borderRadius.medium,
    marginBottom: Theme.spacing.large,
    height: 50,
    ...Theme.shadows.small,
  },
  searchIcon: {
    marginHorizontal: Theme.spacing.medium,
  },
  searchInput: {
    flex: 1,
    height: "100%",
    color: Colors.text.primary,
    fontSize: Theme.typography.fontSize.medium,
  },
  clearButton: {
    padding: Theme.spacing.medium,
  },
  tabsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "transparent",
    marginBottom: 24,
    marginTop: 20,
    paddingHorizontal: 20,
    gap: 6,
  },
  tabButton: {
    flex: 1,
    borderWidth: 2,
    borderColor: "transparent",
    borderRadius: 10,
    backgroundColor: "#fff",
    paddingVertical: 16,
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 4,
    shadowColor: "#000",
    shadowOpacity: 0.06,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
    width: "100%",
    height: 56, // fixed height for all tabs
  },
  tabButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 4,
  },
  tabButtonActive: {
    backgroundColor: "#fff",
  },
  tabButtonInactive: {
    backgroundColor: "#fff",
  },
  tabButtonActiveBorder: {
    borderColor: "#FF7700",
    borderWidth: 2,
    // Optionally, for a gradient border, use a wrapper View with a gradient background
    // and padding, but here we use a solid color for simplicity
    shadowColor: "#FF7700",
    shadowOpacity: 0.12,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 2 },
  },
  tabButtonText: {
    fontSize: 14,
    color: "#222",
    fontFamily: Theme.typography.fontFamily.regular,
  },
  tabButtonTextActive: {
    color: "#FF7700",
    // fontWeight: "700",
  },
  tabButtonIndicator: {
    position: "absolute",
    bottom: 0,
    width: 60,
    height: 3,
    backgroundColor: Colors.primary.end,
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },

  brandsContainer: {
    paddingBottom: Theme.spacing.large,
  },
  brandCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.background.light,
    borderRadius: Theme.borderRadius.large,
    padding: Theme.spacing.medium,
    marginBottom: Theme.spacing.medium,
    ...Theme.shadows.small,
  },
  brandImageContainer: {
    marginRight: Theme.spacing.medium,
  },
  brandImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "rgba(138, 43, 226, 0.1)",
    alignItems: "center",
    justifyContent: "center",
  },
  brandContent: {
    flex: 1,
  },
  brandName: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: "bold",
    color: Colors.text.primary,
    marginBottom: Theme.spacing.tiny,
  },
  brandDescription: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.text.secondary,
    marginBottom: Theme.spacing.tiny,
  },
  brandFollowers: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.primary.end,
  },
  followButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Theme.spacing.medium,
    paddingVertical: Theme.spacing.small,
    borderRadius: Theme.borderRadius.small,
    borderWidth: 1,
    borderColor: Colors.primary.end,
  },
  followButtonText: {
    fontSize: Theme.typography.fontSize.small,
    fontWeight: "bold",
    color: Colors.primary.end,
  },
  followingButton: {
    backgroundColor: "rgba(138, 43, 226, 0.1)",
  },
  followingButtonText: {
    color: Colors.primary.end,
  },
  checkIcon: {
    marginLeft: Theme.spacing.tiny,
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: Theme.spacing.xxlarge,
  },
  emptyText: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.secondary,
    marginTop: Theme.spacing.medium,
  },
});

export default ExploreScreen;
