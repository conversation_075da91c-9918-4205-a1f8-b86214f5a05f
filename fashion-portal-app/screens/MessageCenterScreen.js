import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Theme from '../constants/Theme';
import MockData from '../constants/MockData';

const MessageItem = ({ message, onPress }) => {
  const getIconName = (type) => {
    switch (type) {
      case 'token_earned':
        return 'wallet-outline';
      case 'campaign_invite':
        return 'megaphone-outline';
      case 'system':
        return 'information-circle-outline';
      default:
        return 'mail-outline';
    }
  };

  const getIconColor = (type) => {
    switch (type) {
      case 'token_earned':
        return Colors.status.success;
      case 'campaign_invite':
        return Colors.primary.end;
      case 'system':
        return Colors.status.info;
      default:
        return Colors.text.secondary;
    }
  };

  return (
    <TouchableOpacity style={styles.messageItem} onPress={() => onPress(message)}>
      <View style={styles.messageHeader}>
        <View style={styles.iconContainer}>
          <Ionicons
            name={getIconName(message.type)}
            size={20}
            color={getIconColor(message.type)}
          />
        </View>
        <View style={styles.messageContent}>
          <View style={styles.messageTitleRow}>
            <Text style={styles.messageTitle} numberOfLines={1}>
              {message.title}
            </Text>
            <Text style={styles.messageDate}>
              {new Date(message.date).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
              })}
            </Text>
          </View>
          <Text style={styles.messageText} numberOfLines={2}>
            {message.message}
          </Text>
        </View>
        {!message.read && <View style={styles.unreadIndicator} />}
      </View>
    </TouchableOpacity>
  );
};

const MessageCenterScreen = ({ navigation }) => {
  const { alerts } = MockData;

  const handleMessagePress = (message) => {
    console.log('Message pressed:', message.title);
    // Here you would typically navigate to a detailed message view
    // or mark the message as read
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const renderMessage = ({ item }) => (
    <MessageItem message={item} onPress={handleMessagePress} />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="mail-outline" size={64} color={Colors.text.tertiary} />
      <Text style={styles.emptyTitle}>No Messages</Text>
      <Text style={styles.emptyText}>
        You'll see notifications and messages here when you receive them.
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Ionicons name="arrow-back" size={24} color={Colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Message Center</Text>
          <View style={styles.headerRight} />
        </View>

        <FlatList
          data={alerts}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
        />
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.light,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Theme.spacing.large,
    paddingVertical: Theme.spacing.medium,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border.light,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background.light,
    alignItems: 'center',
    justifyContent: 'center',
    ...Theme.shadows.small,
  },
  headerTitle: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  headerRight: {
    width: 40,
  },
  listContent: {
    padding: Theme.spacing.large,
  },
  messageItem: {
    backgroundColor: Colors.background.light,
    borderRadius: Theme.borderRadius.large,
    marginBottom: Theme.spacing.medium,
    ...Theme.shadows.small,
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: Theme.spacing.medium,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(138, 43, 226, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Theme.spacing.medium,
  },
  messageContent: {
    flex: 1,
  },
  messageTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.tiny,
  },
  messageTitle: {
    flex: 1,
    fontSize: Theme.typography.fontSize.medium,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginRight: Theme.spacing.small,
  },
  messageDate: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.text.secondary,
  },
  messageText: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.secondary,
    lineHeight: 20,
  },
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.status.error,
    marginLeft: Theme.spacing.small,
    marginTop: Theme.spacing.tiny,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Theme.spacing.xxlarge,
  },
  emptyTitle: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: 'bold',
    color: Colors.text.primary,
    marginTop: Theme.spacing.medium,
    marginBottom: Theme.spacing.small,
  },
  emptyText: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.secondary,
    textAlign: 'center',
    paddingHorizontal: Theme.spacing.large,
  },
});

export default MessageCenterScreen;
