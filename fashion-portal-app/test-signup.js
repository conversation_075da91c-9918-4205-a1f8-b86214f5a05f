// Quick test to verify the signup API call structure
import ApiService from './services/api.js';

async function testSignup() {
  console.log('Testing signup API call structure...');
  
  try {
    const result = await ApiService.register(
      '<EMAIL>',
      'password123',
      'Test User'
    );
    console.log('✅ Signup successful:', result);
  } catch (error) {
    console.log('❌ Signup error:', error.message);
  }
}

testSignup();
