import React from "react";
import { View, Image, Text, StyleSheet } from "react-native";
import Theme from "../constants/Theme";

const ActivityCard = ({ icon, iconBg, title, subtitle, timeAgo }) => {
  return (
    <View style={styles.cardContainer}>
      <View style={[styles.iconCircle, { backgroundColor: iconBg }]}>
        <Image source={icon} style={styles.icon} resizeMode="contain" />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title}>{title}</Text>
        {subtitle ? (
          <Text style={styles.subtitle}>
            {subtitle}  B7 {timeAgo}
          </Text>
        ) : (
          <Text style={styles.subtitle}>{timeAgo}</Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 20,
    paddingVertical: 16,
    paddingHorizontal: 16,
    marginBottom: 18,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.07,
    shadowRadius: 8,
    elevation: 2,
    minHeight: 72,
  },
  iconCircle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  icon: {
    width: 50,
    height: 50,
  },
  textContainer: {
    flex: 1,
    justifyContent: "center",
  },
  title: {
    fontSize: 15,
    fontFamily: Theme.typography.fontFamily.display,
    color: "#121212",
    fontWeight: "bold",
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 13,
    color: "#666",
    fontFamily: Theme.typography.fontFamily.regular,
  },
});

export default ActivityCard;
