import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";
import CompleteIcon from "../assets/images/complete.svg";

const Button = ({
  title,
  onPress,
  style,
  textStyle,
  buttonStyle,
  gradient = true,
  outlined = false,
  disabled = false,
  icon = null,
}) => {
  if (gradient && !outlined) {
    return (
      <TouchableOpacity
        onPress={onPress}
        style={[styles.container, style]}
        activeOpacity={0.8}
        disabled={disabled}
      >
        <LinearGradient
          colors={Colors.background.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          
          style={[styles.button,buttonStyle, disabled && styles.disabled]}
        >
          {icon && <View style={styles.iconContainer}>{icon}</View>}
          <Text
            style={[styles.text, textStyle]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {title}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  if (outlined) {
    return (
      <TouchableOpacity
        onPress={onPress}
        style={[styles.container, style]}
        activeOpacity={0.8}
        disabled={disabled}
      >
        <View
          style={[
            styles.button,
            styles.outlined,
            disabled && styles.disabledOutlined,
            buttonStyle,
          ]}
        >
          {icon && <View style={styles.iconContainer}>{icon}</View>}
          <Text
            style={[
              styles.text,
              styles.outlinedText,
              textStyle,
              disabled && styles.disabledText,
            ]}
          >
            {title}
          </Text>
        </View>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.container, style]}
      activeOpacity={0.8}
      disabled={disabled}
    >
      <View
        style={[
          styles.button,
          styles.solid,
          disabled && styles.disabled,
          buttonStyle,
        ]}
      >
        {icon && <View style={styles.iconContainer}>{icon}</View>}
        <Text
          style={[styles.text, textStyle]}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {title}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
  },
  button: {
    height: 50,
    borderRadius: Theme.borderRadius.large,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    paddingHorizontal: Theme.spacing.large,
  },
  text: {
    color: Colors.text.light,
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.regular,
    fontWeight: "normal",
    textAlign: "center",
  },
  solid: {
    backgroundColor: Colors.primary.end,
  },
  outlined: {
    backgroundColor: "transparent",
    borderWidth: 2,
    borderColor: Colors.primary.end,
  },
  outlinedText: {
    color: Colors.primary.end,
  },
  disabled: {
    opacity: 0.5,
  },
  disabledOutlined: {
    borderColor: Colors.text.secondary,
    opacity: 0.5,
  },
  disabledText: {
    color: Colors.text.secondary,
  },
  iconContainer: {
    marginRight: Theme.spacing.small,
  },
});

export default Button;
