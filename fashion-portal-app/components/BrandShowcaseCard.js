import React from "react";
import { View, Text, Image, TouchableOpacity, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import Theme from "../constants/Theme";

const BrandShowcaseCard = ({
  backgroundImage,
  logo,
  name,
  description,
  followers,
  campaigns,
  onFollow,
  isFollowing,
}) => (
  <View style={styles.card}>
    <Image source={backgroundImage} style={styles.backgroundImage} />
    <View style={styles.overlay} />

    <View style={styles.info}>
      <Image source={logo} style={styles.logo} />
      <Text style={styles.name}>{name}</Text>
      <Text style={styles.description} numberOfLines={2}>
        {description}
      </Text>
      <View style={styles.statsRow}>
        <Text style={styles.followers}>👥 {followers.toLocaleString()}</Text>
        <Text style={styles.campaigns}> 📦 {campaigns} Campaign</Text>
      </View>
      <TouchableOpacity
        onPress={onFollow}
        style={styles.button}
        activeOpacity={0.85}
      >
        <LinearGradient
          colors={["#FF7700", "#A259FF"]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.gradient}
        >
          <Text style={styles.buttonText}>
            {isFollowing ? "FOLLOWING" : "FOLLOW"}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  </View>
);

const styles = StyleSheet.create({
  card: {
    borderRadius: 18,
    overflow: "hidden",
    position: "relative",
    width: "100%",
    height: 310,
    marginBottom: 16,
    backgroundColor: "#222",
  },
  backgroundImage: {
    width: "100%",
    height: "100%",
    position: "absolute",
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0,0,0,0.25)",
  },
  logo: {
    // position: "absolute",
    // top: 16,
    // left: 16,
    width: 30,
    height: 30,
    borderRadius: 16,
    // backgroundColor: "#fff",
    zIndex: 2,
    resizeMode: "contain",
    borderWidth: 1,
    // borderColor: "#eee",
  },
  info: {
    position: "absolute",
    left: 16,
    bottom: 16,
    right: 16,
    zIndex: 2,
  },
  name: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 20,
    marginBottom: 2,
  },
  description: {
    color: "#fff",
    fontSize: 12,
    marginBottom: 8,
    opacity: 0.9,
  },
  statsRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
  },
  followers: {
    color: "#fff",
    fontSize: 12,
    marginRight: 8,
  },
  campaigns: {
    color: "#fff",
    fontSize: 12,
  },
  button: {
    alignSelf: "flex-end",
    borderRadius: 5,
    overflow: "hidden",
  },
  gradient: {
    paddingHorizontal: 24,
    paddingVertical: 8,
    borderRadius: 5,
  },
  buttonText: {
    color: "#fff",
    // fontWeight: "bold",
    fontSize: 14,
    letterSpacing: 1,
    fontFamily: Theme.typography.fontFamily.regular,
    fontSize: Theme.typography.fontSize.medium,
  },
});

export default BrandShowcaseCard;
