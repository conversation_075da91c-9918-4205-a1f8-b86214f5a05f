import React from 'react';
import { StyleSheet, Text, View, Image, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Theme from '../constants/Theme';
import Button from './Button';

const ProductCard = ({ 
  product, 
  onPress, 
  onFollowPress,
  style, 
  isFollowing = false,
}) => {
  const { title, tokenPrice, image, brand, category } = product;
  
  return (
    <TouchableOpacity 
      style={[styles.container, style]} 
      onPress={onPress}
      activeOpacity={0.9}
    >
      <View style={styles.card}>
        <View style={styles.imageContainer}>
          <Image 
            source={image} 
            style={styles.image}
            resizeMode="cover"
          />
          <View style={styles.tokenBadge}>
            <Text style={styles.tokenText}>{tokenPrice} Tokens</Text>
          </View>
        </View>
        
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title} numberOfLines={1}>{title}</Text>
          </View>
          
          <View style={styles.footer}>
            <View style={styles.infoContainer}>
              <View style={styles.brandContainer}>
                <Text style={styles.brand}>{brand}</Text>
              </View>
              <Text style={styles.category}>{category}</Text>
            </View>
            
            <TouchableOpacity 
              style={[
                styles.followButton, 
                isFollowing && styles.followingButton
              ]} 
              onPress={onFollowPress}
            >
              <Text style={[
                styles.followButtonText,
                isFollowing && styles.followingButtonText
              ]}>
                {isFollowing ? 'Following' : 'Follow'}
              </Text>
              {isFollowing && (
                <Ionicons 
                  name="checkmark" 
                  size={16} 
                  color={Colors.primary.end} 
                  style={styles.checkIcon}
                />
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '48%',
    borderRadius: Theme.borderRadius.large,
    overflow: 'hidden',
    marginBottom: Theme.spacing.medium,
  },
  card: {
    backgroundColor: Colors.background.light,
    borderRadius: Theme.borderRadius.large,
    overflow: 'hidden',
    ...Theme.shadows.small,
  },
  imageContainer: {
    width: '100%',
    height: 120,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  tokenBadge: {
    position: 'absolute',
    top: Theme.spacing.small,
    right: Theme.spacing.small,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: Theme.spacing.small,
    paddingVertical: Theme.spacing.tiny,
    borderRadius: Theme.borderRadius.round,
  },
  tokenText: {
    color: Colors.accent.white,
    fontSize: Theme.typography.fontSize.tiny,
    fontWeight: 'bold',
  },
  content: {
    padding: Theme.spacing.small,
  },
  header: {
    marginBottom: Theme.spacing.small,
  },
  title: {
    fontSize: Theme.typography.fontSize.medium,
    fontWeight: 'bold',
    color: Colors.text.primary,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoContainer: {
    flexDirection: 'column',
  },
  brandContainer: {
    marginBottom: Theme.spacing.tiny,
  },
  brand: {
    fontSize: Theme.typography.fontSize.small,
    fontWeight: 'bold',
    color: Colors.primary.end,
  },
  category: {
    fontSize: Theme.typography.fontSize.tiny,
    color: Colors.text.secondary,
  },
  followButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Theme.spacing.small,
    paddingVertical: Theme.spacing.tiny,
    borderRadius: Theme.borderRadius.small,
    borderWidth: 1,
    borderColor: Colors.primary.end,
  },
  followButtonText: {
    fontSize: Theme.typography.fontSize.tiny,
    fontWeight: 'bold',
    color: Colors.primary.end,
  },
  followingButton: {
    backgroundColor: 'rgba(138, 43, 226, 0.1)',
  },
  followingButtonText: {
    color: Colors.primary.end,
  },
  checkIcon: {
    marginLeft: Theme.spacing.tiny,
  },
});

export default ProductCard;