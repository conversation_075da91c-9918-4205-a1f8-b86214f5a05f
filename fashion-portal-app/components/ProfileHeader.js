import React from 'react';
import { StyleSheet, Text, View, Image, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Theme from '../constants/Theme';

const ProfileHeader = ({ 
  user, 
  onSettingsPress, 
  onEditPress,
  style 
}) => {
  const { name, rewards, submissions } = user;
  
  // Get initials for avatar placeholder
  const getInitials = (name) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };
  
  return (
    <View style={[styles.container, style]}>
      <LinearGradient
        colors={Colors.background.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <View style={styles.header}>
          <TouchableOpacity onPress={onEditPress}>
            <View style={styles.avatarContainer}>
              <View style={styles.avatar}>
                <Text style={styles.initials}>{getInitials(name)}</Text>
              </View>
              <View style={styles.editIconContainer}>
                <Ionicons name="pencil" size={14} color={Colors.accent.white} />
              </View>
            </View>
          </TouchableOpacity>
          
          <View style={styles.userInfo}>
            <Text style={styles.name}>{name}</Text>
            <View style={styles.statsContainer}>
              <View style={styles.stat}>
                <Text style={styles.statValue}>{rewards}</Text>
                <Text style={styles.statLabel}>Rewards</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.stat}>
                <Text style={styles.statValue}>{submissions}</Text>
                <Text style={styles.statLabel}>Submissions</Text>
              </View>
            </View>
          </View>
          
          <TouchableOpacity style={styles.settingsButton} onPress={onSettingsPress}>
            <Ionicons name="settings-outline" size={24} color={Colors.accent.white} />
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    overflow: 'hidden',
  },
  gradient: {
    padding: Theme.spacing.large,
    paddingTop: Theme.spacing.xxlarge + Theme.spacing.large, // Extra padding for status bar
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: Theme.spacing.large,
  },
  avatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.accent.white,
  },
  initials: {
    fontSize: Theme.typography.fontSize.xlarge,
    fontWeight: 'bold',
    color: Colors.accent.white,
  },
  editIconContainer: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.primary.start,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.accent.white,
  },
  userInfo: {
    flex: 1,
  },
  name: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: 'bold',
    color: Colors.accent.white,
    marginBottom: Theme.spacing.small,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stat: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: Theme.typography.fontSize.medium,
    fontWeight: 'bold',
    color: Colors.accent.white,
  },
  statLabel: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.accent.white,
    opacity: 0.8,
  },
  statDivider: {
    width: 1,
    height: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: Theme.spacing.medium,
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default ProfileHeader;