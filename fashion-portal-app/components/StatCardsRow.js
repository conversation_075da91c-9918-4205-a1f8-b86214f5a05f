import React from "react";
import { View, Text, ScrollView } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import Theme from "../constants/Theme";
import { Image } from "react-native";

const styles = {
  statsScrollContent: {
    flexDirection: "row",
    paddingLeft: 2,
    paddingRight: 0,
    marginTop: 18,
    marginBottom: 8,
  },
  statCardExact: {
    width: 190,
    borderRadius: 10,
    paddingVertical: 15,
    paddingHorizontal: 15,
    height: 70,
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  statLabelExact: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "400",
    marginBottom: 0,
    opacity: 0.95,
  },
  statCardBottomRow: {},
  statValueExact: {
    color: "#fff",
    fontSize: 30,
    fontWeight: "normal",
    letterSpacing: 1,
    fontFamily: Theme.typography.fontFamily.display,
  },
  statIconCircle: {
    width: 38,
    height: 38,
    borderRadius: 19,
    backgroundColor: "#fff",
    alignItems: "center",
    justifyContent: "center",
    marginLeft: 8,
  },
};

const StatCardsRow = () => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.statsScrollContent}
    >
      <LinearGradient
        colors={["#FF983D", "#FF7700"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={[styles.statCardExact, { marginRight: 10, marginLeft: 20 }]}
      >
        <View style={styles.statCardBottomRow}>
          <Text style={styles.statLabelExact}>Active Campaigns</Text>
          <Text style={styles.statValueExact}>5</Text>
        </View>
        <View style={styles.statIconCircle}>
          <Image
            source={require("../assets/images/campaigns.png")}
            style={styles.profileImage}
            height={20}
            width={20}
          />
        </View>
      </LinearGradient>
      <LinearGradient
        colors={["#BA6CFF", "#8800FF"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={[styles.statCardExact, { marginRight: 10 }]}
      >
        <View style={styles.statCardBottomRow}>
          <Text style={styles.statLabelExact}>Total Rewards</Text>
          <Text style={styles.statValueExact}>2,847</Text>
        </View>
        <View style={styles.statIconCircle}>
          <Image
            source={require("../assets/images/cup.png")}
            style={styles.profileImage}
            height={20}
            width={20}
          />
        </View>
      </LinearGradient>
      <LinearGradient
        colors={["#00C566", "#00B2A9"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.statCardExact}
      >
        <View style={styles.statCardBottomRow}>
          <Text style={styles.statLabelExact}>Completed Tasks</Text>
          <Text style={styles.statValueExact}>12</Text>
        </View>
        <View style={styles.statIconCircle}>
          <Ionicons name="checkmark-circle" size={24} color="#00C566" />
        </View>
      </LinearGradient>
    </ScrollView>
  );
};

export default StatCardsRow;
