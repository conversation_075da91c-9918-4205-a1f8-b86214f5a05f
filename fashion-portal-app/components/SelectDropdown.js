import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  FlatList,
  StyleSheet,
} from "react-native";
import Theme from "../constants/Theme";

const SelectDropdown = ({ options, value, onSelect, placeholder, style }) => {
  const [visible, setVisible] = useState(false);

  return (
    <View style={[styles.box, style]}>
      <TouchableOpacity
        style={styles.touchable}
        activeOpacity={0.8}
        onPress={() => setVisible(true)}
      >
        <Text style={styles.valueText} numberOfLines={1}>
          {value || placeholder || "Select"}
        </Text>
        <Text style={styles.arrow}>⌄</Text>
      </TouchableOpacity>
      <Modal
        visible={visible}
        transparent
        animationType="fade"
        onRequestClose={() => setVisible(false)}
      >
        <TouchableOpacity
          style={styles.overlay}
          onPress={() => setVisible(false)}
        >
          <View style={styles.dropdown}>
            <FlatList
              data={options}
              keyExtractor={(item) => item.value.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.option}
                  onPress={() => {
                    onSelect(item.value);
                    setVisible(false);
                  }}
                >
                  <Text style={styles.optionText}>{item.label}</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  box: {
    minWidth: 180,
    margin: 4,
  },
  touchable: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderRadius: 16,
    borderWidth: 3,
    borderColor: "white",
    paddingRight: 30,
    height: 70,
    minWidth: 180,
  },
  valueText: {
    color: "#666666",
    fontSize: 14,
    flex: 1,
    paddingLeft: 8,
    fontFamily: Theme.typography.fontFamily.regular,
  },
  arrow: {
    color: "#FF7700",
    fontSize: 20,
    marginLeft: 8,
    alignSelf: "center",
    marginTop: -4,
  },
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.08)",
    justifyContent: "center",
    alignItems: "center",
  },
  dropdown: {
    backgroundColor: "#fff",
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#dec6b0",
    minWidth: 220,
    maxHeight: 180,
    paddingVertical: 8,
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  option: {
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  optionText: {
    fontSize: 14,
    color: "#666666",
  },
});

export default SelectDropdown;
