import React from "react";
import { View, Image, Text, StyleSheet } from "react-native";
import Theme from "../constants/Theme";

const AchievementCard = ({
  icon,
  title,
  description,
  type,
  typeColor,
  typeBorder,
  typeBg,
}) => {
  return (
    <View style={styles.cardContainer}>
      <Image source={icon} style={styles.icon} resizeMode="contain" />
      <View style={styles.textContainer}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.description}>{description}</Text>
      </View>
      <View
        style={[
          styles.badge,
          { borderColor: typeBorder, backgroundColor: typeBg },
        ]}
      >
        <Text style={[styles.badgeText, { color: typeColor }]}>{type}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fff",
    borderRadius: 20,
    paddingVertical: 18,
    paddingHorizontal: 18,
    marginBottom: 18,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.07,
    shadowRadius: 8,
    elevation: 2,
    minHeight: 80,
  },
  icon: {
    width: 40,
    height: 40,
    marginRight: 18,
  },
  textContainer: {
    flex: 1,
    justifyContent: "center",
  },
  title: {
    fontSize: 16,
    color: "#121212",
    fontFamily: Theme.typography.fontFamily.display,
    marginBottom: 2,
  },
  description: {
    fontSize: 12,
    color: "#666666",
    fontFamily: Theme.typography.fontFamily.regular,

    marginTop: 0,
  },
  badge: {
    borderWidth: 1,
    borderRadius: 16,
    paddingHorizontal: 18,
    paddingVertical: 4,
    alignSelf: "flex-start",
    marginLeft: 12,
  },
  badgeText: {
    fontSize: 12,
    fontFamily: Theme.typography.fontFamily.regular,
    letterSpacing: 0.5,
  },
});

export default AchievementCard;
