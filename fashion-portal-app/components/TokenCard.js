import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Theme from '../constants/Theme';

const TokenCard = ({ token, onPress, style }) => {
  const { name, amount } = token;
  
  return (
    <TouchableOpacity 
      style={[styles.container, style]} 
      onPress={onPress}
      activeOpacity={0.9}
    >
      <LinearGradient
        colors={Colors.background.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <View style={styles.iconContainer}>
          <Ionicons name="wallet-outline" size={24} color={Colors.accent.white} />
        </View>
        
        <View style={styles.content}>
          <Text style={styles.amount}>{amount}</Text>
          <Text style={styles.name}>{name}</Text>
        </View>
        
        <View style={styles.arrowContainer}>
          <Ionicons name="chevron-forward" size={24} color={Colors.accent.white} />
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderRadius: Theme.borderRadius.large,
    overflow: 'hidden',
    marginBottom: Theme.spacing.medium,
    ...Theme.shadows.medium,
  },
  gradient: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Theme.spacing.medium,
    borderRadius: Theme.borderRadius.large,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Theme.spacing.medium,
  },
  content: {
    flex: 1,
  },
  amount: {
    fontSize: Theme.typography.fontSize.xlarge,
    fontWeight: 'bold',
    color: Colors.accent.white,
  },
  name: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.accent.white,
    opacity: 0.8,
  },
  arrowContainer: {
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default TokenCard;