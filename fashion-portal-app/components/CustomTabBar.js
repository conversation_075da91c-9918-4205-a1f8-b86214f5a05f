import React from "react";
import { View, TouchableOpacity, Text, Image, StyleSheet } from "react-native";
import Colors from "../constants/Colors";
import Theme from "../constants/Theme";

const icons = {
  home: {
    filled: require("../assets/images/home/<USER>"),
    outline: require("../assets/images/home/<USER>"),
  },
  explore: {
    filled: require("../assets/images/home/<USER>"),
    outline: require("../assets/images/home/<USER>"),
  },
  rewards: {
    filled: require("../assets/images/home/<USER>"),
    outline: require("../assets/images/home/<USER>"),
  },
  profile: {
    filled: require("../assets/images/home/<USER>"),
    outline: require("../assets/images/home/<USER>"),
  },
  alerts: {
    filled: require("../assets/images/home/<USER>"),
    outline: require("../assets/images/home/<USER>"),
  },
};

const tabConfig = [
  { key: "DashboardTab", label: "Home", icon: icons.home },
  { key: "ExploreTab", label: "Explore", icon: icons.explore },
  { key: "RewardsTab", label: "Rewards", icon: icons.rewards },
  { key: "ProfileTab", label: "Profile", icon: icons.profile },
  { key: "Alerts", label: "Alerts", icon: icons.alerts },
];

const CustomTabBar = ({ state, descriptors, navigation }) => {
  return (
    <View style={styles.tabBarContainer}>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const label =
          options.tabBarLabel !== undefined
            ? options.tabBarLabel
            : options.title !== undefined
            ? options.title
            : route.name;
        const isFocused = state.index === index;
        const iconSet = tabConfig.find((tab) => tab.key === route.name)?.icon;
        const icon = isFocused ? iconSet?.filled : iconSet?.outline;

        const onPress = () => {
          const event = navigation.emit({
            type: "tabPress",
            target: route.key,
            canPreventDefault: true,
          });
          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        return (
          <React.Fragment key={route.key}>
            <TouchableOpacity
              accessibilityRole="button"
              accessibilityState={isFocused ? { selected: true } : {}}
              accessibilityLabel={options.tabBarAccessibilityLabel}
              testID={options.tabBarTestID}
              onPress={onPress}
              style={styles.tabItem}
              activeOpacity={0.8}
            >
              <Image source={icon} style={styles.icon} />
              <Text style={[styles.label, isFocused && styles.labelFocused]}>
                {label}
              </Text>
            </TouchableOpacity>
            {index < state.routes.length - 1 && <View style={styles.divider} />}
          </React.Fragment>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  tabBarContainer: {
    flexDirection: "row",
    backgroundColor: "#fff",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    marginHorizontal: 16,
    marginBottom: 20,
    height: 76,
    alignItems: "center",
    justifyContent: "space-between",
    shadowColor: Colors.shadow.color,
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    overflow: "hidden",
    paddingHorizontal: 0,
  },
  tabItem: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
    paddingTop: 5,
    paddingBottom: 5,
  },
  icon: {
    width: 32,
    height: 32,
    resizeMode: "contain",
    marginBottom: 0,
    marginTop: 8,
  },
  label: {
    color: "#333333",
    fontSize: 12,
    fontFamily: Theme.typography.fontFamily.regular,
    marginTop: 14,
  },
  labelFocused: {
    color: Colors.primary.end,
  },
  divider: {
    width: 1,
    height: 36,
    backgroundColor: "#DBDBDB",
    alignSelf: "center",
    marginVertical: 0,
  },
});

export default CustomTabBar;
