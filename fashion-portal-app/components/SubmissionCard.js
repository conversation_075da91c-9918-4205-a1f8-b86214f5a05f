import React from "react";
import { View, Image, Text, StyleSheet } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import Theme from "../constants/Theme";

const SubmissionCard = ({
  backgroundImage,
  title,
  tokens,
  date,
  badgeText,
  badgeColor = "#4AC96B",
  badgeBg = "#E6F9ED",
  tokenColor = "#FF7700",
  children,
}) => {
  return (
    <View style={styles.cardContainer}>
      <Image source={backgroundImage} style={styles.image} resizeMode="cover" />
      {/* Overlay */}
      <View style={styles.overlay} />
      {/* Badge */}
      {badgeText && (
        <View
          style={[
            styles.badge,
            { borderColor: badgeColor, backgroundColor: badgeBg },
          ]}
        >
          <Text style={[styles.badgeText, { color: badgeColor }]}>
            {badgeText}
          </Text>
        </View>
      )}
      {/* Content */}
      <View style={styles.contentContainer}>
        <Text style={styles.title}>{title}</Text>
        <Text style={[styles.tokens, { color: tokenColor }]}>{tokens}</Text>
        <Text style={styles.date}>{date}</Text>
        {children}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    borderRadius: 18,
    overflow: "hidden",
    position: "relative",
    width: "100%",
    height: 310,
    backgroundColor: "#222",
    marginBottom: 16,
  },
  image: {
    width: "100%",
    height: "100%",
    position: "absolute",
    borderRadius: 18,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0,0,0,0.25)",
  },
  badge: {
    position: "absolute",
    top: 18,
    right: 18,
    borderRadius: 16,
    borderWidth: 1.5,
    paddingHorizontal: 16,
    paddingVertical: 4,
    zIndex: 2,
    alignSelf: "flex-end",
  },
  badgeText: {
    fontSize: 14,
    fontFamily: Theme.typography.fontFamily.regular,
    fontWeight: "bold",
    letterSpacing: 1,
  },
  contentContainer: {
    position: "absolute",
    left: 16,
    right: 16,
    bottom: 40,
    padding: 0,
    backgroundColor: "transparent",
    borderBottomLeftRadius: 18,
    borderBottomRightRadius: 18,
    alignItems: "flex-start",
  },
  title: {
    fontSize: 20,
    color: "#fff",
    fontFamily: Theme.typography.fontFamily.display,
    fontWeight: "bold",
    marginBottom: 0,
  },
  tokens: {
    fontSize: 14,
    fontFamily: Theme.typography.fontFamily.regular,
    fontWeight: "bold",
    marginBottom: 0,
    marginTop: 0,
  },
  date: {
    fontSize: 12,
    color: "#fff",
    opacity: 0.9,
    fontFamily: Theme.typography.fontFamily.regular,
    marginTop: 0,
  },
});

export default SubmissionCard;
