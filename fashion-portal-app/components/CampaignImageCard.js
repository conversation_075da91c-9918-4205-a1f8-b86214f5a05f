import React from "react";
import { View, Image, Text, StyleSheet } from "react-native";

const CampaignImageCard = ({
  imageSource,
  badgeText = "Active",
  badgeColor = "#7ED957",
  overlayColor = "rgba(255, 140, 0, 0.25)",
  badgeBg = "rgba(180,255,180,0.25)",
  style,
}) => {
  return (
    <View style={[styles.cardContainer, style]}>
      <Image
        source={imageSource}
        style={styles.image}
        resizeMode="cover"
      />
      {/* Orange overlay */}
      <View
        style={[
          StyleSheet.absoluteFillObject,
          { backgroundColor: overlayColor, borderRadius: 18 },
        ]}
      />
      {/* Badge */}
      <View
        style={[
          styles.badge,
          { borderColor: badgeColor, backgroundColor: badgeBg },
        ]}
      >
        <Text style={[styles.badgeText, { color: badgeColor }]}>{badgeText}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    borderRadius: 18,
    overflow: "hidden",
    position: "relative",
    width: "100%",
    height: 140,
  },
  image: {
    width: "100%",
    height: 140,
    borderRadius: 18,
  },
  badge: {
    position: "absolute",
    top: 10,
    right: 10,
    borderRadius: 16,
    borderWidth: 1.5,
    paddingHorizontal: 14,
    paddingVertical: 2,
  },
  badgeText: {
    fontWeight: "bold",
    fontSize: 14,
  },
});

export default CampaignImageCard; 