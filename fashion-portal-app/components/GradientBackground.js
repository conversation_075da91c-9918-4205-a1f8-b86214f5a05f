import { StyleSheet, View, ImageBackground } from "react-native";
// import { LinearGradient } from "expo-linear-gradient";
// import Colors from "../constants/Colors";
const GradientBackground = ({ children, style }) => {
  return (
    <View style={[styles.container, style]}>
      {/* <LinearGradient
        colors={Colors.background.gradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      > */}
      <ImageBackground
        source={require("../assets/images/home/<USER>")}
        style={styles.gradient}
      >
        {children}
      </ImageBackground>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
});
export default GradientBackground;
