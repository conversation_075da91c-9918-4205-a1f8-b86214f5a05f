import React from 'react';
import { StyleSheet, Text, View, Image, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Theme from '../constants/Theme';
import Button from './Button';

const CampaignCard = ({ 
  campaign, 
  onPress, 
  style, 
  showDetails = true,
  showJoinButton = true,
}) => {
  const { title, description, tokens, image, status, brand, endDate } = campaign;
  
  // Format date
  const formattedDate = new Date(endDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  return (
    <TouchableOpacity 
      style={[styles.container, style]} 
      onPress={onPress}
      activeOpacity={0.9}
    >
      <LinearGradient
        colors={['rgba(255,140,0,0.05)', 'rgba(138,43,226,0.1)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <View style={styles.imageContainer}>
          <Image 
            source={image} 
            style={styles.image}
            resizeMode="cover"
          />
          <View style={styles.tokenBadge}>
            <Text style={styles.tokenText}>{tokens} Tokens</Text>
          </View>
        </View>
        
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title} numberOfLines={1}>{title}</Text>
            <View style={styles.brandContainer}>
              <Text style={styles.brand}>{brand}</Text>
            </View>
          </View>
          
          {showDetails && (
            <>
              <Text style={styles.description} numberOfLines={2}>
                {description}
              </Text>
              
              <View style={styles.footer}>
                <View style={styles.statusContainer}>
                  <View style={[
                    styles.statusDot, 
                    { backgroundColor: status === 'active' ? Colors.status.success : Colors.status.warning }
                  ]} />
                  <Text style={styles.statusText}>
                    {status === 'active' ? 'Active' : 'Upcoming'}
                  </Text>
                  <Text style={styles.dateText}>Until {formattedDate}</Text>
                </View>
                
                {showJoinButton && (
                  <Button 
                    title={status === 'active' ? 'Join Now' : 'Remind Me'} 
                    onPress={onPress}
                    style={styles.button}
                    textStyle={styles.buttonText}
                    gradient={false}
                    outlined={status !== 'active'}
                  />
                )}
              </View>
            </>
          )}
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderRadius: Theme.borderRadius.large,
    overflow: 'hidden',
    marginBottom: Theme.spacing.medium,
    ...Theme.shadows.medium,
  },
  gradient: {
    borderRadius: Theme.borderRadius.large,
    overflow: 'hidden',
    backgroundColor: Colors.background.light,
  },
  imageContainer: {
    width: '100%',
    height: 150,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  tokenBadge: {
    position: 'absolute',
    top: Theme.spacing.medium,
    right: Theme.spacing.medium,
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: Theme.spacing.medium,
    paddingVertical: Theme.spacing.tiny,
    borderRadius: Theme.borderRadius.round,
  },
  tokenText: {
    color: Colors.accent.white,
    fontSize: Theme.typography.fontSize.small,
    fontWeight: 'bold',
  },
  content: {
    padding: Theme.spacing.medium,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Theme.spacing.small,
  },
  title: {
    fontSize: Theme.typography.fontSize.large,
    fontWeight: 'bold',
    color: Colors.text.primary,
    flex: 1,
  },
  brandContainer: {
    backgroundColor: Colors.primary.end,
    paddingHorizontal: Theme.spacing.small,
    paddingVertical: Theme.spacing.tiny,
    borderRadius: Theme.borderRadius.small,
    marginLeft: Theme.spacing.small,
  },
  brand: {
    color: Colors.text.light,
    fontSize: Theme.typography.fontSize.tiny,
    fontWeight: 'bold',
  },
  description: {
    fontSize: Theme.typography.fontSize.medium,
    color: Colors.text.secondary,
    marginBottom: Theme.spacing.medium,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: Theme.spacing.tiny,
  },
  statusText: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.text.secondary,
    marginRight: Theme.spacing.small,
  },
  dateText: {
    fontSize: Theme.typography.fontSize.small,
    color: Colors.text.secondary,
  },
  button: {
    width: 'auto',
  },
  buttonText: {
    fontSize: Theme.typography.fontSize.small,
  },
});

export default CampaignCard;