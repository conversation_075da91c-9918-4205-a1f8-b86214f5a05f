import React, { useRef, useState } from "react";
import { View, Text, ScrollView, TouchableOpacity, Image } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import Button from "./Button";
import Theme from "../constants/Theme";
import Colors from "../constants/Colors";
import MockData from "../constants/MockData";

const styles = {
  featuredSectionContainer: {
    paddingTop: 10,
    paddingBottom: 20,
  },
  featuredTitle: {
    fontFamily: Theme.typography.fontFamily.display,
    fontSize: 30,
    paddingLeft: 20,
    fontWeight: "normal",
    color: Colors.text.primary,
    marginBottom: 12,
  },
  featuredSliderContent: {
    paddingLeft: 0,
    paddingRight: 0,
  },
  featuredCardSlider: {
    width: 340,
    height: 300,
    borderRadius: 20,
    overflow: "hidden",
    backgroundColor: "#fff",
    elevation: 5,
    shadowColor: "#000",
    shadowOpacity: 0.13,
    shadowRadius: 16,
    shadowOffset: { width: 0, height: 6 },
  },
  featuredImage: {
    width: "100%",
    height: "100%",
  },
  featuredOverlay: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    height: 90,
    backgroundColor: "rgba(60,0,80,0.18)",
  },
  imageWrapper: {
    position: "relative",
    width: "100%",
    height: "100%",
    overflow: "hidden",
  },
  blackOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
  },
  featuredContent: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    paddingHorizontal: 12,
    paddingBottom: 18,
  },
  featuredPaginationRow: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 10,
    marginBottom: 18,
  },
  featuredDot: {
    width: 18,
    height: 6,
    borderRadius: 3,
    backgroundColor: "#F3C6FF",
    marginHorizontal: 4,
  },
  featuredDotActive: {
    backgroundColor: "#FF8A00",
    width: 28,
  },
};

const FeaturedCampaignsSlider = () => {
  const { campaigns } = MockData;
  const [activeCampaignIndex, setActiveCampaignIndex] = useState(0);
  const campaignScrollRef = useRef(null);

  return (
    <View style={styles.featuredSectionContainer}>
      <Text style={styles.featuredTitle}>Featured Campaigns</Text>
      <ScrollView
        ref={campaignScrollRef}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.featuredSliderContent}
        onScroll={(e) => {
          const idx = Math.round(
            e.nativeEvent.contentOffset.x /
              (e.nativeEvent.layoutMeasurement.width || 1)
          );
          setActiveCampaignIndex(idx);
        }}
        scrollEventThrottle={16}
      >
        {campaigns.map((campaign, idx) => (
          <View
            style={[
              styles.featuredCardSlider,
              idx === 0
                ? { marginRight: 12, marginLeft: 20 }
                : { marginRight: 12 },
            ]}
            key={idx}
          >
            <View style={styles.imageWrapper}>
              <Image
                source={require("../assets/images/nike-air-max.jpg")}
                style={styles.featuredImage}
                resizeMode="cover"
              />
              <View style={styles.blackOverlay} />
            </View>
            <View style={styles.featuredOverlay} />
            {/* Top badges */}
            <View
              style={{
                position: "absolute",
                top: 12,
                left: 12,
                alignItems: "center",
              }}
            >
              <View>
                <Image
                  source={require("../assets/images/user.png")}
                  style={{ width: 40, height: 40 }}
                />
              </View>
              <Text
                style={{
                  fontSize: 10,
                  color: "#B16CFF",
                  fontWeight: "bold",
                  marginTop: 3,
                }}
              >
                1247
              </Text>
            </View>
            <View
              style={{
                position: "absolute",
                top: 12,
                right: 12,
                alignItems: "center",
              }}
            >
              <View>
                <Image
                  source={require("../assets/images/user.png")}
                  style={{ width: 40, height: 40 }}
                />
              </View>
              <Text
                style={{
                  fontSize: 10,
                  color: "#fff",
                  fontWeight: "600",
                  marginTop: 3,
                }}
              >
                QR Verification
              </Text>
            </View>
            {/* Bottom content */}
            <View style={styles.featuredContent}>
              <View style={{ width: "100%" }}>
                <Image
                  source={require("../assets/images/nike.png")}
                  style={{
                    width: 30,
                    height: 30,
                    borderRadius: 14,
                    // backgroundColor: "#fff",
                    marginRight: 8,
                  }}
                />
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                    marginBottom: 6,
                  }}
                >
                  <Text
                    style={{
                      fontSize: 22,
                      color: "#fff",
                      fontWeight: "normal",
                      fontFamily: Theme.typography.fontFamily.display,
                    }}
                  >
                    {campaign.title.length > 16
                      ? campaign.title.substring(0, 16) + "..."
                      : campaign.title}
                  </Text>
                  <View
                    style={{
                      backgroundColor: "transparent",
                      borderRadius: 10,
                      paddingHorizontal: 8,
                      borderWidth: 1,
                      borderColor: "#8AE68A",
                      paddingVertical: 2,
                      marginLeft: 8,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 12,
                        color: "#8AE68A",
                        fontFamily: Theme.typography.fontFamily.regular,
                      }}
                    >
                      Active
                    </Text>
                  </View>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    marginBottom: 8,
                  }}
                >
                  <Image
                    source={require("../assets/images/whitecup.png")}
                    style={{ width: 14, height: 14, marginRight: 4 }}
                  />
                  <Text
                    style={{
                      fontSize: 16,
                      color: "#FF7700",
                      fontFamily: Theme.typography.fontFamily.display,
                    }}
                  >
                    500 GSM Coins
                  </Text>
                </View>
                <Button
                  title={"JOIN CAMPAIGN"}
                  buttonStyle={{ height: 36 }}
                  textStyle={{
                    fontSize: Theme.typography.fontSize.small,
                  }}
                  style={{ width: 150 }}
                />
              </View>
            </View>
          </View>
        ))}
      </ScrollView>
      {/* Pagination Dots */}
      <View style={styles.featuredPaginationRow}>
        {campaigns.map((_, idx) => (
          <TouchableOpacity
            key={idx}
            onPress={() => {
              if (campaignScrollRef.current) {
                campaignScrollRef.current.scrollTo({
                  x: idx * 340,
                  animated: true,
                });
              }
            }}
            activeOpacity={0.7}
          >
            <View
              style={[
                styles.featuredDot,
                activeCampaignIndex === idx ? styles.featuredDotActive : null,
              ]}
            />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default FeaturedCampaignsSlider;
