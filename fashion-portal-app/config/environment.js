// Environment configuration for API endpoints
const ENV = {
  development: {
    API_BASE_URL: 'http://localhost:3000/api',
    API_TIMEOUT: 10000,
    ENABLE_LOGGING: true,
  },
  production: {
    API_BASE_URL: 'https://gsm-fashion-portal-f54077544850.herokuapp.com/api',
    API_TIMEOUT: 15000,
    ENABLE_LOGGING: false,
  },
};

// Determine current environment
const getCurrentEnvironment = () => {
  // In development, you can change this to 'development' for local testing
  // For now, we'll use production to connect to the deployed backend
  return __DEV__ ? 'development' : 'production';
};

const currentEnv = getCurrentEnvironment();
const config = ENV[currentEnv];

export default {
  ...config,
  ENVIRONMENT: currentEnv,
  
  // API Endpoints
  ENDPOINTS: {
    // Creator Authentication
    CREATOR_REGISTER: '/creators/auth/register',
    CREATOR_LOGIN: '/creators/auth/login',
    CREATOR_VERIFY_OTP: '/creators/auth/verify-otp',
    CREATOR_FORGOT_PASSWORD: '/creators/auth/forgot-password',
    CREATOR_RESET_PASSWORD: '/creators/auth/reset-password',
    CREATOR_RESEND_OTP: '/creators/auth/resend-otp',
    
    // Creator Profile
    CREATOR_PROFILE: '/creators/profile',
    CREATOR_STATS: '/creators/profile/stats',
    
    // Future endpoints for campaigns, products, etc.
    CAMPAIGNS_DISCOVER: '/campaigns/discover',
    PRODUCTS_DISCOVER: '/products/discover',
  },
  
  // App Configuration
  APP_CONFIG: {
    OTP_LENGTH: 6,
    PASSWORD_MIN_LENGTH: 6,
    TOKEN_STORAGE_KEY: 'creator_token',
    USER_STORAGE_KEY: 'creator_user',
    PROFILE_STORAGE_KEY: 'creator_profile',
  },
};
