import React from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { LogBox } from 'react-native';
import AppNavigator from './navigation/AppNavigator';
import { AuthProvider } from './contexts/AuthContext';
import { useFonts as useBakbakOne, BakbakOne_400Regular } from '@expo-google-fonts/bakbak-one';
import { useFonts as useLexend, Lexend_400Regular, Lexend_700Bold } from '@expo-google-fonts/lexend';
// import AppLoading from 'expo-app-loading';

LogBox.ignoreLogs([
  'Reanimated 2',
  'AsyncStorage has been extracted',
  'Non-serializable values were found in the navigation state',
]);

export default function App() {
  const [bakbakLoaded] = useBakbakOne({ BakbakOne_400Regular });
  const [lexendLoaded] = useLexend({ Lexend_400Regular, Lexend_700Bold });

  // if (!bakbakLoaded || !lexendLoaded) {
  //   return <AppLoading />;
  // }

  return (
    <SafeAreaProvider>
      <AuthProvider>
        <StatusBar style="auto" />
        <AppNavigator />
      </AuthProvider>
    </SafeAreaProvider>
  );
}
