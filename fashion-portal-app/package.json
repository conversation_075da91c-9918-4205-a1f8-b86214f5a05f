{"name": "app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/bakbak-one": "^0.2.3", "@expo-google-fonts/lexend": "^0.2.3", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.17", "expo-font": "^13.3.2", "expo-linear-gradient": "^14.1.5", "expo-local-authentication": "~16.0.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "react-native-svg-transformer": "^1.5.1"}, "private": true}