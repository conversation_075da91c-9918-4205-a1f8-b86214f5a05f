
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 260 84% 75%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 260 84% 95%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 260 84% 75%;

    --radius: 1rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 260 84% 75%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 260 84% 95%;
    --sidebar-accent-foreground: 260 84% 30%;
    --sidebar-border: 260 84% 90%;
    --sidebar-ring: 260 84% 75%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 260 84% 75%;
    --primary-foreground: 0 0% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 260 84% 25%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 260 84% 75%;
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 260 84% 75%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 260 84% 25%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 260 84% 20%;
    --sidebar-ring: 260 84% 75%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }
}

@layer components {
  .card-gradient {
    @apply bg-gradient-to-br from-brand-purple/20 to-brand-light-purple/10;
  }
  
  .button-gradient {
    @apply bg-gradient-to-r from-brand-purple to-brand-vivid-purple hover:from-brand-vivid-purple hover:to-brand-purple transition-all duration-300;
  }

  .section-title {
    @apply text-2xl font-semibold text-brand-dark-purple mb-4;
  }
  
  .stat-card {
    @apply bg-white rounded-xl shadow-sm p-4 border border-brand-purple/10 hover:shadow-md transition-shadow;
  }

  .dashboard-container {
    @apply container max-w-7xl mx-auto px-4 sm:px-6 pb-12;
  }
}
