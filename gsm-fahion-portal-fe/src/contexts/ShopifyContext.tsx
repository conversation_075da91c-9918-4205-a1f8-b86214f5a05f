import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { shopifyAPI } from '@/lib/shopify';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from './AuthContext';

interface ShopifyConnection {
  connected: boolean;
  shopName?: string;
  shopDomain?: string;
  shopEmail?: string;
  connectedAt?: string;
  lastSyncAt?: string;
  totalProducts?: number;
  syncedProducts?: number;
}

interface ShopifyContextType {
  connection: ShopifyConnection;
  isLoading: boolean;
  isConnecting: boolean;
  isDisconnecting: boolean;
  isSyncing: boolean;
  checkConnection: () => Promise<void>;
  connectStore: (domain: string) => Promise<void>;
  disconnectStore: () => Promise<void>;
  syncProducts: () => Promise<void>;
  refreshConnection: () => Promise<void>;
}

const ShopifyContext = createContext<ShopifyContextType | undefined>(undefined);

export const useShopify = () => {
  const context = useContext(ShopifyContext);
  if (context === undefined) {
    throw new Error('useShopify must be used within a ShopifyProvider');
  }
  return context;
};

interface ShopifyProviderProps {
  children: ReactNode;
}

export const ShopifyProvider: React.FC<ShopifyProviderProps> = ({ children }) => {
  const [connection, setConnection] = useState<ShopifyConnection>({
    connected: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const { toast } = useToast();
  const { isAuthenticated, user } = useAuth();

  const checkConnection = useCallback(async () => {
    try {
      setIsLoading(true);
      const status = await shopifyAPI.getConnectionStatus();

      if (status.connected) {
        const newConnection = {
          connected: true,
          shopName: status.shopName,
          shopDomain: status.shopDomain,
          shopEmail: status.shopEmail,
          connectedAt: status.connectedAt,
          lastSyncAt: status.lastSyncAt,
          totalProducts: status.totalProducts || 0,
          syncedProducts: status.syncedProducts || 0,
        };
        setConnection(newConnection);
      } else {
        // Always set disconnected state immediately if backend says not connected
        setConnection({ connected: false });

        // Clear any cached data
        localStorage.removeItem('shopify_connection_cache');
      }
    } catch (error) {
      console.error('Failed to check Shopify connection:', error);
      // On error, assume disconnected to be safe
      setConnection({ connected: false });
      localStorage.removeItem('shopify_connection_cache');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const connectStore = async (domain: string) => {
    try {
      setIsConnecting(true);
      const result = await shopifyAPI.connectStore(domain);
      
      if (result.success && result.oauthUrl) {
        // Redirect to Shopify OAuth
        window.location.href = result.oauthUrl;
      } else {
        throw new Error(result.error || 'Failed to initiate connection');
      }
    } catch (error: any) {
      console.error('Failed to connect store:', error);
      toast({
        title: 'Connection Failed',
        description: error.message || 'Failed to connect to Shopify store',
        variant: 'destructive',
      });
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnectStore = async () => {
    try {
      setIsDisconnecting(true);
      const result = await shopifyAPI.disconnectStore();

      if (result.success) {
        // Clear all connection data completely
        setConnection({ connected: false });

        // Clear any cached data or localStorage if used
        localStorage.removeItem('shopify_connection_cache');

        toast({
          title: 'Store Disconnected',
          description: 'Your Shopify store has been disconnected successfully',
        });
      } else {
        throw new Error(result.error || 'Failed to disconnect store');
      }
    } catch (error: any) {
      console.error('Failed to disconnect store:', error);
      toast({
        title: 'Disconnection Failed',
        description: error.message || 'Failed to disconnect Shopify store',
        variant: 'destructive',
      });
    } finally {
      setIsDisconnecting(false);
    }
  };

  const syncProducts = async () => {
    if (!connection.connected) {
      toast({
        title: 'No Connection',
        description: 'Please connect your Shopify store first',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSyncing(true);
      const result = await shopifyAPI.syncProducts();
      
      if (result.success) {
        toast({
          title: 'Sync Complete',
          description: `Successfully synced ${result.synced} products from ${connection.shopName}`,
        });
        
        // Refresh connection to get updated product count
        await checkConnection();
      } else {
        throw new Error(result.error || 'Sync failed');
      }
    } catch (error: any) {
      console.error('Failed to sync products:', error);
      toast({
        title: 'Sync Failed',
        description: error.message || 'Failed to sync products from Shopify',
        variant: 'destructive',
      });
    } finally {
      setIsSyncing(false);
    }
  };

  const refreshConnection = async () => {
    await checkConnection();
  };

  useEffect(() => {
    // Only check connection for authenticated brand users
    if (isAuthenticated && user?.role === 'brand') {
      checkConnection();
    } else {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, checkConnection]);

  // OAuth callback is now handled by dedicated ShopifyCallback component

  const value: ShopifyContextType = {
    connection,
    isLoading,
    isConnecting,
    isDisconnecting,
    isSyncing,
    checkConnection,
    connectStore,
    disconnectStore,
    syncProducts,
    refreshConnection,
  };

  return (
    <ShopifyContext.Provider value={value}>
      {children}
    </ShopifyContext.Provider>
  );
};
