import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { brandAPI } from '@/lib/api';

export interface User {
  id: string;
  email: string;
  role: 'brand' | 'admin';
  status: 'pending' | 'approved' | 'rejected';
  email_verified: boolean;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  login: (token: string, userData: User) => void;
  logout: () => void;
  updateUser: (userData: User) => void;
  refreshUser: () => Promise<User | null>;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isBrand: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Simple initialization on mount - only runs once
  useEffect(() => {
    const initAuth = () => {
      try {
        // Check for brand session first
        const brandToken = localStorage.getItem('brand_token');
        const brandUser = localStorage.getItem('brand_user');

        if (brandToken && brandUser) {
          const userData = JSON.parse(brandUser);
          setToken(brandToken);
          setUser(userData);
          return;
        }

        // Check for admin session
        const adminToken = localStorage.getItem('admin_token');
        const adminUser = localStorage.getItem('admin_user');

        if (adminToken && adminUser) {
          const userData = JSON.parse(adminUser);
          setToken(adminToken);
          setUser(userData);
          return;
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []); // Empty dependency array - only run once

  // Simple login function without complex side effects
  const login = (authToken: string, userData: User) => {
    setToken(authToken);
    setUser(userData);
    setIsLoading(false); // Ensure loading is false after login

    // Store in localStorage based on user role
    const tokenKey = userData.role === 'admin' ? 'admin_token' : 'brand_token';
    const userKey = userData.role === 'admin' ? 'admin_user' : 'brand_user';

    localStorage.setItem(tokenKey, authToken);
    localStorage.setItem(userKey, JSON.stringify(userData));
  };

  // Simple logout function
  const logout = () => {
    setToken(null);
    setUser(null);

    // Clear all auth data
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_user');
    localStorage.removeItem('brand_token');
    localStorage.removeItem('brand_user');
  };

  // Refresh user data from server
  const refreshUser = async () => {
    try {
      const token = localStorage.getItem('brand_token');
      if (!token || !user || user.role === 'admin') return;

      const response = await brandAPI.getProfile();
      const brandProfile = response.data;

      // Extract user data from nested structure
      const updatedUser: User = {
        id: brandProfile.user_id._id,
        email: brandProfile.user_id.email,
        role: 'brand',
        status: brandProfile.user_id.status,
        email_verified: brandProfile.user_id.email_verified
      };

      // Update localStorage with fresh user data
      localStorage.setItem('brand_user', JSON.stringify(updatedUser));
      setUser(updatedUser);

      return updatedUser;
    } catch (error) {
      console.error('Failed to refresh user data:', error);
      // If refresh fails, user might need to login again
      logout();
      return null;
    }
  };

  // Simple update user function
  const updateUser = (userData: User) => {
    setUser(userData);
    
    // Update localStorage
    const userKey = userData.role === 'admin' ? 'admin_user' : 'brand_user';
    localStorage.setItem(userKey, JSON.stringify(userData));
  };

  // Initialize auth from localStorage (called manually, not in useEffect)
  const initializeAuth = () => {
    try {
      // Check for brand session first
      const brandToken = localStorage.getItem('brand_token');
      const brandUser = localStorage.getItem('brand_user');
      
      if (brandToken && brandUser) {
        const userData = JSON.parse(brandUser);
        setToken(brandToken);
        setUser(userData);
        return;
      }

      // Check for admin session
      const adminToken = localStorage.getItem('admin_token');
      const adminUser = localStorage.getItem('admin_user');
      
      if (adminToken && adminUser) {
        const userData = JSON.parse(adminUser);
        setToken(adminToken);
        setUser(userData);
        return;
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      // Clear corrupted data
      logout();
    }
  };

  const value: AuthContextType = {
    user,
    token,
    isLoading,
    login,
    logout,
    updateUser,
    refreshUser,
    isAuthenticated: !!token && !!user,
    isAdmin: user?.role === 'admin',
    isBrand: user?.role === 'brand',
  };

  // Expose initializeAuth for manual initialization
  (value as any).initializeAuth = initializeAuth;

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Hook for manual auth initialization
export function useAuthInit() {
  const context = useAuth();
  return (context as any).initializeAuth;
}
