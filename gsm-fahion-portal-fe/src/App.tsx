import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";

// Context Providers
import { AuthProvider } from "./contexts/AuthContext";
import { ShopifyProvider } from "./contexts/ShopifyContext";

// Auth components
import Login from "./pages/auth/Login";
import { Register } from "./pages/auth/Register";
import { VerifyOtp } from "./pages/auth/VerifyOtp";

// Admin components
import { AdminLogin } from "./pages/admin/AdminLogin";
import { AdminDashboard } from "./pages/admin/AdminDashboard";
import { AdminCampaigns } from "./pages/admin/AdminCampaigns";

// Main app components
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import Settings from "./pages/Settings";
import ProductImportPage from "./pages/ProductImportPage";
import ProductCreate from "./pages/ProductCreate";
import ProductDetail from "./pages/ProductDetail";
import ProductList from "./pages/ProductList";
import CampaignList from "./pages/CampaignList";
import CampaignCreate from "./pages/CampaignCreate";
import CampaignDetail from "./pages/CampaignDetail";
import RewardsManagement from "./pages/RewardsManagement";
import Marketplace from "./pages/Marketplace";
import TeamManagement from "./pages/TeamManagement";

// Layout and utility components
import { ProtectedRoute } from "./components/ProtectedRoute";
import NotFound from "./pages/NotFound";
import { ShopifyCallback } from "./pages/ShopifyCallback";

const queryClient = new QueryClient();

const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AuthProvider>
            <ShopifyProvider>
              <Routes>
                {/* Admin Portal Routes */}
                <Route path="/admin/login" element={<AdminLogin />} />
                <Route
                  path="/admin"
                  element={
                    <ProtectedRoute requireAdmin>
                      <AdminDashboard />
                    </ProtectedRoute>
                  }
                />
                {/* Redirect old admin dashboard route */}
                <Route path="/admin/dashboard" element={<Navigate to="/admin" replace />} />
                <Route
                  path="/admin/campaigns"
                  element={
                    <ProtectedRoute requireAdmin>
                      <AdminCampaigns />
                    </ProtectedRoute>
                  }
                />

                {/* Auth Routes */}
                <Route path="/auth/login" element={<Login />} />
                <Route path="/auth/register" element={<Register />} />
                <Route path="/auth/verify-otp" element={<VerifyOtp />} />

                {/* Main App Routes */}
                <Route
                  path="/"
                  element={
                    <ProtectedRoute requireBrand requireApproved>
                      <Index />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/dashboard"
                  element={
                    <ProtectedRoute requireBrand requireApproved>
                      <Dashboard />
                    </ProtectedRoute>
                  }
                />

                {/* Product Routes */}
                <Route
                  path="/products"
                  element={
                    <ProtectedRoute requireBrand requireApproved>
                      <ProductList />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/products/create"
                  element={
                    <ProtectedRoute requireBrand requireApproved>
                      <ProductCreate />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/products/:id"
                  element={
                    <ProtectedRoute requireBrand requireApproved>
                      <ProductDetail />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/products/import"
                  element={
                    <ProtectedRoute requireBrand requireApproved>
                      <ProductImportPage />
                    </ProtectedRoute>
                  }
                />

                {/* Campaign Routes */}
                <Route
                  path="/campaigns"
                  element={
                    <ProtectedRoute requireBrand requireApproved>
                      <CampaignList />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/campaigns/create"
                  element={
                    <ProtectedRoute requireBrand requireApproved>
                      <CampaignCreate />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/campaigns/:id"
                  element={
                    <ProtectedRoute requireBrand requireApproved>
                      <CampaignDetail />
                    </ProtectedRoute>
                  }
                />

                {/* Other Main Routes */}
                <Route
                  path="/rewards"
                  element={
                    <ProtectedRoute requireBrand requireApproved>
                      <RewardsManagement />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/marketplace"
                  element={
                    <ProtectedRoute requireBrand requireApproved>
                      <Marketplace />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/team"
                  element={
                    <ProtectedRoute requireBrand requireApproved>
                      <TeamManagement />
                    </ProtectedRoute>
                  }
                />

                {/* Settings */}
                <Route
                  path="/settings"
                  element={
                    <ProtectedRoute requireBrand>
                      <Settings />
                    </ProtectedRoute>
                  }
                />

                {/* Shopify OAuth Callback */}
                <Route path="/shopify/callback" element={<ShopifyCallback />} />

                {/* Catch-all route */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </ShopifyProvider>
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
