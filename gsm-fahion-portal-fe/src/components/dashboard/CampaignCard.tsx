
import { Calendar, Clock, User } from "lucide-react";
import { <PERSON>, CardContent, CardD<PERSON>cription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Link } from "react-router-dom";

interface CampaignCardProps {
  title: string;
  description: string;
  status: 'active' | 'draft' | 'ended' | 'scheduled';
  submissions: number;
  target: number;
  daysLeft?: number;
  startDate?: string;
  id?: string;
  banner_url?: string;
}

export function CampaignCard({
  title,
  description,
  status,
  submissions,
  target,
  daysLeft,
  startDate,
  id = "1", // Default ID for demo purposes
  banner_url
}: CampaignCardProps) {
  const progress = Math.min(Math.round((submissions / target) * 100), 100);
  
  const statusColors = {
    active: "bg-green-100 text-green-800",
    draft: "bg-yellow-100 text-yellow-800",
    ended: "bg-gray-100 text-gray-800",
    scheduled: "bg-blue-100 text-blue-800"
  };

  return (
    <Card className="h-full overflow-hidden border border-brand-purple/10 hover:shadow-md transition-shadow">
      <Link to={`/campaigns/${id}`} className="block h-full">
        {banner_url && (
          <div className="aspect-video w-full overflow-hidden">
            <img
              src={banner_url}
              alt={title}
              className="w-full h-full object-cover"
            />
          </div>
        )}
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <CardTitle className="text-lg font-semibold">{title}</CardTitle>
            <Badge className={statusColors[status]}>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
          </div>
          <CardDescription className="text-sm text-muted-foreground line-clamp-2">
            {description}
          </CardDescription>
        </CardHeader>
        <CardContent className="pb-2">
          <div className="flex items-center justify-between text-sm mb-1">
            <span>Progress</span>
            <span className="font-medium">{submissions} / {target}</span>
          </div>
          <Progress value={progress} className="h-2" />
        </CardContent>
        <CardFooter className="border-t pt-4 text-xs text-muted-foreground">
          <div className="w-full grid grid-cols-2 gap-4">
            {daysLeft !== undefined && (
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                <span>{daysLeft} days left</span>
              </div>
            )}
            {startDate && !daysLeft && (
              <div className="flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                <span>Starts {startDate}</span>
              </div>
            )}
            <div className="flex items-center justify-end">
              <User className="h-3 w-3 mr-1" />
              <span>{submissions} submissions</span>
            </div>
          </div>
        </CardFooter>
      </Link>
    </Card>
  );
}
