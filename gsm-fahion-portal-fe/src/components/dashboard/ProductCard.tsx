
import { Tag } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Header } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Link } from "react-router-dom";

interface ProductCardProps {
  name: string;
  image: string;
  sku: string;
  status: 'live' | 'pending' | 'rejected';
  campaigns?: number;
  id?: string;
}

export function ProductCard({
  name,
  image,
  sku,
  status,
  campaigns = 0,
  id = "1" // Default ID for demo purposes
}: ProductCardProps) {
  const statusColors = {
    live: "bg-green-100 text-green-800",
    pending: "bg-yellow-100 text-yellow-800",
    rejected: "bg-red-100 text-red-800"
  };

  return (
    <Card className="h-full overflow-hidden border border-brand-purple/10 hover:shadow-md transition-shadow">
      <Link to={`/products/${id}`} className="block">
        <div className="aspect-[4/3] relative overflow-hidden bg-muted">
          <img 
            src={image} 
            alt={name}
            className="object-cover w-full h-full"
          />
          <Badge className={`absolute top-2 right-2 ${statusColors[status]}`}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        </div>
        <CardHeader className="py-3">
          <h3 className="font-semibold text-base line-clamp-1">{name}</h3>
        </CardHeader>
        <CardFooter className="pt-0 pb-3 flex items-center justify-between text-xs text-muted-foreground">
          <span>SKU: {sku}</span>
          <div className="flex items-center">
            <Tag className="h-3 w-3 mr-1" />
            <span>{campaigns} {campaigns === 1 ? 'campaign' : 'campaigns'}</span>
          </div>
        </CardFooter>
      </Link>
    </Card>
  );
}
