
import { cn } from "@/lib/utils";

interface StatCardProps {
  title: string;
  value: string;
  description?: string;
  icon: React.ElementType;
  trend?: 'up' | 'down';
  trendValue?: string;
  className?: string;
}

export function StatCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  trendValue,
  className
}: StatCardProps) {
  return (
    <div className={cn("stat-card", className)}>
      <div className="flex items-start justify-between">
        <div>
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <h3 className="text-2xl font-bold mt-1 text-brand-dark-purple">{value}</h3>
          {description && <p className="text-xs text-muted-foreground mt-1">{description}</p>}
          {trend && (
            <div className={cn(
              "flex items-center mt-2 text-xs font-medium",
              trend === 'up' ? 'text-green-600' : 'text-red-600'
            )}>
              <span className="mr-1">
                {trend === 'up' ? '↑' : '↓'}
              </span>
              <span>{trendValue}</span>
            </div>
          )}
        </div>
        <div className="h-8 w-8 rounded-full bg-brand-purple/10 flex items-center justify-center">
          <Icon className="h-4 w-4 text-brand-purple" />
        </div>
      </div>
    </div>
  );
}
