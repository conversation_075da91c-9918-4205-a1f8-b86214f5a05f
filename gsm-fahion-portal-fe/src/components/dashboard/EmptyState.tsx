
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";

interface EmptyStateProps {
  title: string;
  description: string;
  actionLabel: string;
  icon: React.ElementType;
  onClick: () => void;
}

export function EmptyState({
  title,
  description,
  actionLabel,
  icon: Icon,
  onClick,
}: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center p-8 my-8 rounded-xl border border-dashed border-brand-purple/30 bg-white/50 animate-fade-in">
      <div className="h-16 w-16 rounded-full bg-brand-purple/10 flex items-center justify-center mb-4">
        <Icon className="h-8 w-8 text-brand-purple" />
      </div>
      <h2 className="text-2xl font-semibold text-brand-dark-purple mb-2">{title}</h2>
      <p className="text-muted-foreground text-center mb-6 max-w-md">{description}</p>
      <Button onClick={onClick} className="button-gradient text-white flex items-center gap-2">
        <Plus className="h-4 w-4" /> {actionLabel}
      </Button>
    </div>
  );
}
