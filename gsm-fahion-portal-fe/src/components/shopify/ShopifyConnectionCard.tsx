import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Store, Package, RefreshCw, ExternalLink } from 'lucide-react';
import { useShopify } from '@/contexts/ShopifyContext';
import { useAuth } from '@/contexts/AuthContext';
import { useSearchParams } from 'react-router-dom';

interface ShopifyConnectionCardProps {
  showTitle?: boolean;
  showDescription?: boolean;
  compact?: boolean;
}

export const ShopifyConnectionCard: React.FC<ShopifyConnectionCardProps> = ({
  showTitle = true,
  showDescription = true,
  compact = false
}) => {
  const { connection, isLoading, isConnecting, isSyncing, connectStore, disconnectStore, syncProducts, checkConnection } = useShopify();
  const { user, isAuthenticated, token } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const [storeDomain, setStoreDomain] = useState('');

  // Auto-refresh connection status when component mounts
  useEffect(() => {
    // Only run once when component mounts and user is authenticated
    if (isAuthenticated && user?.role === 'brand' && !connection.connected) {
      const timer = setTimeout(() => {
        checkConnection();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, user?.role]); // Removed checkConnection from dependencies



  const handleConnect = async () => {
    if (!storeDomain.trim()) return;
    await connectStore(storeDomain.trim());
  };

  const handleDisconnect = async () => {
    try {
      await disconnectStore();
      // Force a connection check after disconnect to ensure UI is updated
      setTimeout(() => {
        checkConnection();
      }, 500);
    } catch (error) {
      console.error('Disconnect error:', error);
    }
  };

  const handleSync = async () => {
    await syncProducts();
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Checking Shopify connection...</span>
        </CardContent>
      </Card>
    );
  }

  if (connection.connected) {
    return (
      <Card>
        {showTitle && (
          <CardHeader className={compact ? "pb-3" : ""}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Store className="h-5 w-5 text-green-600" />
                <CardTitle className={compact ? "text-lg" : "text-xl"}>
                  Shopify Store Connected
                </CardTitle>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Connected
                </Badge>
              </div>
            </div>
            {showDescription && !compact && (
              <CardDescription>
                Your Shopify store is connected and ready to sync products
              </CardDescription>
            )}
          </CardHeader>
        )}
        
        <CardContent className={compact ? "pt-0" : ""}>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Store Name</label>
                <p className="text-sm text-gray-600">{connection.shopName}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium">Store Domain</label>
                <p className="text-sm text-gray-600">{connection.shopDomain}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium">Connected On</label>
                <p className="text-sm text-gray-600">
                  {connection.connectedAt ? new Date(connection.connectedAt).toLocaleDateString() : 'Recently'}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium">Products</label>
                <div className="flex items-center space-x-2">
                  <p className="text-sm text-gray-600">
                    {connection.totalProducts || 0} total
                  </p>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={handleSync}
                    disabled={isSyncing}
                    className="h-6 px-2 text-xs"
                  >
                    {isSyncing ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <RefreshCw className="h-3 w-3" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="flex space-x-2 pt-2">
              <Button 
                variant="outline" 
                onClick={handleSync}
                disabled={isSyncing}
                className="flex-1"
              >
                {isSyncing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Syncing...
                  </>
                ) : (
                  <>
                    <Package className="h-4 w-4 mr-2" />
                    Sync Products
                  </>
                )}
              </Button>
              
              <Button 
                variant="outline" 
                onClick={() => window.open(`https://${connection.shopDomain}`, '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                View Store
              </Button>
              
              <Button 
                variant="destructive" 
                onClick={handleDisconnect}
              >
                Disconnect
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      {showTitle && (
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Store className="h-5 w-5" />
              <span>Connect Shopify Store</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={checkConnection}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </CardTitle>
          {showDescription && (
            <CardDescription>
              Import your products directly from your Shopify store. We'll automatically sync your product catalog.
            </CardDescription>
          )}
        </CardHeader>
      )}
      
      <CardContent>
        <div className="space-y-4">


          {showDescription && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• All product details including images will be imported</li>
                <li>• Updates to your Shopify products can be synced</li>
                <li>• Import up to 1,000 products at once</li>
              </ul>
            </div>
          )}
          
          <div className="space-y-3">
            <div>
              <label htmlFor="storeDomain" className="block text-sm font-medium mb-1">
                Store Domain
              </label>
              <Input
                id="storeDomain"
                type="text"
                placeholder="your-store-name"
                value={storeDomain}
                onChange={(e) => setStoreDomain(e.target.value)}
                className="w-full"
                disabled={isConnecting}
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter your store name (without .myshopify.com)
              </p>
            </div>
            
            <Button
              onClick={handleConnect}
              disabled={!storeDomain.trim() || isConnecting}
              className="w-full"
            >
              {isConnecting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Connecting...
                </>
              ) : (
                <>
                  <Store className="h-4 w-4 mr-2" />
                  Connect Shopify Store
                </>
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
