import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useShopify } from '@/contexts/ShopifyContext';
import {
  Store,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Loader2,
  Unlink,
  RefreshCw
} from 'lucide-react';

export function ShopifyConnect() {
  const [shopDomain, setShopDomain] = useState('');
  const { toast } = useToast();
  const {
    connection,
    isLoading,
    isConnecting,
    isDisconnecting,
    connectStore,
    disconnectStore,
    checkConnection
  } = useShopify();

  useEffect(() => {
    // Check for connection result from URL params
    const urlParams = new URLSearchParams(window.location.search);
    const shopifyResult = urlParams.get('shopify');
    const shopName = urlParams.get('shop');
    const errorMessage = urlParams.get('message');

    if (shopifyResult === 'connected') {
      toast({
        title: 'Shopify Connected!',
        description: `Successfully connected to ${shopName}`,
      });
      // Clean URL
      window.history.replaceState({}, '', window.location.pathname);
      // Refresh connection status
      setTimeout(checkConnection, 1000);
    } else if (shopifyResult === 'error') {
      toast({
        title: 'Connection Failed',
        description: errorMessage || 'Failed to connect to Shopify store',
        variant: 'destructive',
      });
      // Clean URL
      window.history.replaceState({}, '', window.location.pathname);
    }
  }, [checkConnection, toast]);

  const handleConnect = async () => {
    if (!shopDomain.trim()) {
      toast({
        title: 'Store domain required',
        description: 'Please enter your Shopify store domain',
        variant: 'destructive',
      });
      return;
    }

    await connectStore(shopDomain);
  };

  const handleDisconnect = async () => {
    try {
      await disconnectStore();
      // Force a connection check after disconnect to ensure UI is updated
      setTimeout(() => {
        checkConnection();
      }, 500);
    } catch (error) {
      console.error('Disconnect error:', error);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (connection.connected) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Store className="h-5 w-5" />
            <span>Shopify Store Connected</span>
            <Badge className="bg-green-100 text-green-800">
              <CheckCircle className="h-3 w-3 mr-1" />
              Connected
            </Badge>
          </CardTitle>
          <CardDescription>
            Your Shopify store is connected and ready to sync products
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Store Name</label>
              <p className="text-sm text-gray-600">{connection.shopName}</p>
            </div>
            <div>
              <label className="text-sm font-medium">Store Domain</label>
              <p className="text-sm text-gray-600">{connection.shopDomain}</p>
            </div>
            <div>
              <label className="text-sm font-medium">Connected On</label>
              <p className="text-sm text-gray-600">
                {connection.connectedAt ? new Date(connection.connectedAt).toLocaleDateString() : 'Unknown'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium">Products</label>
              <p className="text-sm text-gray-600">
                {connection.totalProducts || 0} total
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={checkConnection}
                  className="ml-2 h-6 px-2 text-xs"
                >
                  <RefreshCw className="h-3 w-3" />
                </Button>
              </p>
            </div>
          </div>

          {connection.lastSyncAt && (
            <Alert>
              <RefreshCw className="h-4 w-4" />
              <AlertDescription>
                Last synced: {new Date(connection.lastSyncAt).toLocaleString()}
              </AlertDescription>
            </Alert>
          )}

          <div className="flex space-x-3">
            <Button 
              variant="outline" 
              onClick={() => window.open(`https://${connection.shopDomain}`, '_blank')}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Visit Store
            </Button>
            <Button 
              variant="outline" 
              onClick={handleDisconnect}
              disabled={isDisconnecting}
            >
              {isDisconnecting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Unlink className="h-4 w-4 mr-2" />
              )}
              Disconnect
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Store className="h-5 w-5" />
          <span>Connect Shopify Store</span>
        </CardTitle>
        <CardDescription>
          Connect your Shopify store to automatically sync products
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You'll be redirected to Shopify to authorize the connection. Make sure you have admin access to your store.
          </AlertDescription>
        </Alert>

        <div className="space-y-2">
          <label htmlFor="shop-domain" className="text-sm font-medium">
            Store Domain
          </label>
          <div className="flex space-x-2">
            <div className="flex-1 flex">
              <Input
                id="shop-domain"
                type="text"
                placeholder="mystore"
                value={shopDomain}
                onChange={(e) => setShopDomain(e.target.value)}
                className="rounded-r-none"
                disabled={isConnecting}
              />
              <div className="px-3 py-2 bg-gray-50 border border-l-0 rounded-r-md text-sm text-gray-500 flex items-center">
                .myshopify.com
              </div>
            </div>
          </div>
          <p className="text-xs text-gray-500">
            Enter your Shopify store domain (without .myshopify.com)
          </p>
        </div>

        <Button 
          onClick={handleConnect} 
          disabled={isConnecting || !shopDomain.trim()}
          className="w-full"
        >
          {isConnecting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Connecting...
            </>
          ) : (
            <>
              <Store className="h-4 w-4 mr-2" />
              Connect Store
            </>
          )}
        </Button>

        <div className="text-xs text-gray-500 space-y-1">
          <p>Don't have a Shopify store? <a href="https://shopify.com" target="_blank" className="text-blue-600 hover:underline">Create one free</a></p>
          <p>Need help? <a href="#" className="text-blue-600 hover:underline">View setup guide</a></p>
        </div>
      </CardContent>
    </Card>
  );
}
