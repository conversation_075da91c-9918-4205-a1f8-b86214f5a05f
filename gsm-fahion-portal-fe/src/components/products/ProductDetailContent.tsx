import { useState } from "react";
import { QrCode, Tag, Package, Edit, Plus, Trash, Download, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Link } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";

export type VerificationType = 'manual' | 'qr' | 'nfc';
export type ProductStatus = 'live' | 'pending' | 'rejected';
export type NFCStatus = 'unavailable' | 'ordered' | 'delivered' | 'activated';

export interface ProductDetailProps {
  id: string;
  name: string;
  description: string;
  sku: string;
  price?: number;
  currency?: string;
  status: ProductStatus;
  verificationType: VerificationType;
  images: string[];
  nfcStatus?: NFCStatus;
  nfcChips?: { id: string; status: string }[];
  campaigns: Array<{
    id: string;
    title: string;
    status: 'active' | 'scheduled' | 'draft' | 'ended';
  }>;
}

export function ProductDetailContent({
  id,
  name,
  description,
  sku,
  price,
  currency = "USD",
  status,
  verificationType,
  images,
  nfcStatus,
  nfcChips,
  campaigns
}: ProductDetailProps) {
  const { toast } = useToast();
  const [currentImage, setCurrentImage] = useState(0);
  
  const statusColors = {
    live: "bg-green-100 text-green-800",
    pending: "bg-yellow-100 text-yellow-800",
    rejected: "bg-red-100 text-red-800"
  };
  
  const getVerificationIcon = (type: VerificationType) => {
    switch (type) {
      case 'qr':
        return <QrCode className="h-6 w-6 text-purple-600" />;
      case 'nfc':
        return <Tag className="h-6 w-6 text-blue-600" />;
      case 'manual':
        return <Package className="h-6 w-6 text-gray-600" />;
    }
  };
  
  const handleDeleteCampaign = (campaignId: string) => {
    toast({
      title: "Campaign removed",
      description: "Campaign has been unlinked from this product.",
    });
  };
  
  const handleOrderNFC = () => {
    toast({
      title: "Redirecting to NFC order",
      description: "Taking you to the NFC ordering page.",
    });
  };
  
  const downloadQRCode = () => {
    toast({
      title: "QR Code Downloaded",
      description: "The QR Code has been downloaded successfully.",
    });
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Left column - Product images and basic info */}
      <div className="lg:col-span-2">
        <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
          <div className="relative aspect-[4/3] bg-gray-100">
            <img 
              src={images[currentImage]} 
              alt={name} 
              className="w-full h-full object-contain"
            />
            <Badge className={`absolute top-3 right-3 ${statusColors[status]}`}>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
          </div>
          
          {images.length > 1 && (
            <div className="p-2 flex gap-2 overflow-x-auto">
              {images.map((image, idx) => (
                <button 
                  key={idx} 
                  className={`w-16 h-16 rounded-md overflow-hidden flex-shrink-0 ${idx === currentImage ? 'ring-2 ring-brand-purple' : ''}`}
                  onClick={() => setCurrentImage(idx)}
                >
                  <img src={image} alt={`${name} ${idx + 1}`} className="w-full h-full object-cover" />
                </button>
              ))}
            </div>
          )}
        </div>

        <Card className="p-6 mb-6">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold text-brand-dark-purple mb-1">{name}</h1>
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-sm text-muted-foreground">SKU: {sku}</span>
                <span className="h-1 w-1 rounded-full bg-gray-300"></span>
                <div className="flex items-center">
                  {getVerificationIcon(verificationType)}
                  <span className="ml-1 text-sm">{verificationType.toUpperCase()}</span>
                </div>
              </div>
            </div>
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4 mr-2" /> Edit
            </Button>
          </div>

          {price !== undefined && (
            <div className="mb-4">
              <p className="text-xl font-semibold">{new Intl.NumberFormat('en-US', { style: 'currency', currency }).format(price)}</p>
            </div>
          )}
          
          <div className="prose max-w-none">
            <p className="text-muted-foreground">{description}</p>
          </div>
        </Card>

        <Tabs defaultValue="verification" className="mb-6">
          <TabsList className="mb-4">
            <TabsTrigger value="verification">Verification</TabsTrigger>
            <TabsTrigger value="campaigns">Linked Campaigns</TabsTrigger>
          </TabsList>
          
          <TabsContent value="verification" className="animate-fade-in">
            <Card className="p-6">
              {verificationType === 'manual' && (
                <div className="text-center space-y-4">
                  <div className="h-24 w-24 bg-gray-100 rounded-full mx-auto flex items-center justify-center">
                    <Package className="h-12 w-12 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium">Manual Verification</h3>
                  <p className="text-sm text-muted-foreground max-w-md mx-auto">
                    This product uses manual verification. Users will need to upload photos or other proof of purchase.
                  </p>
                  <div className="pt-4">
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                      <Button variant="outline" className="sm:w-auto" asChild>
                        <Link to="/verification-setup">Add QR Code</Link>
                      </Button>
                      <Button variant="outline" className="sm:w-auto" asChild>
                        <Link to="/nfc-requests">Order NFC Chips</Link>
                      </Button>
                    </div>
                  </div>
                </div>
              )}
              
              {verificationType === 'qr' && (
                <div className="text-center space-y-4">
                  <div className="h-64 w-64 bg-white border mx-auto flex items-center justify-center p-4">
                    <img 
                      src={`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://verify.brand.com/p/${sku}`}
                      alt="QR Code" 
                      className="max-h-full"
                    />
                  </div>
                  <h3 className="text-lg font-medium">QR Code Verification</h3>
                  <p className="text-sm text-muted-foreground max-w-md mx-auto">
                    This unique QR code verifies authentic product ownership. Print and attach to product packaging.
                  </p>
                  <div className="pt-4">
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                      <Button onClick={downloadQRCode}>
                        <Download className="h-4 w-4 mr-2" /> Download QR Code
                      </Button>
                      <Button variant="outline" asChild>
                        <Link to="/nfc-requests">Upgrade to NFC</Link>
                      </Button>
                    </div>
                  </div>
                </div>
              )}
              
              {verificationType === 'nfc' && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">NFC Chip Verification</h3>
                    <Badge variant="outline" className={
                      nfcStatus === 'activated' ? "bg-green-50 text-green-800" :
                      nfcStatus === 'delivered' ? "bg-blue-50 text-blue-800" :
                      nfcStatus === 'ordered' ? "bg-yellow-50 text-yellow-800" :
                      "bg-gray-50 text-gray-800"
                    }>
                      {nfcStatus}
                    </Badge>
                  </div>
                  
                  {nfcStatus === 'unavailable' ? (
                    <div className="text-center py-8">
                      <div className="h-16 w-16 bg-gray-100 rounded-full mx-auto flex items-center justify-center mb-4">
                        <Tag className="h-8 w-8 text-gray-400" />
                      </div>
                      <p className="mb-6 text-muted-foreground">No NFC chips have been ordered for this product yet.</p>
                      <Button onClick={handleOrderNFC} asChild>
                        <Link to="/nfc-requests">Order NFC Chips</Link>
                      </Button>
                    </div>
                  ) : (
                    <>
                      <p className="text-sm text-muted-foreground">
                        This product has {nfcChips?.length || 0} NFC chips assigned to it.
                      </p>
                      
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex justify-between mb-2">
                          <h4 className="text-sm font-medium">NFC Chip IDs</h4>
                          <Badge variant="outline" className="bg-blue-50 text-blue-700">
                            {nfcChips?.length || 0} chips
                          </Badge>
                        </div>
                        {nfcChips && nfcChips.length > 0 ? (
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            {nfcChips.map((chip, idx) => (
                              <div key={idx} className="text-xs bg-white p-2 rounded border flex items-center justify-between">
                                <span className="font-mono">{chip.id}</span>
                                <Badge variant="outline" className={
                                  chip.status === 'active' ? "bg-green-50 text-green-800" : 
                                  "bg-yellow-50 text-yellow-800"
                                }>
                                  {chip.status}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-center py-2 text-muted-foreground">No chip IDs available</p>
                        )}
                      </div>
                      
                      <div className="flex justify-end">
                        <Button variant="outline" size="sm" asChild>
                          <Link to="/nfc-requests">Manage NFC Chips</Link>
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              )}
            </Card>
          </TabsContent>
          
          <TabsContent value="campaigns" className="animate-fade-in">
            <Card className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Linked Campaigns</h3>
                <Button asChild>
                  <Link to="/campaigns/create">
                    <Plus className="h-4 w-4 mr-2" /> Create Campaign
                  </Link>
                </Button>
              </div>
              
              {campaigns.length > 0 ? (
                <div className="space-y-3">
                  {campaigns.map((campaign) => (
                    <div key={campaign.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Badge className={
                          campaign.status === 'active' ? "bg-green-100 text-green-800" :
                          campaign.status === 'scheduled' ? "bg-blue-100 text-blue-800" :
                          campaign.status === 'draft' ? "bg-yellow-100 text-yellow-800" :
                          "bg-gray-100 text-gray-800"
                        }>
                          {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                        </Badge>
                        <Link to={`/campaigns/${campaign.id}`} className="font-medium hover:text-brand-purple transition-colors">
                          {campaign.title}
                        </Link>
                      </div>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        onClick={() => handleDeleteCampaign(campaign.id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="h-16 w-16 bg-gray-100 rounded-full mx-auto flex items-center justify-center mb-4">
                    <Calendar className="h-8 w-8 text-gray-400" />
                  </div>
                  <p className="mb-6 text-muted-foreground">No campaigns are linked to this product yet.</p>
                  <Button asChild>
                    <Link to="/campaigns/create">Create Campaign</Link>
                  </Button>
                </div>
              )}
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Right column - Actions and quick info */}
      <div>
        <Card className="p-6 mb-6">
          <h3 className="text-lg font-medium mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <Button className="w-full" asChild>
              <Link to="/campaigns/create">
                <Plus className="h-4 w-4 mr-2" /> Create Campaign
              </Link>
            </Button>
            
            {verificationType === 'manual' && (
              <Button variant="outline" className="w-full" asChild>
                <Link to="/verification-setup">
                  <QrCode className="h-4 w-4 mr-2" /> Add Verification
                </Link>
              </Button>
            )}
            
            {verificationType !== 'nfc' && (
              <Button variant="outline" className="w-full" asChild>
                <Link to="/nfc-requests">
                  <Tag className="h-4 w-4 mr-2" /> Order NFC Chips
                </Link>
              </Button>
            )}
            
            <Button variant="outline" className="w-full">
              <Edit className="h-4 w-4 mr-2" /> Edit Product
            </Button>
          </div>
        </Card>
        
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">Product Stats</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Linked Campaigns</span>
              <span className="font-medium">{campaigns.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Total Submissions</span>
              <span className="font-medium">42</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Rewards Issued</span>
              <span className="font-medium">$420 USDC</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Verification Type</span>
              <span className="font-medium flex items-center">
                {getVerificationIcon(verificationType)}
                <span className="ml-1">{verificationType.toUpperCase()}</span>
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
