import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  requireBrand?: boolean;
  requireApproved?: boolean;
}

export function ProtectedRoute({
  children,
  requireAuth = true,
  requireAdmin = false,
  requireBrand = false,
  requireApproved = false
}: ProtectedRouteProps) {
  const { isAuthenticated, isAdmin, isBrand, isLoading, user, refreshUser } = useAuth();
  const location = useLocation();
  const [hasRefreshed, setHasRefreshed] = useState(false);

  // Auto-refresh user data if brand user has pending status (might be stale)
  useEffect(() => {
    if (!hasRefreshed && isBrand && user?.status === 'pending' && isAuthenticated) {
      console.log('🔄 Auto-refreshing user data to check for approval updates...');
      refreshUser().then((updatedUser) => {
        if (updatedUser?.status === 'approved') {
          console.log('✅ User approval status updated from pending to approved!');
        }
        setHasRefreshed(true);
      }).catch(() => {
        setHasRefreshed(true);
      });
    }
  }, [isBrand, user?.status, isAuthenticated, hasRefreshed, refreshUser]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    // For admin routes, redirect to admin login
    if (location.pathname.startsWith('/admin')) {
      return <Navigate to="/admin/login" replace />;
    }
    // For brand routes, redirect to brand login
    return <Navigate to="/auth/login" replace />;
  }

  // If admin access is required but user is not admin
  if (requireAdmin && !isAdmin) {
    return <Navigate to="/admin/login" replace />;
  }

  // If brand access is required but user is not brand
  if (requireBrand && !isBrand) {
    return <Navigate to="/auth/login" replace />;
  }

  // If approved status is required but user is not approved
  if (requireApproved && user?.status !== 'approved') {
    return <Navigate to="/settings" replace />;
  }

  return <>{children}</>;
}
