import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, X, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from './button';

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  onFileRemove?: () => void;
  accept?: Record<string, string[]>;
  maxSize?: number;
  disabled?: boolean;
  className?: string;
  selectedFile?: File | null;
  error?: string;
  description?: string;
}

export function FileUpload({
  onFileSelect,
  onFileRemove,
  accept = { 'text/csv': ['.csv'] },
  maxSize = 5 * 1024 * 1024, // 5MB
  disabled = false,
  className,
  selectedFile,
  error,
  description = 'Drag and drop your CSV file here, or click to browse',
}: FileUploadProps) {
  const [dragError, setDragError] = useState<string>('');

  const onDrop = useCallback(
    (acceptedFiles: File[], rejectedFiles: any[]) => {
      setDragError('');
      
      if (rejectedFiles.length > 0) {
        const rejection = rejectedFiles[0];
        if (rejection.errors[0]?.code === 'file-too-large') {
          setDragError(`File is too large. Maximum size is ${Math.round(maxSize / 1024 / 1024)}MB`);
        } else if (rejection.errors[0]?.code === 'file-invalid-type') {
          setDragError('Invalid file type. Please upload a CSV file');
        } else {
          setDragError('Invalid file. Please try again');
        }
        return;
      }

      if (acceptedFiles.length > 0) {
        onFileSelect(acceptedFiles[0]);
      }
    },
    [onFileSelect, maxSize]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept,
    maxSize,
    multiple: false,
    disabled,
  });

  const displayError = error || dragError;

  if (selectedFile) {
    return (
      <div className={cn('border-2 border-dashed border-gray-300 rounded-lg p-6', className)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
              <FileText className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="font-medium text-gray-900">{selectedFile.name}</p>
              <p className="text-sm text-gray-500">
                {(selectedFile.size / 1024).toFixed(1)} KB
              </p>
            </div>
          </div>
          {onFileRemove && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onFileRemove}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        {displayError && (
          <div className="mt-3 flex items-center text-red-600">
            <AlertCircle className="h-4 w-4 mr-2" />
            <span className="text-sm">{displayError}</span>
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      {...getRootProps()}
      className={cn(
        'border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer transition-colors',
        isDragActive && 'border-blue-400 bg-blue-50',
        disabled && 'cursor-not-allowed opacity-50',
        displayError && 'border-red-300 bg-red-50',
        className
      )}
    >
      <input {...getInputProps()} />
      
      <div className="flex flex-col items-center">
        <div className={cn(
          'h-12 w-12 rounded-full flex items-center justify-center mb-4',
          isDragActive ? 'bg-blue-100' : 'bg-gray-100',
          displayError && 'bg-red-100'
        )}>
          <Upload className={cn(
            'h-6 w-6',
            isDragActive ? 'text-blue-600' : 'text-gray-600',
            displayError && 'text-red-600'
          )} />
        </div>
        
        <p className={cn(
          'text-lg font-medium mb-2',
          isDragActive ? 'text-blue-900' : 'text-gray-900',
          displayError && 'text-red-900'
        )}>
          {isDragActive ? 'Drop your file here' : 'Upload CSV File'}
        </p>
        
        <p className={cn(
          'text-sm mb-4',
          isDragActive ? 'text-blue-700' : 'text-gray-600',
          displayError && 'text-red-700'
        )}>
          {description}
        </p>
        
        {!isDragActive && (
          <Button variant="outline" disabled={disabled}>
            Choose File
          </Button>
        )}
        
        {displayError && (
          <div className="mt-4 flex items-center text-red-600">
            <AlertCircle className="h-4 w-4 mr-2" />
            <span className="text-sm">{displayError}</span>
          </div>
        )}
      </div>
    </div>
  );
}
