import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface UserDataRefreshProps {
  children: React.ReactNode;
}

export const UserDataRefresh: React.FC<UserDataRefreshProps> = ({ children }) => {
  const { user, refreshUser, isBrand } = useAuth();

  useEffect(() => {
    // Only refresh for brand users who might have stale approval status
    if (isBrand && user?.status === 'pending') {
      console.log('🔄 Refreshing user data to check for approval updates...');
      refreshUser().then((updatedUser) => {
        if (updatedUser?.status === 'approved') {
          console.log('✅ User approval status updated!');
        }
      });
    }
  }, [user?.status, isBrand, refreshUser]);

  return <>{children}</>;
};
