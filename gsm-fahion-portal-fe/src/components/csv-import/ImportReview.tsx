import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowLeft, Package, Image as ImageIcon, Star } from 'lucide-react';

interface ImportReviewProps {
  products: any[];
  importBatchId: string;
  onFinalize: (settings: any) => void;
  onBack: () => void;
  isLoading: boolean;
}

export function ImportReview({ 
  products, 
  importBatchId, 
  onFinalize, 
  onBack, 
  isLoading 
}: ImportReviewProps) {
  const [defaultVerificationType, setDefaultVerificationType] = useState<string>('qr');
  const [publishImmediately, setPublishImmediately] = useState<boolean>(true);

  const handleFinalize = () => {
    onFinalize({
      defaultVerificationType,
      publishImmediately,
    });
  };

  const productsWithImages = products.filter(product => 
    product.images && product.images.length > 0
  ).length;

  const totalProducts = products.length;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-2">Review & Finalize Import</h2>
        <p className="text-muted-foreground">
          Review your products and configure final settings before importing
        </p>
      </div>

      {/* Import Summary */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Import Summary</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
              <Package className="h-6 w-6 text-blue-600" />
            </div>
            <p className="font-semibold text-2xl">{totalProducts}</p>
            <p className="text-sm text-muted-foreground">Total Products</p>
          </div>
          
          <div className="text-center">
            <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-2">
              <ImageIcon className="h-6 w-6 text-green-600" />
            </div>
            <p className="font-semibold text-2xl">{productsWithImages}</p>
            <p className="text-sm text-muted-foreground">With Images</p>
          </div>
          
          <div className="text-center">
            <div className="h-12 w-12 rounded-full bg-amber-100 flex items-center justify-center mx-auto mb-2">
              <Package className="h-6 w-6 text-amber-600" />
            </div>
            <p className="font-semibold text-2xl">{totalProducts - productsWithImages}</p>
            <p className="text-sm text-muted-foreground">Without Images</p>
          </div>
        </div>

        {/* Import Settings */}
        <div className="space-y-4 border-t pt-4">
          <h4 className="font-medium">Import Settings</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="verification-type">Default Verification Type</Label>
              <Select value={defaultVerificationType} onValueChange={setDefaultVerificationType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select verification type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="qr">QR Code</SelectItem>
                  <SelectItem value="nfc">NFC Chip</SelectItem>
                  <SelectItem value="manual">Manual Verification</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Publishing Options</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="publish-immediately"
                  checked={publishImmediately}
                  onCheckedChange={(checked) => setPublishImmediately(checked as boolean)}
                />
                <Label htmlFor="publish-immediately" className="text-sm">
                  Publish products immediately
                </Label>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Product Preview */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Product Preview</h3>
        
        <div className="space-y-3 max-h-64 overflow-y-auto">
          {products.slice(0, 5).map((product, index) => (
            <div key={product.id} className="flex items-center space-x-4 p-3 border rounded-lg">
              {/* Product Image */}
              <div className="h-12 w-12 rounded border overflow-hidden bg-gray-100 flex-shrink-0">
                {product.images && product.images.length > 0 ? (
                  <img
                    src={product.images[0].src}
                    alt={product.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <ImageIcon className="h-5 w-5 text-gray-400" />
                  </div>
                )}
              </div>
              
              {/* Product Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <h4 className="font-medium truncate">{product.title}</h4>
                  <Badge variant="outline">${product.price}</Badge>
                </div>
                
                <div className="flex items-center space-x-2 mt-1">
                  {product.category && (
                    <Badge variant="secondary" className="text-xs">
                      {product.category}
                    </Badge>
                  )}
                  
                  {product.images && product.images.length > 0 && (
                    <Badge variant="secondary" className="text-xs text-green-600">
                      <ImageIcon className="h-3 w-3 mr-1" />
                      {product.images.length} image(s)
                    </Badge>
                  )}
                  
                  <Badge variant="outline" className="text-xs">
                    {defaultVerificationType.toUpperCase()}
                  </Badge>
                </div>
              </div>
            </div>
          ))}
          
          {products.length > 5 && (
            <p className="text-sm text-muted-foreground text-center py-2">
              ... and {products.length - 5} more products
            </p>
          )}
        </div>
      </Card>

      {/* Warning Messages */}
      {totalProducts - productsWithImages > 0 && (
        <Card className="p-4 border-amber-200 bg-amber-50">
          <div className="flex items-start space-x-3">
            <div className="h-5 w-5 rounded-full bg-amber-100 flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-amber-600 text-xs font-bold">!</span>
            </div>
            <div>
              <p className="font-medium text-amber-900">
                {totalProducts - productsWithImages} products without images
              </p>
              <p className="text-sm text-amber-700">
                These products will be imported without images. You can add images later from the product management page.
              </p>
            </div>
          </div>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6">
        <Button variant="outline" onClick={onBack} disabled={isLoading}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Images
        </Button>
        
        <Button 
          onClick={handleFinalize}
          disabled={isLoading}
          className="button-gradient text-white"
        >
          {isLoading ? (
            'Finalizing Import...'
          ) : (
            `Import ${totalProducts} Products`
          )}
        </Button>
      </div>
    </div>
  );
}
