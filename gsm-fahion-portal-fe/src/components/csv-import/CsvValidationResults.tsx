import React from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertCircle, ArrowLeft, ArrowRight } from 'lucide-react';

interface CsvValidationResultsProps {
  uploadResult: any;
  onProceed: () => void;
  onBack: () => void;
  isLoading: boolean;
}

export function CsvValidationResults({ 
  uploadResult, 
  onProceed, 
  onBack, 
  isLoading 
}: CsvValidationResultsProps) {
  if (!uploadResult) return null;

  const { total_rows, valid_products, errors, preview } = uploadResult;
  const hasErrors = errors && errors.length > 0;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-2">Validation Results</h2>
        <p className="text-muted-foreground">
          Review the validation results before proceeding
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-blue-600 font-semibold">{total_rows}</span>
            </div>
            <div>
              <p className="font-medium">Total Rows</p>
              <p className="text-sm text-muted-foreground">In CSV file</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="font-medium">{valid_products} Valid</p>
              <p className="text-sm text-muted-foreground">Ready to import</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
              <AlertCircle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <p className="font-medium">{errors?.length || 0} Errors</p>
              <p className="text-sm text-muted-foreground">Need attention</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Error Details */}
      {hasErrors && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">Validation Errors Found:</p>
              <div className="max-h-40 overflow-y-auto space-y-1">
                {errors.map((error: any, index: number) => (
                  <div key={index} className="text-sm">
                    <span className="font-medium">Row {error.row}:</span>{' '}
                    {error.errors.join(', ')}
                  </div>
                ))}
              </div>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Product Preview */}
      {preview && preview.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Product Preview</h3>
          <div className="space-y-3">
            {preview.map((product: any, index: number) => (
              <Card key={index} className="p-4">
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-medium">{product.name}</h4>
                      <Badge variant="outline">${product.price}</Badge>
                    </div>
                    
                    {product.description && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {product.description}
                      </p>
                    )}
                    
                    <div className="flex flex-wrap gap-2">
                      {product.sku && (
                        <Badge variant="secondary">SKU: {product.sku}</Badge>
                      )}
                      {product.category && (
                        <Badge variant="secondary">{product.category}</Badge>
                      )}
                      {product.product_type && (
                        <Badge variant="secondary">{product.product_type}</Badge>
                      )}
                    </div>
                    
                    {product.tags && (
                      <div className="flex flex-wrap gap-1">
                        {product.tags.split(',').map((tag: string, tagIndex: number) => (
                          <Badge key={tagIndex} variant="outline" className="text-xs">
                            {tag.trim()}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                  
                  <div className="text-right text-sm text-muted-foreground">
                    {product.inventory && (
                      <p>Stock: {product.inventory}</p>
                    )}
                    {product.weight && (
                      <p>Weight: {product.weight} {product.weight_unit || 'g'}</p>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>
          
          {total_rows > preview.length && (
            <p className="text-sm text-muted-foreground text-center">
              Showing {preview.length} of {total_rows} products
            </p>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Upload
        </Button>
        
        <Button 
          onClick={onProceed} 
          disabled={isLoading || valid_products === 0}
          className="button-gradient text-white"
        >
          {isLoading ? (
            'Creating Products...'
          ) : (
            <>
              Proceed with {valid_products} Products
              <ArrowRight className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </div>

      {valid_products === 0 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No valid products found. Please fix the errors in your CSV file and try again.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
