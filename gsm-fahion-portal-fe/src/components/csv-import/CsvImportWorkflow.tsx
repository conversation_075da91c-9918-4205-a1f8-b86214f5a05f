import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { FileUpload } from '@/components/ui/file-upload';
import { csvImportAPI } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Download, CheckCircle, AlertCircle, Upload, Image as ImageIcon } from 'lucide-react';
import { CsvValidationResults } from './CsvValidationResults';
import { ProductImageUpload } from './ProductImageUpload';
import { ImportReview } from './ImportReview';

export type ImportStep = 'upload' | 'validate' | 'images' | 'review' | 'complete';

interface CsvImportWorkflowProps {
  onComplete: (importedCount: number) => void;
}

export function CsvImportWorkflow({ onComplete }: CsvImportWorkflowProps) {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState<ImportStep>('upload');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [importBatchId, setImportBatchId] = useState<string>('');
  const [draftProducts, setDraftProducts] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  const steps = [
    { id: 'upload', title: 'Upload CSV', icon: Upload },
    { id: 'validate', title: 'Validate Data', icon: CheckCircle },
    { id: 'images', title: 'Add Images', icon: ImageIcon },
    { id: 'review', title: 'Review & Save', icon: CheckCircle },
  ];

  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  const handleDownloadTemplate = async () => {
    try {
      console.log('Downloading CSV template...');
      const response = await csvImportAPI.downloadTemplate();
      console.log('Template response:', response);

      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'product-import-template.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Template downloaded",
        description: "CSV template has been downloaded successfully.",
      });
    } catch (error) {
      console.error('Template download error:', error);
      toast({
        title: "Download failed",
        description: "Failed to download CSV template. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleFileSelect = async (file: File) => {
    console.log('File selected:', file.name, file.size, file.type);
    setSelectedFile(file);
    setError('');
    setIsLoading(true);

    try {
      console.log('Uploading CSV file...');
      const response = await csvImportAPI.uploadCsv(file);
      console.log('Upload response:', response);

      setUploadResult(response.data);
      setImportBatchId(response.data.import_batch_id);

      if (response.data.errors.length === 0) {
        setCurrentStep('validate');
        toast({
          title: "CSV uploaded successfully",
          description: `${response.data.valid_products} products ready for import.`,
        });
      } else {
        setCurrentStep('validate');
        toast({
          title: "CSV uploaded with warnings",
          description: `${response.data.valid_products} valid products found, ${response.data.errors.length} errors detected.`,
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error('Upload error:', error);
      setError(error.response?.data?.message || 'Failed to upload CSV file');
      toast({
        title: "Upload failed",
        description: error.response?.data?.message || 'Failed to upload CSV file',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileRemove = () => {
    setSelectedFile(null);
    setUploadResult(null);
    setError('');
    setCurrentStep('upload');
  };

  const handleCreateDrafts = async () => {
    if (!uploadResult || !importBatchId) return;

    setIsLoading(true);
    try {
      console.log('Creating draft products with data:', {
        products: uploadResult.all_valid_products || uploadResult.preview,
        import_batch_id: importBatchId,
      });

      const response = await csvImportAPI.createDraftProducts({
        products: uploadResult.all_valid_products || uploadResult.preview,
        import_batch_id: importBatchId,
      });

      console.log('Draft creation response:', response);
      setDraftProducts(response.data.products);
      setCurrentStep('images');

      toast({
        title: "Draft products created",
        description: `${response.data.products.length} products are ready for image upload.`,
      });
    } catch (error: any) {
      console.error('Draft creation error:', error);
      toast({
        title: "Failed to create drafts",
        description: error.response?.data?.message || 'Failed to create draft products',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleImagesComplete = (updatedProducts: any[]) => {
    console.log('Images completed, updating products:', updatedProducts);

    // Ensure we have the updated products with image data
    if (updatedProducts && updatedProducts.length > 0) {
      setDraftProducts(updatedProducts);
      setCurrentStep('review');

      toast({
        title: "Ready for review",
        description: "Product images have been processed. You can now review and finalize the import.",
      });
    } else {
      console.error('No updated products received from image step');
      toast({
        title: "Error",
        description: "Failed to process product images. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleFinalizeImport = async (verificationSettings: any) => {
    setIsLoading(true);
    try {
      console.log('Finalizing import with batch ID:', importBatchId);
      console.log('Draft products count:', draftProducts.length);
      console.log('Verification settings:', verificationSettings);

      const response = await csvImportAPI.finalizeImport({
        import_batch_id: importBatchId,
        default_verification_type: verificationSettings.defaultVerificationType,
        publish_immediately: verificationSettings.publishImmediately,
      });

      console.log('Finalize response:', response);
      setCurrentStep('complete');
      onComplete(draftProducts.length);

      toast({
        title: "Import completed",
        description: `${draftProducts.length} products have been imported successfully.`,
      });
    } catch (error: any) {
      console.error('Finalize import error:', error);
      toast({
        title: "Import failed",
        description: error.response?.data?.message || 'Failed to finalize import',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'upload':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-semibold mb-2">Upload CSV File</h2>
              <p className="text-muted-foreground mb-6">
                Start by downloading our template, then upload your product data
              </p>
            </div>
            
            <div className="flex justify-center mb-6">
              <Button onClick={handleDownloadTemplate} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download CSV Template
              </Button>
            </div>
            
            <FileUpload
              onFileSelect={handleFileSelect}
              onFileRemove={handleFileRemove}
              selectedFile={selectedFile}
              error={error}
              disabled={isLoading}
              description="Upload your CSV file with product data"
            />
          </div>
        );

      case 'validate':
        return (
          <CsvValidationResults
            uploadResult={uploadResult}
            onProceed={handleCreateDrafts}
            onBack={handleFileRemove}
            isLoading={isLoading}
          />
        );

      case 'images':
        return (
          <ProductImageUpload
            products={draftProducts}
            importBatchId={importBatchId}
            onComplete={handleImagesComplete}
            onBack={() => setCurrentStep('validate')}
          />
        );

      case 'review':
        return (
          <ImportReview
            products={draftProducts}
            importBatchId={importBatchId}
            onFinalize={handleFinalizeImport}
            onBack={() => setCurrentStep('images')}
            isLoading={isLoading}
          />
        );

      default:
        return null;
    }
  };

  if (currentStep === 'complete') {
    return (
      <Card className="p-6 text-center">
        <div className="flex flex-col items-center">
          <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h2 className="text-2xl font-semibold mb-2">Import Completed!</h2>
          <p className="text-muted-foreground mb-6">
            {draftProducts.length} products have been imported successfully.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Progress Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">CSV Import</h1>
          <span className="text-sm text-muted-foreground">
            Step {currentStepIndex + 1} of {steps.length}
          </span>
        </div>
        
        <Progress value={progress} className="h-2" />
        
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = index === currentStepIndex;
            const isCompleted = index < currentStepIndex;
            
            return (
              <div key={step.id} className="flex items-center">
                <div className={`
                  h-8 w-8 rounded-full flex items-center justify-center
                  ${isCompleted ? 'bg-green-100 text-green-600' : 
                    isActive ? 'bg-blue-100 text-blue-600' : 
                    'bg-gray-100 text-gray-400'}
                `}>
                  <Icon className="h-4 w-4" />
                </div>
                <span className={`ml-2 text-sm ${
                  isActive ? 'font-medium text-gray-900' : 'text-gray-500'
                }`}>
                  {step.title}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <Card className="p-6">
        {renderStepContent()}
      </Card>
    </div>
  );
}
