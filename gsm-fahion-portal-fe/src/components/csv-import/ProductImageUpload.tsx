import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { csvImportAPI, uploadAPI } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { 
  Upload, 
  ArrowLeft, 
  ArrowRight, 
  Image as ImageIcon, 
  X, 
  Star,
  Check
} from 'lucide-react';

interface ProductImageUploadProps {
  products: any[];
  importBatchId: string;
  onComplete: (updatedProducts: any[]) => void;
  onBack: () => void;
}

interface ProductImages {
  [productId: string]: {
    urls: string[];
    primaryIndex: number;
  };
}

export function ProductImageUpload({ 
  products, 
  importBatchId, 
  onComplete, 
  onBack 
}: ProductImageUploadProps) {
  const { toast } = useToast();
  const [productImages, setProductImages] = useState<ProductImages>({});
  const [uploadingImages, setUploadingImages] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);

  const getProductProgress = () => {
    const productsWithImages = Object.keys(productImages).filter(
      productId => productImages[productId].urls.length > 0
    ).length;
    return (productsWithImages / products.length) * 100;
  };

  const handleImageUpload = async (productId: string, files: FileList) => {
    if (!files || files.length === 0) return;

    // Validate file types and sizes
    const validFiles = Array.from(files).filter(file => {
      const isValidType = file.type.startsWith('image/');
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB limit

      if (!isValidType) {
        toast({
          title: "Invalid file type",
          description: `${file.name} is not a valid image file`,
          variant: "destructive",
        });
        return false;
      }

      if (!isValidSize) {
        toast({
          title: "File too large",
          description: `${file.name} exceeds 10MB limit`,
          variant: "destructive",
        });
        return false;
      }

      return true;
    });

    if (validFiles.length === 0) return;

    setUploadingImages(prev => new Set(prev).add(productId));

    try {
      // Upload files sequentially to avoid overwhelming the server
      const imageUrls: string[] = [];

      for (const file of validFiles) {
        try {
          const result = await uploadAPI.uploadLogo(file);
          imageUrls.push(result.data.url);
        } catch (fileError: any) {
          console.error(`Failed to upload ${file.name}:`, fileError);
          toast({
            title: "Upload failed",
            description: `Failed to upload ${file.name}: ${fileError.response?.data?.message || fileError.message}`,
            variant: "destructive",
          });
        }
      }

      if (imageUrls.length > 0) {
        setProductImages(prev => ({
          ...prev,
          [productId]: {
            urls: [...(prev[productId]?.urls || []), ...imageUrls],
            primaryIndex: prev[productId]?.primaryIndex || 0,
          },
        }));

        toast({
          title: "Images uploaded",
          description: `${imageUrls.length} image(s) uploaded successfully.`,
        });
      }
    } catch (error: any) {
      console.error('Upload error:', error);
      toast({
        title: "Upload failed",
        description: error.response?.data?.message || 'Failed to upload images',
        variant: "destructive",
      });
    } finally {
      setUploadingImages(prev => {
        const newSet = new Set(prev);
        newSet.delete(productId);
        return newSet;
      });
    }
  };

  const handleRemoveImage = (productId: string, imageIndex: number) => {
    setProductImages(prev => {
      const current = prev[productId];
      if (!current) return prev;

      const newUrls = current.urls.filter((_, index) => index !== imageIndex);
      const newPrimaryIndex = current.primaryIndex >= imageIndex && current.primaryIndex > 0
        ? current.primaryIndex - 1
        : current.primaryIndex;

      return {
        ...prev,
        [productId]: {
          urls: newUrls,
          primaryIndex: Math.min(newPrimaryIndex, newUrls.length - 1),
        },
      };
    });
  };

  const handleSetPrimaryImage = (productId: string, imageIndex: number) => {
    setProductImages(prev => ({
      ...prev,
      [productId]: {
        ...prev[productId],
        primaryIndex: imageIndex,
      },
    }));
  };

  const handleSkipProduct = (productId: string) => {
    setProductImages(prev => ({
      ...prev,
      [productId]: {
        urls: [],
        primaryIndex: 0,
      },
    }));
  };

  const handleSaveImages = async () => {
    setIsLoading(true);
    try {
      const bulkImageData = {
        products: Object.entries(productImages)
          .filter(([_, images]) => images.urls.length > 0)
          .map(([productId, images]) => ({
            product_id: productId,
            image_urls: images.urls,
            primary_image_index: images.primaryIndex,
          })),
      };

      let saveResult = null;
      if (bulkImageData.products.length > 0) {
        console.log('Saving images for products:', bulkImageData);
        saveResult = await csvImportAPI.updateBulkImages(bulkImageData);
        console.log('Save result:', saveResult);

        // Check if any saves failed
        if (saveResult.data.failed > 0) {
          const failedProducts = saveResult.data.results.filter((r: any) => !r.success);
          console.warn('Some image saves failed:', failedProducts);

          toast({
            title: "Partial save success",
            description: `${saveResult.data.updated} products saved, ${saveResult.data.failed} failed. Check console for details.`,
            variant: "destructive",
          });
        }
      }

      // Update the products array with image information for the next step
      const updatedProducts = products.map(product => {
        const productId = product.id;
        const images = productImages[productId];

        if (images && images.urls.length > 0) {
          return {
            ...product,
            images: images.urls.map((url, index) => ({
              src: url,
              alt: `${product.title} image ${index + 1}`,
              position: index + 1,
              is_primary: index === images.primaryIndex
            }))
          };
        }

        return product;
      });

      // Pass the updated products to the parent component
      onComplete(updatedProducts);

      if (!saveResult || saveResult.data.failed === 0) {
        toast({
          title: "Images saved",
          description: `Images for ${bulkImageData.products.length} products have been saved successfully.`,
        });
      }
    } catch (error: any) {
      console.error('Save images error:', error);
      toast({
        title: "Save failed",
        description: error.response?.data?.message || 'Failed to save images. Please try again.',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const productsWithImages = Object.keys(productImages).filter(
    productId => productImages[productId]?.urls.length > 0
  ).length;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-2">Add Product Images</h2>
        <p className="text-muted-foreground mb-4">
          Upload images for your products. You can skip products without images.
        </p>
        
        <div className="flex items-center justify-center space-x-4 mb-4">
          <Badge variant="outline">
            {productsWithImages} of {products.length} products have images
          </Badge>
          <Progress value={getProductProgress()} className="w-32 h-2" />
        </div>
      </div>

      <div className="space-y-4 max-h-96 overflow-y-auto">
        {products.map((product) => {
          const productId = product.id;
          const images = productImages[productId] || { urls: [], primaryIndex: 0 };
          const isUploading = uploadingImages.has(productId);
          const hasImages = images.urls.length > 0;

          return (
            <Card key={productId} className="p-4">
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="font-medium">{product.title}</h3>
                    <p className="text-sm text-muted-foreground">
                      ${product.price} • {product.category || 'No category'}
                    </p>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {hasImages && (
                      <Badge variant="secondary" className="text-green-600">
                        <Check className="h-3 w-3 mr-1" />
                        {images.urls.length} image(s)
                      </Badge>
                    )}
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSkipProduct(productId)}
                      disabled={isUploading}
                    >
                      Skip
                    </Button>
                  </div>
                </div>

                {/* Image Upload */}
                <div className="space-y-3">
                  <Label>Product Images</Label>
                  
                  {/* Existing Images */}
                  {images.urls.length > 0 && (
                    <div className="grid grid-cols-4 gap-2">
                      {images.urls.map((url, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={url}
                            alt={`Product ${index + 1}`}
                            className="w-full h-20 object-cover rounded border"
                          />
                          
                          {/* Primary Image Indicator */}
                          {index === images.primaryIndex && (
                            <div className="absolute top-1 left-1">
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            </div>
                          )}
                          
                          {/* Image Actions */}
                          <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center space-x-1">
                            {index !== images.primaryIndex && (
                              <Button
                                size="icon"
                                variant="secondary"
                                className="h-6 w-6"
                                onClick={() => handleSetPrimaryImage(productId, index)}
                              >
                                <Star className="h-3 w-3" />
                              </Button>
                            )}
                            
                            <Button
                              size="icon"
                              variant="destructive"
                              className="h-6 w-6"
                              onClick={() => handleRemoveImage(productId, index)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {/* Upload Input */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => {
                          if (e.target.files) {
                            handleImageUpload(productId, e.target.files);
                            // Clear the input so the same file can be selected again if needed
                            e.target.value = '';
                          }
                        }}
                        disabled={isUploading}
                        className="flex-1"
                      />

                      {isUploading && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Upload className="h-4 w-4 mr-1 animate-pulse" />
                          Uploading...
                        </div>
                      )}
                    </div>

                    <p className="text-xs text-muted-foreground">
                      Supported formats: JPG, PNG, GIF. Max size: 10MB per image.
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6">
        <Button variant="outline" onClick={onBack} disabled={isLoading}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Validation
        </Button>
        
        <Button
          onClick={handleSaveImages}
          disabled={isLoading || uploadingImages.size > 0}
          className="button-gradient text-white"
        >
          {isLoading ? (
            'Saving Images...'
          ) : uploadingImages.size > 0 ? (
            'Uploading Images...'
          ) : (
            <>
              Continue to Review
              <ArrowRight className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
