
import { ChevronLeft, ChevronRight, Home, Settings, Package, Calendar, Wallet, Tag, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useLocation, Link } from "react-router-dom";

interface AppSidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export function AppSidebar({ open, setOpen }: AppSidebarProps) {
  const location = useLocation();
  const currentPath = location.pathname;

  const navigationItems = [
    { name: "Dashboard", href: "/", icon: Home, current: currentPath === "/" },
    { name: "Campaigns", href: "/campaigns", icon: Calendar, current: currentPath.includes("/campaigns") },
    { name: "Products", href: "/products", icon: Package, current: currentPath.includes("/products") },
    { name: "Rewards", href: "/rewards", icon: Wallet, current: currentPath === "/rewards" },
    { name: "Marketplace", href: "/marketplace", icon: Tag, current: currentPath === "/marketplace" },
    { name: "Team", href: "/team", icon: Users, current: currentPath === "/team" },
    { name: "Settings", href: "/settings", icon: Settings, current: currentPath === "/settings" },
  ];

  return (
    <aside
      className={cn(
        "fixed inset-y-0 left-0 z-20 flex flex-col bg-white border-r border-border transition-all duration-300 ease-in-out transform",
        open ? "translate-x-0 w-64" : "-translate-x-full w-64 md:translate-x-0 md:w-20"
      )}
    >
      <div className="flex items-center justify-between h-16 px-4 border-b border-border">
        <div className="flex items-center">
          {open ? (
            <span className="text-xl font-semibold bg-clip-text text-transparent bg-gradient-purple">FashionBrand</span>
          ) : (
            <div className="w-8 h-8 rounded-full bg-gradient-purple flex items-center justify-center text-white font-bold">
              F
            </div>
          )}
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="hidden md:flex"
          onClick={() => setOpen(!open)}
        >
          {open ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
        </Button>
      </div>
      <nav className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-1 px-2">
          {navigationItems.map((item) => (
            <li key={item.name}>
              <Link
                to={item.href}
                className={cn(
                  "flex items-center px-2 py-2 rounded-lg text-sm font-medium transition-colors",
                  item.current
                    ? "bg-brand-purple/10 text-brand-purple"
                    : "text-gray-700 hover:bg-brand-purple/5 hover:text-brand-purple"
                )}
              >
                <item.icon className={cn("h-5 w-5 mr-3", open ? "" : "mx-auto")} />
                {open && <span>{item.name}</span>}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
      <div className="p-4">
        {open ? (
          <Button className="w-full button-gradient text-white">
            Upgrade Plan
          </Button>
        ) : (
          <Button variant="ghost" size="icon" className="w-full flex justify-center">
            <Wallet className="h-5 w-5 text-brand-purple" />
          </Button>
        )}
      </div>
    </aside>
  );
}
