import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  ExternalLink, 
  Mail, 
  Calendar,
  Building,
  FileText,
  Globe,
  Instagram,
  Twitter,
  Facebook
} from 'lucide-react';
import { adminAPI } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';

interface Brand {
  _id: string;
  email: string;
  status: 'pending' | 'approved' | 'rejected';
  email_verified: boolean;
  createdAt: string;
  profile?: {
    brand_name?: string;
    website?: string;
    bio?: string;
    logo_url?: string;
    social_handles?: {
      instagram?: string;
      tiktok?: string;
      twitter?: string;
      facebook?: string;
    };
    business_name?: string;
    tax_id?: string;
    business_registration_doc_url?: string;
    profile_completed: boolean;
  };
}

interface BrandDetailModalProps {
  brand: Brand | null;
  isOpen: boolean;
  onClose: () => void;
  onApprove: (brandId: string) => void;
  onReject: (brandId: string) => void;
  isLoading: boolean;
}

export function BrandDetailModal({ 
  brand, 
  isOpen, 
  onClose, 
  onApprove, 
  onReject, 
  isLoading 
}: BrandDetailModalProps) {
  const { toast } = useToast();
  const [brandDetails, setBrandDetails] = useState<Brand | null>(null);
  const [detailsLoading, setDetailsLoading] = useState(false);

  useEffect(() => {
    if (brand && isOpen) {
      fetchBrandDetails();
    }
  }, [brand, isOpen]);

  const fetchBrandDetails = async () => {
    if (!brand) return;

    setDetailsLoading(true);
    try {
      // Fetch complete brand details including profile
      const response = await adminAPI.getBrandById(brand._id);
      const brandData = response.data;

      // Ensure we have the most complete data structure
      const completeData = {
        ...brandData,
        profile: brandData.profile || brand.profile || {},
      };

      setBrandDetails(completeData);
    } catch (error: any) {
      console.error('Failed to fetch brand details:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch brand details',
        variant: 'destructive',
      });
      // Fallback to using the brand data we already have
      setBrandDetails(brand);
    } finally {
      setDetailsLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      default:
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
    }
  };

  const handleApprove = () => {
    if (brand) {
      onApprove(brand._id);
    }
  };

  const handleReject = () => {
    if (brand) {
      const reason = prompt('Enter rejection reason (optional):');
      onReject(brand._id);
    }
  };

  if (!brand) return null;

  const displayBrand = brandDetails || brand;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={displayBrand.profile?.logo_url} />
              <AvatarFallback className="bg-slate-600 text-white">
                {displayBrand.profile?.brand_name?.[0] || displayBrand.email[0].toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-bold">
                {displayBrand.profile?.brand_name || 'Unnamed Brand'}
              </h2>
              <div className="flex items-center space-x-2 mt-1">
                {getStatusBadge(displayBrand.status)}
                {displayBrand.email_verified && (
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </div>
            </div>
          </DialogTitle>
          <DialogDescription>
            Application submitted on {new Date(displayBrand.createdAt).toLocaleDateString()}
          </DialogDescription>
        </DialogHeader>

        {detailsLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-slate-600"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Mail className="h-5 w-5 mr-2" />
                  Contact & Application Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium">Email Address:</span>
                    <p className="text-sm text-gray-600 mt-1 font-mono">{displayBrand.email}</p>
                  </div>
                  <div>
                    <span className="font-medium">Email Verification:</span>
                    <div className="mt-1">
                      {displayBrand.email_verified ? (
                        <Badge className="bg-green-100 text-green-800">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Verified
                        </Badge>
                      ) : (
                        <Badge className="bg-red-100 text-red-800">
                          <XCircle className="h-3 w-3 mr-1" />
                          Not Verified
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium">Application Date:</span>
                    <p className="text-sm text-gray-600 mt-1">
                      {new Date(displayBrand.createdAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                  <div>
                    <span className="font-medium">Current Status:</span>
                    <div className="mt-1">
                      {getStatusBadge(displayBrand.status)}
                    </div>
                  </div>
                </div>

                {displayBrand.profile && (
                  <div>
                    <span className="font-medium">Profile Completion:</span>
                    <div className="mt-1">
                      <Badge variant={displayBrand.profile.profile_completed ? "default" : "secondary"}>
                        {displayBrand.profile.profile_completed ? (
                          <>
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Complete
                          </>
                        ) : (
                          <>
                            <Clock className="h-3 w-3 mr-1" />
                            Incomplete
                          </>
                        )}
                      </Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Brand Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Building className="h-5 w-5 mr-2" />
                    Brand Information
                  </div>
                  {displayBrand.profile && (
                    <Badge variant={displayBrand.profile.profile_completed ? "default" : "secondary"}>
                      {displayBrand.profile.profile_completed ? "Complete" : "Incomplete"}
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium">Brand Name:</span>
                    <p className="text-sm text-gray-600 mt-1">
                      {displayBrand.profile?.brand_name || (
                        <span className="text-red-500 italic">Not provided</span>
                      )}
                    </p>
                  </div>
                  <div>
                    <span className="font-medium">Website:</span>
                    <p className="text-sm text-gray-600 mt-1">
                      {displayBrand.profile?.website ? (
                        <a
                          href={displayBrand.profile.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline flex items-center"
                        >
                          {displayBrand.profile.website}
                          <ExternalLink className="h-3 w-3 ml-1" />
                        </a>
                      ) : (
                        <span className="text-red-500 italic">Not provided</span>
                      )}
                    </p>
                  </div>
                </div>

                <div>
                  <span className="font-medium">Brand Bio:</span>
                  <p className="text-sm text-gray-600 mt-1 p-3 bg-gray-50 rounded-md">
                    {displayBrand.profile?.bio || (
                      <span className="text-red-500 italic">Not provided</span>
                    )}
                  </p>
                </div>

                {/* Logo Display */}
                <div>
                  <span className="font-medium">Brand Logo:</span>
                  <div className="mt-2">
                    {displayBrand.profile?.logo_url ? (
                      <div className="flex items-center space-x-3">
                        <img
                          src={displayBrand.profile.logo_url}
                          alt="Brand Logo"
                          className="h-16 w-16 rounded-full object-cover border-2 border-gray-200"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(displayBrand.profile?.logo_url, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          View Full Size
                        </Button>
                      </div>
                    ) : (
                      <span className="text-red-500 italic">No logo uploaded</span>
                    )}
                  </div>
                </div>

                {/* Social Media */}
                <div>
                  <span className="font-medium">Social Media Handles:</span>
                  <div className="mt-2">
                    {displayBrand.profile?.social_handles &&
                     Object.values(displayBrand.profile.social_handles).some(handle => handle) ? (
                      <div className="flex flex-wrap gap-2">
                        {displayBrand.profile.social_handles.instagram && (
                          <Badge variant="outline" className="flex items-center">
                            <Instagram className="h-3 w-3 mr-1" />
                            {displayBrand.profile.social_handles.instagram}
                          </Badge>
                        )}
                        {displayBrand.profile.social_handles.tiktok && (
                          <Badge variant="outline" className="flex items-center">
                            <span className="h-3 w-3 mr-1 text-xs">🎵</span>
                            {displayBrand.profile.social_handles.tiktok}
                          </Badge>
                        )}
                        {displayBrand.profile.social_handles.twitter && (
                          <Badge variant="outline" className="flex items-center">
                            <Twitter className="h-3 w-3 mr-1" />
                            {displayBrand.profile.social_handles.twitter}
                          </Badge>
                        )}
                        {displayBrand.profile.social_handles.facebook && (
                          <Badge variant="outline" className="flex items-center">
                            <Facebook className="h-3 w-3 mr-1" />
                            {displayBrand.profile.social_handles.facebook}
                          </Badge>
                        )}
                      </div>
                    ) : (
                      <span className="text-gray-500 italic">No social media handles provided</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Business Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Business Information
                </CardTitle>
                <CardDescription>
                  Legal business details and verification documents
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium">Business Name:</span>
                    <p className="text-sm text-gray-600 mt-1">
                      {displayBrand.profile?.business_name || (
                        <span className="text-red-500 italic">Not provided</span>
                      )}
                    </p>
                  </div>
                  <div>
                    <span className="font-medium">Tax ID:</span>
                    <p className="text-sm text-gray-600 mt-1 font-mono">
                      {displayBrand.profile?.tax_id || (
                        <span className="text-red-500 italic">Not provided</span>
                      )}
                    </p>
                  </div>
                </div>

                <div>
                  <span className="font-medium">Business Registration Document:</span>
                  <div className="mt-2">
                    {displayBrand.profile?.business_registration_doc_url ? (
                      <div className="flex items-center space-x-3 p-3 bg-green-50 border border-green-200 rounded-md">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="text-sm text-green-700">Document uploaded</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(displayBrand.profile?.business_registration_doc_url, '_blank')}
                        >
                          <FileText className="h-4 w-4 mr-2" />
                          View Document
                        </Button>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-3 p-3 bg-red-50 border border-red-200 rounded-md">
                        <XCircle className="h-5 w-5 text-red-600" />
                        <span className="text-sm text-red-700">No business registration document uploaded</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Profile Completion Summary */}
                <div className="pt-4 border-t">
                  <span className="font-medium">Profile Completion Status:</span>
                  <div className="mt-2 space-y-2">
                    <div className="flex items-center space-x-2">
                      {displayBrand.profile?.brand_name ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="text-sm">Brand Name</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {displayBrand.profile?.website ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="text-sm">Website</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {displayBrand.profile?.bio ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="text-sm">Brand Bio</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {displayBrand.profile?.business_name ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="text-sm">Business Name</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {displayBrand.profile?.tax_id ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="text-sm">Tax ID</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {displayBrand.profile?.logo_url ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="text-sm">Brand Logo</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {displayBrand.profile?.business_registration_doc_url ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      <span className="text-sm">Business Registration Document</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            {displayBrand.status === 'pending' && (
              <div className="flex justify-end space-x-3 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={handleReject}
                  disabled={isLoading}
                  className="text-red-600 border-red-600 hover:bg-red-50"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject
                </Button>
                <Button
                  onClick={handleApprove}
                  disabled={isLoading}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve
                </Button>
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
