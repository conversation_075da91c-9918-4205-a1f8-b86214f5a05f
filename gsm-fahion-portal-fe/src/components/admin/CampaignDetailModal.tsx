import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Title } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  Calendar, 
  Package, 
  Target,
  Gift,
  User,
  FileText,
  Loader2
} from 'lucide-react';

interface Campaign {
  _id: string;
  name: string;
  description: string;
  status: string;
  brand_id: {
    email: string;
    _id: string;
  };
  selected_products: Array<{
    _id: string;
    title: string;
    images: Array<{ src: string }>;
  }>;
  reward_settings: {
    type: string;
    amount: number;
    token?: string;
    daily_limit?: number;
    user_limit?: number;
  };
  missions: Array<{
    type: string;
    instructions?: string;
    min_duration?: number;
  }>;
  submissions_count: number;
  target_submissions?: number;
  createdAt: string;
  admin_notes?: string;
  approved_by?: {
    email: string;
  };
  banner_url?: string;
}

interface CampaignDetailModalProps {
  campaign: Campaign | null;
  isOpen: boolean;
  onClose: () => void;
  onApprove: (campaignId: string, notes?: string) => void;
  onReject: (campaignId: string, notes?: string) => void;
  isLoading: boolean;
}

export function CampaignDetailModal({
  campaign,
  isOpen,
  onClose,
  onApprove,
  onReject,
  isLoading
}: CampaignDetailModalProps) {
  const [notes, setNotes] = useState('');

  if (!campaign) return null;

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case 'active':
        return <Badge className="bg-blue-100 text-blue-800">Active</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      case 'completed':
        return <Badge className="bg-gray-100 text-gray-800">Completed</Badge>;
      default:
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
    }
  };

  const handleApprove = () => {
    onApprove(campaign._id, notes);
    setNotes('');
  };

  const handleReject = () => {
    onReject(campaign._id, notes);
    setNotes('');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto bg-slate-800 border-slate-700">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center justify-between">
            <span>Campaign Details</span>
            {getStatusBadge(campaign.status)}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Campaign Banner */}
          {campaign.banner_url && (
            <div className="w-full h-48 rounded-lg overflow-hidden">
              <img 
                src={campaign.banner_url} 
                alt={campaign.name}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">{campaign.name}</h3>
                <p className="text-slate-300">{campaign.description}</p>
              </div>

              <div className="flex items-center space-x-2 text-slate-400">
                <User className="h-4 w-4" />
                <span>Brand: {campaign.brand_id.email}</span>
              </div>

              <div className="flex items-center space-x-2 text-slate-400">
                <Calendar className="h-4 w-4" />
                <span>Submitted: {new Date(campaign.createdAt).toLocaleDateString()}</span>
              </div>

              {campaign.target_submissions && (
                <div className="flex items-center space-x-2 text-slate-400">
                  <Target className="h-4 w-4" />
                  <span>Target: {campaign.target_submissions} submissions</span>
                </div>
              )}
            </div>

            {/* Reward Settings */}
            <div className="space-y-4">
              <h4 className="text-md font-semibold text-white flex items-center">
                <Gift className="h-4 w-4 mr-2" />
                Reward Settings
              </h4>
              <div className="bg-slate-700 p-4 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-slate-400">Type:</span>
                  <span className="text-white capitalize">{campaign.reward_settings.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Amount:</span>
                  <span className="text-white">
                    {campaign.reward_settings.amount} {campaign.reward_settings.token}
                  </span>
                </div>
                {campaign.reward_settings.daily_limit && (
                  <div className="flex justify-between">
                    <span className="text-slate-400">Daily Limit:</span>
                    <span className="text-white">{campaign.reward_settings.daily_limit}</span>
                  </div>
                )}
                {campaign.reward_settings.user_limit && (
                  <div className="flex justify-between">
                    <span className="text-slate-400">Per User Limit:</span>
                    <span className="text-white">{campaign.reward_settings.user_limit}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <Separator className="bg-slate-700" />

          {/* Selected Products */}
          <div>
            <h4 className="text-md font-semibold text-white flex items-center mb-4">
              <Package className="h-4 w-4 mr-2" />
              Selected Products ({campaign.selected_products.length})
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {campaign.selected_products.map((product) => (
                <div key={product._id} className="bg-slate-700 p-3 rounded-lg flex items-center space-x-3">
                  <div className="w-12 h-12 rounded-md overflow-hidden">
                    <img 
                      src={product.images?.[0]?.src || '/placeholder-product.png'} 
                      alt={product.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <p className="text-white text-sm font-medium">{product.title}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator className="bg-slate-700" />

          {/* Missions */}
          <div>
            <h4 className="text-md font-semibold text-white flex items-center mb-4">
              <FileText className="h-4 w-4 mr-2" />
              Mission Details
            </h4>
            <div className="space-y-3">
              {campaign.missions.map((mission, index) => (
                <div key={index} className="bg-slate-700 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline" className="capitalize">
                      {mission.type} Mission
                    </Badge>
                    {mission.min_duration && (
                      <span className="text-slate-400 text-sm">
                        Min Duration: {mission.min_duration}s
                      </span>
                    )}
                  </div>
                  {mission.instructions && (
                    <p className="text-slate-300 text-sm">{mission.instructions}</p>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Admin Notes */}
          {campaign.admin_notes && (
            <>
              <Separator className="bg-slate-700" />
              <div>
                <h4 className="text-md font-semibold text-white mb-2">Admin Notes</h4>
                <div className="bg-slate-700 p-3 rounded-lg">
                  <p className="text-slate-300">{campaign.admin_notes}</p>
                  {campaign.approved_by && (
                    <p className="text-slate-400 text-sm mt-2">
                      By: {campaign.approved_by.email}
                    </p>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Action Section */}
          {campaign.status === 'pending' && (
            <>
              <Separator className="bg-slate-700" />
              <div className="space-y-4">
                <div>
                  <Label htmlFor="admin-notes" className="text-white">
                    Admin Notes (Optional)
                  </Label>
                  <Textarea
                    id="admin-notes"
                    placeholder="Add notes about your decision..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    className="mt-1 bg-slate-700 border-slate-600 text-white"
                    rows={3}
                  />
                </div>

                <div className="flex space-x-3">
                  <Button
                    onClick={handleApprove}
                    disabled={isLoading}
                    className="bg-green-600 hover:bg-green-700 text-white flex-1"
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    )}
                    Approve Campaign
                  </Button>
                  <Button
                    onClick={handleReject}
                    disabled={isLoading}
                    className="bg-red-600 hover:bg-red-700 text-white flex-1"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject Campaign
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
