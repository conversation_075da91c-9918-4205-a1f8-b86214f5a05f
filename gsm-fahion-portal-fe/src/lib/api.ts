import axios from 'axios';

const API_BASE_URL = 'https://gsm-fashion-portal-f54077544850.herokuapp.com/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use((config) => {
  const currentPath = window.location.pathname;
  const isAdminRoute = currentPath.startsWith('/admin');

  // Use appropriate token based on current route
  const tokenKey = isAdminRoute ? 'admin_token' : 'brand_token';
  const token = localStorage.getItem(tokenKey);

  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const currentPath = window.location.pathname;
      const isAdminRoute = currentPath.startsWith('/admin');

      // Clear appropriate session data
      const tokenKey = isAdminRoute ? 'admin_token' : 'brand_token';
      const userKey = isAdminRoute ? 'admin_user' : 'brand_user';

      localStorage.removeItem(tokenKey);
      localStorage.removeItem(userKey);

      // Redirect to appropriate login page
      if (isAdminRoute) {
        window.location.href = '/admin';
      } else {
        window.location.href = '/auth/login';
      }
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (data: { email: string; password: string }) =>
    api.post('/auth/register', data),
  
  verifyOtp: (data: { email: string; otp: string }) =>
    api.post('/auth/verify-otp', data),
  
  login: (data: { email: string; password: string }) =>
    api.post('/auth/login', data),
  
  adminLogin: (data: { email: string; password: string }) =>
    api.post('/admin/login', data),
};

// Brand API
export const brandAPI = {
  getProfile: () => api.get('/brands/profile'),

  updateProfile: (data: any) => api.post('/brands/update-profile', data),
};

// Upload API
export const uploadAPI = {
  uploadFile: (formData: FormData) => {
    return api.post('/upload/file', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  },

  uploadLogo: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/upload/logo', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  },

  uploadDocument: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/upload/document', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  },
};

// Admin API
export const adminAPI = {
  // Brand management
  getAllBrands: () => api.get('/admin/brands'),
  getBrand: (id: string) => api.get(`/admin/brands/${id}`),
  getBrandById: (id: string) => api.get(`/admin/brands/${id}`),
  approveBrand: (id: string) => api.put(`/admin/brands/${id}/approve`),
  rejectBrand: (id: string, reason?: string) =>
    api.put(`/admin/brands/${id}/reject`, { reason }),

  // Campaign management
  getAllCampaigns: (params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }) => api.get('/admin/campaigns', { params }),
  getCampaign: (id: string) => api.get(`/admin/campaigns/${id}`),
  approveCampaign: (id: string, notes?: string) =>
    api.put(`/admin/campaigns/${id}/approve`, { notes }),
  rejectCampaign: (id: string, notes?: string) =>
    api.put(`/admin/campaigns/${id}/reject`, { notes }),
};

// Products API
export const productsAPI = {
  // Sync products from Shopify
  syncProducts: () => api.post('/products/sync'),

  // Get products with filtering and pagination
  getProducts: (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    product_type?: string;
    tags?: string;
  }) => api.get('/products', { params }),

  // Get single product
  getProduct: (id: string) => api.get(`/products/${id}`),

  // Update product
  updateProduct: (id: string, data: any) => api.put(`/products/${id}`, data),

  // Delete product
  deleteProduct: (id: string) => api.delete(`/products/${id}`),

  // Get product statistics
  getStats: () => api.get('/products/stats/overview'),

  // Get filter options
  getFilterOptions: () => api.get('/products/filters/options'),
};

// CSV Import API
export const csvImportAPI = {
  // Download CSV template
  downloadTemplate: () => api.get('/products/csv/template', {
    responseType: 'blob',
  }),

  // Upload and validate CSV file
  uploadCsv: (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/products/csv/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  },

  // Validate CSV data
  validateCsv: (data: { products: any[]; import_batch_id: string }) =>
    api.post('/products/csv/validate', data),

  // Create draft products
  createDraftProducts: (data: { products: any[]; import_batch_id: string }) =>
    api.post('/products/csv/create-draft', data),

  // Get import batch products
  getImportBatch: (batchId: string) =>
    api.get(`/products/csv/batch/${batchId}`),

  // Update product images
  updateProductImages: (data: {
    product_id: string;
    image_urls: string[];
    primary_image_index?: number;
  }) => api.put('/products/csv/images', data),

  // Update bulk product images
  updateBulkImages: (data: { products: any[] }) =>
    api.put('/products/csv/images/bulk', data),

  // Update product verification types
  updateVerificationTypes: (data: {
    product_ids: string[];
    verification_type: string;
  }) => api.put('/products/csv/verification', data),

  // Finalize import
  finalizeImport: (data: {
    import_batch_id: string;
    default_verification_type?: string;
    publish_immediately?: boolean;
  }) => api.post('/products/csv/finalize', data),

  // Delete import batch
  deleteImportBatch: (batchId: string) =>
    api.delete(`/products/csv/batch/${batchId}`),
};

// Campaigns API
export const campaignsAPI = {
  // Create a new campaign
  createCampaign: (data: any) => api.post('/campaigns', data),

  // Get campaigns with filtering and pagination
  getCampaigns: (params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }) => api.get('/campaigns', { params }),

  // Get single campaign
  getCampaign: (id: string) => api.get(`/campaigns/${id}`),

  // Get campaign statistics
  getStats: () => api.get('/campaigns/stats/overview'),
};



export { api };
export default api;
