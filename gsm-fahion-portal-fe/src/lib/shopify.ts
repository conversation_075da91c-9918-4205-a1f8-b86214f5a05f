import { api } from './api';

export interface ShopifyConnection {
  connected: boolean;
  shopName?: string;
  shopDomain?: string;
  shopEmail?: string;
  connectedAt?: string;
  lastSyncAt?: string;
  totalProducts?: number;
  syncedProducts?: number;
}

export interface ShopifyProduct {
  id: number;
  title: string;
  handle: string;
  description: string;
  vendor: string;
  product_type: string;
  created_at: string;
  updated_at: string;
  published_at: string;
  status: string;
  tags: string;
  variants: ShopifyVariant[];
  images: ShopifyImage[];
  options: ShopifyOption[];
}

export interface ShopifyVariant {
  id: number;
  product_id: number;
  title: string;
  price: string;
  compare_at_price: string | null;
  sku: string;
  inventory_quantity: number;
  weight: number;
  option1: string | null;
  option2: string | null;
  option3: string | null;
  image_id: number | null;
}

export interface ShopifyImage {
  id: number;
  product_id: number;
  src: string;
  alt: string | null;
  position: number;
}

export interface ShopifyOption {
  id: number;
  product_id: number;
  name: string;
  position: number;
  values: string[];
}

export interface ShopifyProductsResponse {
  success: boolean;
  products: ShopifyProduct[];
  pageInfo: {
    next?: string;
    previous?: string;
  };
  shop?: {
    name: string;
    domain: string;
  };
  error?: string;
}

class ShopifyAPI {
  // Initiate Shopify connection
  async connectStore(shopDomain: string) {
    const response = await api.post('/shopify/connect', { shopDomain });
    return response.data;
  }

  // Get connection status
  async getConnectionStatus(): Promise<ShopifyConnection> {
    const response = await api.get('/shopify/status');
    return response.data;
  }

  // Get products from connected Shopify store
  async getProducts(limit = 50, pageInfo?: string): Promise<ShopifyProductsResponse> {
    const params = new URLSearchParams();
    params.append('limit', limit.toString());
    if (pageInfo) {
      params.append('page_info', pageInfo);
    }

    const response = await api.get(`/shopify/products?${params.toString()}`);
    return response.data;
  }

  // Get single product
  async getProduct(productId: string): Promise<{ success: boolean; product?: ShopifyProduct; error?: string }> {
    const response = await api.get(`/shopify/products/${productId}`);
    return response.data;
  }

  // Disconnect Shopify store
  async disconnectStore() {
    const response = await api.delete('/shopify/disconnect');
    return response.data;
  }

  // Test connection
  async testConnection() {
    const response = await api.get('/shopify/test');
    return response.data;
  }

  // Sync products from Shopify to database
  async syncProducts() {
    const response = await api.post('/products/sync');
    return response.data;
  }
}

export const shopifyAPI = new ShopifyAPI();
