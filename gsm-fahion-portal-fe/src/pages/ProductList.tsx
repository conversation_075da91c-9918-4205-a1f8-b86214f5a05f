import { useState, useEffect } from "react";
import { Search, Plus, Filter, Package, QrCode, Tag, Loader2, AlertCircle } from "lucide-react";
import { AppLayout } from "@/components/layout/AppLayout";
import { ProductCard } from "@/components/dashboard/ProductCard";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Link } from "react-router-dom";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { productsAPI } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { useShopify } from "@/contexts/ShopifyContext";

type ProductStatus = 'live' | 'pending' | 'rejected';
type VerificationType = 'manual' | 'qr' | 'nfc';
type ProductSource = 'manual' | 'shopify' | 'csv';

interface Product {
  id: string;
  name: string;
  image: string;
  sku: string;
  status: ProductStatus;
  verificationType: VerificationType;
  source: ProductSource;
  campaigns: number;
  price?: string;
  shopifyId?: number;
  variants?: number;
}

export function ProductList() {
  const [products, setProducts] = useState<Product[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [sourceFilter, setSourceFilter] = useState("all");
  const [verificationFilter, setVerificationFilter] = useState("all");
  const [campaignFilter, setCampaignFilter] = useState("all");
  const { toast } = useToast();
  const { connection, isLoading, isSyncing, syncProducts } = useShopify();

  const loadProductsFromDB = async () => {
    try {
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        search: searchQuery || undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        import_source: sourceFilter !== 'all' ? sourceFilter : undefined,
      };

      const result = await productsAPI.getProducts(params);

      if (result.data.success) {
        // Transform database products to our Product interface
        const transformedProducts: Product[] = result.data.products.map((dbProduct: any) => ({
          id: dbProduct._id,
          shopifyId: dbProduct.shopify_product_id ? parseInt(dbProduct.shopify_product_id) : null,
          name: dbProduct.title,
          image: dbProduct.images?.[0]?.src || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?q=80&w=500',
          sku: dbProduct.variants?.[0]?.sku || dbProduct.sku || `PROD-${dbProduct._id.slice(-6)}`,
          status: dbProduct.status === 'active' ? 'live' : dbProduct.status === 'draft' ? 'pending' : 'rejected',
          verificationType: dbProduct.verification_type || 'qr',
          source: dbProduct.import_source || 'csv',
          campaigns: dbProduct.campaigns_count || 0,
          price: dbProduct.variants?.[0]?.price?.toString() || dbProduct.price?.toString() || '0.00',
          variants: dbProduct.variants?.length || 1
        }));

        setProducts(transformedProducts);
        setPagination(result.data.pagination || {
          page: 1,
          limit: 20,
          total: transformedProducts.length,
          pages: 1
        });
      } else {
        throw new Error(result.data.error || 'Failed to load products');
      }
    } catch (error: any) {
      console.error('Failed to load products:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to load products from database',
        variant: 'destructive',
      });
      setProducts([]);
    }
  };

  const handleSyncProducts = async () => {
    try {
      await syncProducts();
      // Reload products after sync
      await loadProductsFromDB();
    } catch (error) {
      // Error handling is done in the context
    }
  };

  useEffect(() => {
    loadProductsFromDB();
  }, [pagination.page, searchQuery, statusFilter, sourceFilter]);

  // Filter products based on search and filters
  const filteredProducts = products.filter((product) => {
    // Search filter
    if (searchQuery && !product.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !product.sku.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Status filter
    if (statusFilter !== "all" && product.status !== statusFilter) {
      return false;
    }
    
    // Source filter
    if (sourceFilter !== "all" && product.source !== sourceFilter) {
      return false;
    }
    
    // Verification filter
    if (verificationFilter !== "all" && product.verificationType !== verificationFilter) {
      return false;
    }
    
    // Campaign filter
    if (campaignFilter === "with" && product.campaigns === 0) {
      return false;
    } else if (campaignFilter === "without" && product.campaigns > 0) {
      return false;
    }
    
    return true;
  });

  const resetFilters = () => {
    setSearchQuery("");
    setStatusFilter("all");
    setSourceFilter("all");
    setVerificationFilter("all");
    setCampaignFilter("all");
  };

  const getVerificationIcon = (type: VerificationType) => {
    switch (type) {
      case 'qr':
        return <QrCode className="h-3 w-3" />;
      case 'nfc':
        return <Tag className="h-3 w-3" />;
      default:
        return <Package className="h-3 w-3" />;
    }
  };

  const getSourceBadge = (source: ProductSource) => {
    const colors = {
      shopify: "bg-green-100 text-green-800",
      csv: "bg-blue-100 text-blue-800",
      manual: "bg-gray-100 text-gray-800"
    };
    
    return (
      <Badge className={`text-xs ${colors[source]}`}>
        {source.toUpperCase()}
      </Badge>
    );
  };

  return (
    <AppLayout>
      <div className="py-6 px-6 max-w-7xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-brand-dark-purple">Products</h1>
            <p className="text-muted-foreground mt-1">
              {connection.connected
                ? `Manage your product catalog from ${connection.shopName || 'Shopify'} • ${pagination.total} products`
                : 'Connect your Shopify store to manage products'
              }
            </p>
          </div>
          <div className="mt-4 sm:mt-0 flex gap-2 flex-wrap">
            {connection.connected && (
              <Button 
                variant="outline" 
                onClick={handleSyncProducts}
                disabled={isSyncing}
              >
                {isSyncing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Syncing...
                  </>
                ) : (
                  <>
                    <Package className="mr-2 h-4 w-4" />
                    Sync Products
                  </>
                )}
              </Button>
            )}
            <Button variant="outline" asChild>
              <Link to="/products/import">
                <Package className="mr-2 h-4 w-4" /> Import
              </Link>
            </Button>
            <Button className="button-gradient text-white" asChild>
              <Link to="/products/create">
                <Plus className="mr-2 h-4 w-4" /> Add Product
              </Link>
            </Button>
          </div>
        </div>

        {!connection.connected && (
          <Alert className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <Link to="/settings" className="text-blue-600 hover:underline">
                Connect your Shopify store
              </Link> to sync and manage your products.
            </AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading products...</span>
          </div>
        ) : (
          <>
            <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search products..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="live">Live</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select value={sourceFilter} onValueChange={setSourceFilter}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="Source" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sources</SelectItem>
                    <SelectItem value="shopify">Shopify</SelectItem>
                    <SelectItem value="csv">CSV Import</SelectItem>
                    <SelectItem value="manual">Manual Entry</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="mb-4 text-sm text-muted-foreground">
              Showing {filteredProducts.length} of {products.length} products
            </div>

            {filteredProducts.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {filteredProducts.map((product) => (
                  <div key={product.id} className="flex flex-col">
                    <ProductCard
                      id={product.id}
                      name={product.name}
                      image={product.image}
                      sku={product.sku}
                      status={product.status}
                      campaigns={product.campaigns}
                    />
                    <div className="mt-1 flex items-center justify-between px-1">
                      <div className="flex items-center">
                        {getVerificationIcon(product.verificationType)}
                        <span className="text-xs ml-1 text-muted-foreground">
                          {product.verificationType.toUpperCase()}
                        </span>
                      </div>
                      {getSourceBadge(product.source)}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Package className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No products found</h3>
                <p className="text-muted-foreground mb-6">
                  {products.length === 0 && !searchQuery && statusFilter === 'all'
                    ? 'Start by importing products from Shopify or uploading a CSV file'
                    : 'No products match your current filter criteria'
                  }
                </p>
                <div className="flex gap-2 justify-center">
                  <Button asChild variant="outline">
                    <Link to="/import">Import Products</Link>
                  </Button>
                  {connection.connected && (
                    <Button onClick={handleSyncProducts} disabled={isSyncing}>
                      {isSyncing ? 'Syncing...' : 'Sync from Shopify'}
                    </Button>
                  )}
                  {(searchQuery || statusFilter !== 'all') && (
                    <Button onClick={resetFilters} variant="outline">
                      Reset Filters
                    </Button>
                  )}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </AppLayout>
  );
}

export default ProductList;
