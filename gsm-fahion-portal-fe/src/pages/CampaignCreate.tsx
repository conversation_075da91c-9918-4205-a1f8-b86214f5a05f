
import { useState, useEffect } from "react";
import { ChevronLeft, Upload, X, Check } from "lucide-react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { productsAPI, campaignsAPI, uploadAPI } from "@/lib/api";

interface Product {
  _id: string;
  title: string;
  images: { src: string; alt?: string }[];
  variants: { sku?: string }[];
}

export function CampaignCreate() {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [bannerFile, setBannerFile] = useState<File | null>(null);
  const [bannerUrl, setBannerUrl] = useState<string>("");
  const { toast } = useToast();
  const navigate = useNavigate();

  const [campaign, setCampaign] = useState({
    name: "",
    description: "",
    banner_url: "",
    selected_products: [] as string[],
    missions: [{
      type: "video",
      title: "",
      description: "",
      instructions: "",
      required: false,
      min_duration: 15
    }],
    reward_settings: {
      type: "crypto",
      amount: 10,
      token: "USDC",
      daily_limit: 1000,
      user_limit: 3,
      start_date: "",
      end_date: "",
      approval_required: false
    },
    target_submissions: 100
  });

  const handleStepChange = (nextStep: number) => {
    setStep(nextStep);
  };

  const updateCampaign = (field: string, value: any) => {
    setCampaign({ ...campaign, [field]: value });
  };

  const updateMission = (field: string, value: any) => {
    const updatedMissions = [...campaign.missions];
    updatedMissions[0] = { ...updatedMissions[0], [field]: value };
    setCampaign({ ...campaign, missions: updatedMissions });
  };

  const updateRewardSettings = (field: string, value: any) => {
    setCampaign({
      ...campaign,
      reward_settings: { ...campaign.reward_settings, [field]: value }
    });
  };

  // Load products for the brand
  const loadProducts = async () => {
    try {
      const result = await productsAPI.getProducts({ limit: 100 });
      if (result.data.success) {
        setProducts(result.data.products);
      }
    } catch (error) {
      console.error('Failed to load products:', error);
      toast({
        title: 'Error',
        description: 'Failed to load products',
        variant: 'destructive',
      });
    }
  };

  // Handle banner file upload
  const handleBannerUpload = async (file: File) => {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('file', file);

      const result = await uploadAPI.uploadFile(formData);
      if (result.data.success) {
        setBannerUrl(result.data.url);
        updateCampaign('banner_url', result.data.url);
        toast({
          title: 'Success',
          description: 'Banner uploaded successfully',
        });
      }
    } catch (error) {
      console.error('Failed to upload banner:', error);
      toast({
        title: 'Error',
        description: 'Failed to upload banner',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle product selection
  const toggleProductSelection = (productId: string) => {
    const updatedSelection = selectedProducts.includes(productId)
      ? selectedProducts.filter(id => id !== productId)
      : [...selectedProducts, productId];

    setSelectedProducts(updatedSelection);
    updateCampaign('selected_products', updatedSelection);
  };

  // Create campaign
  const createCampaign = async () => {
    try {
      setLoading(true);

      if (!campaign.name || !campaign.description) {
        toast({
          title: 'Error',
          description: 'Please fill in campaign name and description',
          variant: 'destructive',
        });
        return;
      }

      if (selectedProducts.length === 0) {
        toast({
          title: 'Error',
          description: 'Please select at least one product',
          variant: 'destructive',
        });
        return;
      }

      const result = await campaignsAPI.createCampaign(campaign);

      if (result.data.success) {
        toast({
          title: 'Success',
          description: 'Campaign created successfully and submitted for approval',
        });
        navigate('/campaigns');
      } else {
        throw new Error(result.data.error || 'Failed to create campaign');
      }
    } catch (error: any) {
      console.error('Failed to create campaign:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to create campaign',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProducts();
  }, []);

  return (
    <AppLayout>
      <div className="py-6 px-6 max-w-4xl mx-auto">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <a href="/campaigns">
              <ChevronLeft className="h-5 w-5" />
            </a>
          </Button>
          <h1 className="text-3xl font-bold text-brand-dark-purple">Create Campaign</h1>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex justify-between items-center mb-8">
            <div className="flex gap-2">
              <div className={`size-8 rounded-full flex items-center justify-center ${step >= 1 ? "bg-brand-purple text-white" : "bg-gray-200 text-gray-500"}`}>
                1
              </div>
              <div className={`h-1 w-12 self-center ${step >= 2 ? "bg-brand-purple" : "bg-gray-200"}`}></div>
              <div className={`size-8 rounded-full flex items-center justify-center ${step >= 2 ? "bg-brand-purple text-white" : "bg-gray-200 text-gray-500"}`}>
                2
              </div>
              <div className={`h-1 w-12 self-center ${step >= 3 ? "bg-brand-purple" : "bg-gray-200"}`}></div>
              <div className={`size-8 rounded-full flex items-center justify-center ${step >= 3 ? "bg-brand-purple text-white" : "bg-gray-200 text-gray-500"}`}>
                3
              </div>
              <div className={`h-1 w-12 self-center ${step >= 4 ? "bg-brand-purple" : "bg-gray-200"}`}></div>
              <div className={`size-8 rounded-full flex items-center justify-center ${step >= 4 ? "bg-brand-purple text-white" : "bg-gray-200 text-gray-500"}`}>
                4
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              Step {step} of 4
            </div>
          </div>

          {step === 1 && (
            <div className="space-y-6 animate-fade-in">
              <h2 className="text-xl font-semibold mb-4">Campaign Details</h2>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Campaign Name</Label>
                  <Input 
                    id="name" 
                    placeholder="Summer Collection Launch"
                    value={campaign.name}
                    onChange={(e) => updateCampaign('name', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea 
                    id="description" 
                    placeholder="Describe your campaign and what you're looking for from creators"
                    rows={4}
                    value={campaign.description}
                    onChange={(e) => updateCampaign('description', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="banner">Campaign Banner</Label>
                  {bannerUrl ? (
                    <div className="mt-1 relative">
                      <img
                        src={bannerUrl}
                        alt="Campaign banner"
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={() => {
                          setBannerUrl("");
                          setBannerFile(null);
                          updateCampaign('banner_url', '');
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <div className="mt-1 border-2 border-dashed border-gray-300 rounded-lg p-6 flex justify-center">
                      <div className="text-center">
                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="flex justify-center mt-4">
                          <Button
                            variant="outline"
                            disabled={loading}
                            onClick={() => document.getElementById('banner-upload')?.click()}
                          >
                            {loading ? 'Uploading...' : 'Upload Image'}
                          </Button>
                          <input
                            id="banner-upload"
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={(e) => {
                              const file = e.target.files?.[0];
                              if (file) {
                                setBannerFile(file);
                                handleBannerUpload(file);
                              }
                            }}
                          />
                        </div>
                        <p className="text-xs text-gray-500 mt-2">PNG, JPG, GIF up to 10MB</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <Button 
                  className="button-gradient text-white" 
                  onClick={() => handleStepChange(2)}
                >
                  Next: Select Products
                </Button>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6 animate-fade-in">
              <h2 className="text-xl font-semibold mb-4">Select Products</h2>
              {products.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No products found. Please import products first.</p>
                  <Button variant="outline" className="mt-4" onClick={() => navigate('/products/import')}>
                    Import Products
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {products.map((product) => {
                    const isSelected = selectedProducts.includes(product._id);
                    return (
                      <Card
                        key={product._id}
                        className={`p-4 cursor-pointer border-2 transition-all ${
                          isSelected
                            ? 'border-brand-purple bg-brand-purple/5'
                            : 'border-gray-200 hover:border-brand-purple/40'
                        }`}
                        onClick={() => toggleProductSelection(product._id)}
                      >
                        <div className="flex items-center">
                          <div className="h-16 w-16 rounded-md overflow-hidden mr-4">
                            <img
                              src={product.images?.[0]?.src || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?q=80&w=500'}
                              alt={product.title}
                              className="h-full w-full object-cover"
                            />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold">{product.title}</h3>
                            <p className="text-sm text-muted-foreground">
                              {product.variants?.[0]?.sku || `PROD-${product._id.slice(-6)}`}
                            </p>
                          </div>
                          {isSelected && (
                            <Check className="h-5 w-5 text-brand-purple" />
                          )}
                        </div>
                      </Card>
                    );
                  })}
                </div>
              )}

              {selectedProducts.length > 0 && (
                <div className="bg-brand-purple/5 p-4 rounded-lg">
                  <p className="text-sm font-medium">
                    {selectedProducts.length} product{selectedProducts.length > 1 ? 's' : ''} selected
                  </p>
                </div>
              )}

              <div className="flex justify-between mt-6">
                <Button variant="outline" onClick={() => handleStepChange(1)}>
                  Back
                </Button>
                <Button 
                  className="button-gradient text-white" 
                  onClick={() => handleStepChange(3)}
                >
                  Next: Missions Setup
                </Button>
              </div>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-6 animate-fade-in">
              <h2 className="text-xl font-semibold mb-4">Missions Setup</h2>
              <Tabs defaultValue="video">
                <TabsList className="w-full bg-muted grid grid-cols-3 mb-6">
                  <TabsTrigger value="video">Video Submission</TabsTrigger>
                  <TabsTrigger value="social">Social Media</TabsTrigger>
                  <TabsTrigger value="custom">Custom Mission</TabsTrigger>
                </TabsList>
                <TabsContent value="video">
                  <Card className="p-4">
                    <div className="space-y-4">
                      <div>
                        <Label>Mission Type</Label>
                        <p className="text-sm text-muted-foreground mt-1">User submits a video showcasing your product</p>
                      </div>
                      <div>
                        <Label htmlFor="min-duration">Minimum Video Duration (seconds)</Label>
                        <Input
                          id="min-duration"
                          type="number"
                          value={campaign.missions[0].min_duration}
                          min={5}
                          max={300}
                          onChange={(e) => updateMission('min_duration', parseInt(e.target.value))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="instructions">Instructions for creators</Label>
                        <Textarea
                          id="instructions"
                          placeholder="Tell creators what you'd like them to showcase in their videos"
                          rows={3}
                          value={campaign.missions[0].instructions}
                          onChange={(e) => updateMission('instructions', e.target.value)}
                        />
                      </div>
                      <div className="flex items-center">
                        <input
                          id="required"
                          type="checkbox"
                          className="h-4 w-4 mr-2"
                          checked={campaign.missions[0].required}
                          onChange={(e) => updateMission('required', e.target.checked)}
                        />
                        <Label htmlFor="required">Required for reward</Label>
                      </div>
                    </div>
                  </Card>
                </TabsContent>
                <TabsContent value="social">
                  <Card className="p-4">
                    <div className="space-y-4">
                      <div>
                        <Label>Mission Type</Label>
                        <p className="text-sm text-muted-foreground mt-1">User shares content on social media platforms</p>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex items-center space-x-2">
                          <input id="instagram" type="checkbox" className="h-4 w-4" />
                          <Label htmlFor="instagram">Instagram Post</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input id="tiktok" type="checkbox" className="h-4 w-4" />
                          <Label htmlFor="tiktok">TikTok Video</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <input id="twitter" type="checkbox" className="h-4 w-4" />
                          <Label htmlFor="twitter">Twitter Post</Label>
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="hashtag">Required Hashtag</Label>
                        <Input id="hashtag" placeholder="#YourCampaignHashtag" />
                      </div>
                      <div>
                        <Label htmlFor="mention">Required Mention</Label>
                        <Input id="mention" placeholder="@yourbrand" />
                      </div>
                    </div>
                  </Card>
                </TabsContent>
                <TabsContent value="custom">
                  <Card className="p-4">
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="custom-mission">Custom Mission Title</Label>
                        <Input id="custom-mission" placeholder="Follow our Instagram account" />
                      </div>
                      <div>
                        <Label htmlFor="custom-description">Description</Label>
                        <Textarea 
                          id="custom-description" 
                          placeholder="Describe what users need to do to complete this mission"
                          rows={3}
                        />
                      </div>
                      <div>
                        <Label htmlFor="verification">Verification Method</Label>
                        <select id="verification" className="w-full p-2 border rounded">
                          <option>Screenshot upload</option>
                          <option>Manual verification</option>
                          <option>URL submission</option>
                        </select>
                      </div>
                    </div>
                  </Card>
                </TabsContent>
              </Tabs>

              <div className="flex justify-between mt-6">
                <Button variant="outline" onClick={() => handleStepChange(2)}>
                  Back
                </Button>
                <Button 
                  className="button-gradient text-white" 
                  onClick={() => handleStepChange(4)}
                >
                  Next: Reward Setup
                </Button>
              </div>
            </div>
          )}

          {step === 4 && (
            <div className="space-y-6 animate-fade-in">
              <h2 className="text-xl font-semibold mb-4">Reward Setup</h2>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="reward-type">Reward Type</Label>
                  <select
                    id="reward-type"
                    className="w-full p-2 border rounded"
                    value={campaign.reward_settings.type}
                    onChange={(e) => updateRewardSettings('type', e.target.value)}
                  >
                    <option value="crypto">Crypto Token</option>
                    <option value="nft">NFT</option>
                    <option value="points">Points</option>
                  </select>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="amount">Amount per Submission</Label>
                    <Input
                      id="amount"
                      type="number"
                      value={campaign.reward_settings.amount}
                      onChange={(e) => updateRewardSettings('amount', parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="token">Token</Label>
                    <select
                      id="token"
                      className="w-full p-2 border rounded"
                      value={campaign.reward_settings.token}
                      onChange={(e) => updateRewardSettings('token', e.target.value)}
                    >
                      <option value="USDC">USDC</option>
                      <option value="ETH">ETH</option>
                      <option value="BRAND">BRAND</option>
                    </select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="daily-limit">Daily Reward Limit</Label>
                    <Input
                      id="daily-limit"
                      type="number"
                      value={campaign.reward_settings.daily_limit}
                      onChange={(e) => updateRewardSettings('daily_limit', parseInt(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="user-limit">Per User Limit</Label>
                    <Input
                      id="user-limit"
                      type="number"
                      value={campaign.reward_settings.user_limit}
                      onChange={(e) => updateRewardSettings('user_limit', parseInt(e.target.value))}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start-date">Start Date</Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={campaign.reward_settings.start_date}
                      onChange={(e) => updateRewardSettings('start_date', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="end-date">End Date</Label>
                    <Input
                      id="end-date"
                      type="date"
                      value={campaign.reward_settings.end_date}
                      onChange={(e) => updateRewardSettings('end_date', e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex items-center">
                  <input
                    id="approval-required"
                    type="checkbox"
                    className="h-4 w-4 mr-2"
                    checked={campaign.reward_settings.approval_required}
                    onChange={(e) => updateRewardSettings('approval_required', e.target.checked)}
                  />
                  <Label htmlFor="approval-required">Approval required before sending rewards</Label>
                </div>
              </div>

              <div className="flex justify-between mt-6">
                <Button variant="outline" onClick={() => handleStepChange(3)}>
                  Back
                </Button>
                <Button
                  className="button-gradient text-white"
                  onClick={createCampaign}
                  disabled={loading}
                >
                  {loading ? 'Creating...' : 'Create Campaign'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
}

export default CampaignCreate;
