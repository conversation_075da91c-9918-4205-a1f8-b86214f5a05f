
import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { AppLayout } from "@/components/layout/AppLayout";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { ProductDetailContent, ProductDetailProps, VerificationType } from "@/components/products/ProductDetailContent";
import { productsAPI } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";

export function ProductDetail() {
  const { id } = useParams<{ id: string }>();
  const [product, setProduct] = useState<ProductDetailProps | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchProduct = async () => {
      if (!id) {
        setError("Product ID is required");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const response = await productsAPI.getProduct(id);
        const data = response.data;

        if (data.success && data.product) {
          // Transform backend product data to match ProductDetailProps interface
          const backendProduct = data.product;

          setProduct({
            id: backendProduct._id || backendProduct.id,
            name: backendProduct.title,
            description: backendProduct.description || "No description available",
            sku: backendProduct.variants?.[0]?.sku || "N/A",
            price: backendProduct.variants?.[0]?.price || 0,
            currency: "USD", // Default currency
            status: backendProduct.status === "active" ? "live" : backendProduct.status,
            verificationType: (backendProduct.verification_type as VerificationType) || "qr",
            images: backendProduct.images?.map((img: any) => img.src) || [],
            campaigns: [] // TODO: Add campaigns data when available
          });
        } else {
          setError(data.error || "Product not found");
        }
      } catch (err: any) {
        console.error("Failed to fetch product:", err);
        setError(err.message || "Failed to load product");
        toast({
          title: "Error",
          description: "Failed to load product details",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id, toast]);

  if (loading) {
    return (
      <AppLayout>
        <div className="py-6 px-6 max-w-7xl mx-auto">
          <div className="animate-pulse">
            <div className="h-10 w-56 bg-gray-200 rounded mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <div className="aspect-[4/3] bg-gray-200 rounded-lg mb-6"></div>
                <div className="h-64 bg-gray-200 rounded-lg mb-6"></div>
                <div className="h-48 bg-gray-200 rounded-lg"></div>
              </div>
              <div>
                <div className="h-48 bg-gray-200 rounded-lg mb-6"></div>
                <div className="h-64 bg-gray-200 rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }
  
  if (error || (!loading && !product)) {
    return (
      <AppLayout>
        <div className="py-6 px-6 max-w-7xl mx-auto">
          <div className="flex items-center mb-6">
            <Button variant="ghost" size="icon" asChild className="mr-2">
              <Link to="/products">
                <ChevronLeft className="h-5 w-5" />
              </Link>
            </Button>
            <div>
              <p className="text-sm text-muted-foreground">Product Details</p>
              <h1 className="text-xl font-semibold">Error</h1>
            </div>
          </div>

          <div className="text-center py-12">
            <h2 className="text-2xl font-bold mb-2">
              {error ? "Failed to Load Product" : "Product Not Found"}
            </h2>
            <p className="text-muted-foreground mb-6">
              {error || "The product you're looking for doesn't exist or has been removed."}
              {error === "Product not found" && (
                <span className="block mt-2 text-sm">
                  Make sure you have synced your products from Shopify first.
                </span>
              )}
            </p>
            <div className="space-x-2">
              <Button asChild>
                <Link to="/products">Back to Products</Link>
              </Button>
              <Button variant="outline" asChild>
                <Link to="/settings">Check Shopify Connection</Link>
              </Button>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="py-6 px-6 max-w-7xl mx-auto">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link to="/products">
              <ChevronLeft className="h-5 w-5" />
            </Link>
          </Button>
          <div>
            <p className="text-sm text-muted-foreground">Product Details</p>
            <h1 className="text-xl font-semibold">{product.name}</h1>
          </div>
        </div>
        
        <ProductDetailContent {...product} />
      </div>
    </AppLayout>
  );
}

export default ProductDetail;
