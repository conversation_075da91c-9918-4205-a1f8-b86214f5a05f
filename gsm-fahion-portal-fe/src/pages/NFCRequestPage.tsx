
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  ChevronLeft,
  Truck,
  QrCode,
  CheckCircle,
  Package,
  Clock,
  CreditCard
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";

type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'activated';

interface NFCOrder {
  id: string;
  quantity: number;
  status: OrderStatus;
  orderDate: string;
  estimatedDelivery?: string;
  trackingNumber?: string;
}

export function NFCRequestPage() {
  const { toast } = useToast();
  const [step, setStep] = useState(1);
  const [quantity, setQuantity] = useState(100);
  const [chipType, setChipType] = useState("standard");
  
  const [orders, setOrders] = useState<NFCOrder[]>([
    {
      id: "NFC-2025-001",
      quantity: 150,
      status: "shipped",
      orderDate: "May 15, 2025",
      estimatedDelivery: "May 25, 2025",
      trackingNumber: "TRK123456789"
    },
    {
      id: "NFC-2025-002",
      quantity: 50,
      status: "processing",
      orderDate: "May 20, 2025",
      estimatedDelivery: "June 1, 2025"
    }
  ]);
  
  const getStatusBadge = (status: OrderStatus) => {
    const statusMap = {
      pending: <Badge variant="outline" className="bg-yellow-50 text-yellow-700">Pending</Badge>,
      processing: <Badge variant="outline" className="bg-blue-50 text-blue-700">Processing</Badge>,
      shipped: <Badge variant="outline" className="bg-purple-50 text-purple-700">Shipped</Badge>,
      delivered: <Badge variant="outline" className="bg-green-50 text-green-700">Delivered</Badge>,
      activated: <Badge variant="outline" className="bg-green-100 text-green-800">Activated</Badge>
    };
    
    return statusMap[status];
  };
  
  const nextStep = () => {
    setStep(step + 1);
  };
  
  const prevStep = () => {
    setStep(step - 1);
  };
  
  const handleSubmitOrder = () => {
    const newOrder: NFCOrder = {
      id: `NFC-2025-${(orders.length + 3).toString().padStart(3, '0')}`,
      quantity,
      status: 'pending',
      orderDate: "May 23, 2025",
      estimatedDelivery: "June 5, 2025"
    };
    
    setOrders([...orders, newOrder]);
    setStep(5); // Go to success
    
    toast({
      title: "Order placed successfully",
      description: `Your order of ${quantity} NFC chips has been placed.`,
    });
  };
  
  const renderStepIndicator = () => (
    <div className="flex justify-between items-center mb-8">
      <div className="flex gap-2">
        <div className={`size-8 rounded-full flex items-center justify-center ${step >= 1 ? "bg-brand-purple text-white" : "bg-gray-200 text-gray-500"}`}>
          1
        </div>
        <div className={`h-1 w-12 self-center ${step >= 2 ? "bg-brand-purple" : "bg-gray-200"}`}></div>
        <div className={`size-8 rounded-full flex items-center justify-center ${step >= 2 ? "bg-brand-purple text-white" : "bg-gray-200 text-gray-500"}`}>
          2
        </div>
        <div className={`h-1 w-12 self-center ${step >= 3 ? "bg-brand-purple" : "bg-gray-200"}`}></div>
        <div className={`size-8 rounded-full flex items-center justify-center ${step >= 3 ? "bg-brand-purple text-white" : "bg-gray-200 text-gray-500"}`}>
          3
        </div>
        <div className={`h-1 w-12 self-center ${step >= 4 ? "bg-brand-purple" : "bg-gray-200"}`}></div>
        <div className={`size-8 rounded-full flex items-center justify-center ${step >= 4 ? "bg-brand-purple text-white" : "bg-gray-200 text-gray-500"}`}>
          4
        </div>
      </div>
      <div className="text-sm text-muted-foreground">
        Step {step} of 4
      </div>
    </div>
  );
  
  return (
    <AppLayout>
      <div className="py-6 px-6 max-w-4xl mx-auto">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link to="/">
              <ChevronLeft className="h-5 w-5" />
            </Link>
          </Button>
          <h1 className="text-3xl font-bold text-brand-dark-purple">NFC Chips</h1>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <Card className="p-4 lg:col-span-2">
            <div className="flex space-x-2 mb-3">
              <h2 className="text-xl font-semibold">My NFC Orders</h2>
              <Badge variant="outline" className="bg-blue-50 text-blue-700">{orders.length} orders</Badge>
            </div>
            
            {orders.length > 0 ? (
              <div className="space-y-4">
                {orders.map(order => (
                  <Card key={order.id} className="p-4 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-semibold">{order.id}</h3>
                          {getStatusBadge(order.status)}
                        </div>
                        <p className="text-sm text-muted-foreground">Ordered on {order.orderDate}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{order.quantity} chips</p>
                        {order.estimatedDelivery && (
                          <p className="text-sm text-muted-foreground">Est. delivery: {order.estimatedDelivery}</p>
                        )}
                      </div>
                    </div>
                    
                    {order.status === 'shipped' && (
                      <div className="mt-3 pt-3 border-t">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <Truck className="h-4 w-4 mr-2 text-brand-purple" />
                            <span className="text-sm">In transit</span>
                          </div>
                          <Button variant="link" size="sm" className="p-0 h-auto">
                            Track Package
                          </Button>
                        </div>
                      </div>
                    )}
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">You haven't ordered any NFC chips yet.</p>
              </div>
            )}
          </Card>
          
          <Card className="p-4">
            <h2 className="text-lg font-semibold mb-3">Order New Chips</h2>
            <p className="text-sm text-muted-foreground mb-4">NFC chips allow users to verify authentic ownership of your products.</p>
            <Button className="w-full button-gradient text-white" onClick={() => setStep(1)}>
              Place New Order
            </Button>
          </Card>
        </div>
        
        {step < 5 && (
          <Card className="p-6">
            {renderStepIndicator()}
            
            {step === 1 && (
              <div className="space-y-6 animate-fade-in">
                <h2 className="text-xl font-semibold mb-4">Select Chip Quantity</h2>
                
                <div className="space-y-4">
                  <div>
                    <label htmlFor="quantity" className="block text-sm font-medium mb-1">Number of NFC Chips</label>
                    <div className="flex items-center">
                      <Input
                        id="quantity"
                        type="number"
                        value={quantity}
                        onChange={(e) => setQuantity(parseInt(e.target.value) || 0)}
                        min={10}
                        className="w-32"
                      />
                      <span className="ml-2 text-sm text-muted-foreground">chips</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">Minimum order: 10 chips</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Chip Type</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <Card 
                        className={`p-4 cursor-pointer border-2 ${chipType === 'standard' ? 'border-brand-purple' : 'border-transparent'}`}
                        onClick={() => setChipType('standard')}
                      >
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                            <QrCode className="h-4 w-4 text-brand-purple" />
                          </div>
                          <div>
                            <p className="font-medium">Standard Chips</p>
                            <p className="text-xs text-muted-foreground">Basic NFC verification</p>
                          </div>
                        </div>
                      </Card>
                      
                      <Card 
                        className={`p-4 cursor-pointer border-2 ${chipType === 'gsm' ? 'border-brand-purple' : 'border-transparent'}`}
                        onClick={() => setChipType('gsm')}
                      >
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <Package className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium">GSM Enhanced</p>
                            <p className="text-xs text-muted-foreground">With location tracking</p>
                          </div>
                        </div>
                      </Card>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end mt-6">
                  <Button onClick={nextStep}>
                    Next Step
                  </Button>
                </div>
              </div>
            )}
            
            {step === 2 && (
              <div className="space-y-6 animate-fade-in">
                <h2 className="text-xl font-semibold mb-4">Batch Configuration</h2>
                
                <div className="bg-gray-50 p-4 rounded-lg mb-4">
                  <h3 className="text-sm font-medium mb-2">Chip Batch Preview</h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full text-xs">
                      <thead>
                        <tr className="bg-gray-100">
                          <th className="p-2 text-left">Index</th>
                          <th className="p-2 text-left">Chip ID</th>
                          <th className="p-2 text-left">URL</th>
                        </tr>
                      </thead>
                      <tbody>
                        {[...Array(5)].map((_, i) => (
                          <tr key={i} className="border-t border-gray-200">
                            <td className="p-2">{i + 1}</td>
                            <td className="p-2 font-mono">NFC-{(1000 + i).toString()}</td>
                            <td className="p-2 font-mono text-brand-purple">verify.brand.com/nfc/{(1000 + i).toString()}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    + {quantity - 5} more chips (showing first 5)
                  </p>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <label htmlFor="prefix" className="block text-sm font-medium mb-1">Chip ID Prefix (Optional)</label>
                    <Input
                      id="prefix"
                      placeholder="BRAND-"
                      className="max-w-xs"
                    />
                    <p className="text-xs text-muted-foreground mt-1">Custom prefix for your chip IDs</p>
                  </div>
                  
                  <div>
                    <label htmlFor="csv" className="block text-sm font-medium mb-1">Upload Custom IDs (Optional)</label>
                    <div className="flex items-center">
                      <Input
                        id="csv"
                        type="file"
                        className="max-w-xs"
                      />
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">Upload a CSV with custom chip IDs</p>
                  </div>
                </div>
                
                <div className="flex justify-between mt-6">
                  <Button variant="outline" onClick={prevStep}>
                    Back
                  </Button>
                  <Button onClick={nextStep}>
                    Next Step
                  </Button>
                </div>
              </div>
            )}
            
            {step === 3 && (
              <div className="space-y-6 animate-fade-in">
                <h2 className="text-xl font-semibold mb-4">Pricing & Shipping</h2>
                
                <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                  <div className="flex justify-between">
                    <span>NFC Chips ({quantity} × $2.50)</span>
                    <span>${(quantity * 2.5).toFixed(2)}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span>Setup Fee</span>
                    <span>$25.00</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span>Shipping</span>
                    <span>$15.00</span>
                  </div>
                  
                  <Separator className="my-2" />
                  
                  <div className="flex justify-between font-medium">
                    <span>Total</span>
                    <span>${(quantity * 2.5 + 40).toFixed(2)}</span>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Shipping Address</label>
                    <Input
                      placeholder="1234 Main Street"
                      className="mb-2"
                    />
                    <div className="grid grid-cols-2 gap-2">
                      <Input placeholder="City" />
                      <Input placeholder="ZIP Code" />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Estimated Delivery</label>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2 text-brand-purple" />
                      <span>7-10 business days</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-between mt-6">
                  <Button variant="outline" onClick={prevStep}>
                    Back
                  </Button>
                  <Button onClick={nextStep}>
                    Next Step
                  </Button>
                </div>
              </div>
            )}
            
            {step === 4 && (
              <div className="space-y-6 animate-fade-in">
                <h2 className="text-xl font-semibold mb-4">Payment</h2>
                
                <div className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-3 mb-4">
                    <CreditCard className="h-5 w-5 text-brand-purple" />
                    <h3 className="font-medium">Payment Details</h3>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="card" className="block text-sm font-medium mb-1">Card Number</label>
                      <Input
                        id="card"
                        placeholder="4242 4242 4242 4242"
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label htmlFor="expiry" className="block text-sm font-medium mb-1">Expiry Date</label>
                        <Input
                          id="expiry"
                          placeholder="MM/YY"
                        />
                      </div>
                      <div>
                        <label htmlFor="cvc" className="block text-sm font-medium mb-1">CVC</label>
                        <Input
                          id="cvc"
                          placeholder="123"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium mb-1">Name on Card</label>
                      <Input
                        id="name"
                        placeholder="John Smith"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium mb-2">Order Summary</h3>
                  <div className="flex justify-between text-sm mb-1">
                    <span>{quantity} NFC Chips</span>
                    <span>${(quantity * 2.5).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Fees & Shipping</span>
                    <span>$40.00</span>
                  </div>
                  <div className="flex justify-between font-medium mt-2">
                    <span>Total</span>
                    <span>${(quantity * 2.5 + 40).toFixed(2)}</span>
                  </div>
                </div>
                
                <div className="flex justify-between mt-6">
                  <Button variant="outline" onClick={prevStep}>
                    Back
                  </Button>
                  <Button className="button-gradient text-white" onClick={handleSubmitOrder}>
                    Place Order
                  </Button>
                </div>
              </div>
            )}
          </Card>
        )}
        
        {step === 5 && (
          <Card className="p-8 text-center">
            <div className="flex flex-col items-center">
              <div className="h-20 w-20 rounded-full bg-green-100 flex items-center justify-center mb-6">
                <CheckCircle className="h-10 w-10 text-green-600" />
              </div>
              <h2 className="text-2xl font-semibold mb-2">Order Placed Successfully!</h2>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Your order for {quantity} NFC chips has been placed. You'll receive an email confirmation with tracking details soon.
              </p>
              
              <div className="bg-gray-50 p-4 rounded-lg max-w-md w-full mb-6">
                <h3 className="font-medium mb-3">Order Summary</h3>
                <div className="flex justify-between mb-2">
                  <span>Order ID:</span>
                  <span className="font-medium">NFC-2025-003</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span>Quantity:</span>
                  <span>{quantity} chips</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span>Status:</span>
                  <Badge variant="outline" className="bg-yellow-50 text-yellow-700">Pending</Badge>
                </div>
                <div className="flex justify-between mb-2">
                  <span>Expected Delivery:</span>
                  <span>June 5, 2025</span>
                </div>
              </div>
              
              <div className="flex gap-4">
                <Button variant="outline" asChild>
                  <Link to="/">Back to Dashboard</Link>
                </Button>
                <Button asChild>
                  <Link to="/products">Manage Products</Link>
                </Button>
              </div>
            </div>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}

export default NFCRequestPage;
