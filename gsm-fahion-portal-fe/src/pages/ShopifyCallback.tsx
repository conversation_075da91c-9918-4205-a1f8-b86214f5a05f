import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useShopify } from '@/contexts/ShopifyContext';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

export const ShopifyCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { isAuthenticated, isLoading } = useAuth();
  const { checkConnection } = useShopify();
  const { toast } = useToast();

  useEffect(() => {
    const handleCallback = async () => {
      const shopifyStatus = searchParams.get('shopify');
      const shop = searchParams.get('shop');
      const errorMessage = searchParams.get('message');

      if (shopifyStatus === 'connected' && shop) {
        toast({
          title: 'Store Connected',
          description: `Your Shopify store ${shop} has been connected successfully`,
        });

        // Force refresh the connection status after a delay
        setTimeout(async () => {
          await checkConnection();
          navigate('/settings', { replace: true });
        }, 1500);
      } else if (shopifyStatus === 'error') {
        toast({
          title: 'Connection Failed',
          description: errorMessage || 'Failed to connect your Shopify store',
          variant: 'destructive',
        });
        navigate('/settings', { replace: true });
      } else {
        // No valid callback parameters, redirect to settings
        navigate('/settings', { replace: true });
      }
    };

    // Wait for authentication to load
    if (!isLoading) {
      if (isAuthenticated) {
        handleCallback();
      } else {
        // User is not authenticated, redirect to login
        navigate('/auth/login', { replace: true });
      }
    }
  }, [isLoading, isAuthenticated, searchParams, navigate, toast, checkConnection]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
        <p className="text-gray-600">Processing Shopify connection...</p>
      </div>
    </div>
  );
};
