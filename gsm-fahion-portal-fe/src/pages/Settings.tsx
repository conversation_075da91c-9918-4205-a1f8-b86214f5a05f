
import { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ShopifyConnectionCard } from "@/components/shopify/ShopifyConnectionCard";
import { Wallet, CreditCard, UserCog, Plug, Building, Upload, Loader2, CheckCircle, AlertCircle, Clock } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { brandAPI, uploadAPI } from "@/lib/api";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface BrandProfile {
  brand_name?: string;
  website?: string;
  bio?: string;
  logo_url?: string;
  social_handles?: {
    instagram?: string;
    tiktok?: string;
    twitter?: string;
    facebook?: string;
  };
  business_name?: string;
  tax_id?: string;
  business_registration_doc_url?: string;
  profile_completed: boolean;
  user_id: {
    email: string;
    status: 'pending' | 'approved' | 'rejected';
    email_verified: boolean;
  };
}

export function Settings() {
  const { user, refreshUser } = useAuth();
  const { toast } = useToast();
  const [profile, setProfile] = useState<BrandProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setSaving] = useState(false);
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [uploadingDoc, setUploadingDoc] = useState(false);

  const [formData, setFormData] = useState({
    brand_name: '',
    website: '',
    bio: '',
    logo_url: '',
    social_handles: {
      instagram: '',
      tiktok: '',
      twitter: '',
      facebook: '',
    },
    business_name: '',
    tax_id: '',
    business_registration_doc_url: '',
  });

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      const response = await brandAPI.getProfile();
      const profileData = response.data;
      setProfile(profileData);

      // Populate form with existing data
      setFormData({
        brand_name: profileData.brand_name || '',
        website: profileData.website || '',
        bio: profileData.bio || '',
        logo_url: profileData.logo_url || '',
        social_handles: {
          instagram: profileData.social_handles?.instagram || '',
          tiktok: profileData.social_handles?.tiktok || '',
          twitter: profileData.social_handles?.twitter || '',
          facebook: profileData.social_handles?.facebook || '',
        },
        business_name: profileData.business_name || '',
        tax_id: profileData.tax_id || '',
        business_registration_doc_url: profileData.business_registration_doc_url || '',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to load profile data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      await brandAPI.updateProfile(formData);
      toast({
        title: 'Profile updated',
        description: 'Your brand profile has been saved successfully',
      });
      fetchProfile(); // Refresh profile data
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update profile',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploadingLogo(true);
    try {
      const response = await uploadAPI.uploadLogo(file);
      const logoUrl = response.data.url;

      setFormData(prev => ({ ...prev, logo_url: logoUrl }));
      toast({
        title: 'Logo uploaded',
        description: 'Your logo has been uploaded successfully',
      });
    } catch (error: any) {
      toast({
        title: 'Upload failed',
        description: error.response?.data?.message || 'Failed to upload logo',
        variant: 'destructive',
      });
    } finally {
      setUploadingLogo(false);
    }
  };

  const handleDocumentUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploadingDoc(true);
    try {
      const response = await uploadAPI.uploadDocument(file);
      const docUrl = response.data.url;

      setFormData(prev => ({ ...prev, business_registration_doc_url: docUrl }));
      toast({
        title: 'Document uploaded',
        description: 'Your business registration document has been uploaded successfully',
      });
    } catch (error: any) {
      toast({
        title: 'Upload failed',
        description: error.response?.data?.message || 'Failed to upload document',
        variant: 'destructive',
      });
    } finally {
      setUploadingDoc(false);
    }
  };

  const calculateProgress = () => {
    const fields = [
      formData.brand_name,
      formData.website,
      formData.bio,
      formData.business_name,
      formData.tax_id,
    ];
    const filledFields = fields.filter(field => field && field.trim() !== '').length;
    return Math.round((filledFields / fields.length) * 100);
  };

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex justify-center items-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="py-6 px-6 max-w-5xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-brand-dark-purple">Settings</h1>
            <p className="text-muted-foreground mt-1">Manage your account settings and preferences</p>
          </div>
        </div>

        {/* Application Status Alert */}
        {user && user.status !== 'approved' && (
          <div className="mb-6">
            {user.status === 'pending' && (
              <Alert className="border-yellow-200 bg-yellow-50">
                <Clock className="h-4 w-4" />
                <AlertTitle className="text-yellow-800">Application Under Review</AlertTitle>
                <AlertDescription className="text-yellow-700">
                  <div className="space-y-2">
                    <p>Your brand application is currently being reviewed by our team.</p>
                    {!profile?.profile_completed && (
                      <div className="p-3 bg-yellow-100 rounded-md border border-yellow-200">
                        <p className="font-medium text-yellow-800">⚠️ Action Required:</p>
                        <p className="text-sm text-yellow-700 mt-1">
                          Complete your brand profile below to speed up the review process.
                          Make sure to fill all required fields and upload necessary documents.
                        </p>
                      </div>
                    )}
                    {profile?.profile_completed && (
                      <div className="p-3 bg-green-100 rounded-md border border-green-200">
                        <p className="font-medium text-green-800">✅ Profile Complete</p>
                        <p className="text-sm text-green-700 mt-1">
                          Your profile is complete and under admin review. You'll receive an email notification once reviewed.
                        </p>
                      </div>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {user.status === 'rejected' && (
              <Alert className="border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle className="text-red-800">Application Needs Attention</AlertTitle>
                <AlertDescription className="text-red-700">
                  <div className="space-y-2">
                    <p>Your brand application requires additional information or corrections.</p>
                    <div className="p-3 bg-red-100 rounded-md border border-red-200">
                      <p className="font-medium text-red-800">🔄 Next Steps:</p>
                      <ul className="text-sm text-red-700 mt-1 space-y-1">
                        <li>• Review and update your profile information below</li>
                        <li>• Ensure all required fields are completed</li>
                        <li>• Upload any missing documents</li>
                        <li>• Save your changes to resubmit for review</li>
                      </ul>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Success Alert for Approved Users */}
        {user && user.status === 'approved' && (
          <div className="mb-6">
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4" />
              <AlertTitle className="text-green-800">Application Approved! 🎉</AlertTitle>
              <AlertDescription className="text-green-700">
                <div className="space-y-2">
                  <p>Congratulations! Your brand application has been approved.</p>
                  <div className="p-3 bg-green-100 rounded-md border border-green-200">
                    <p className="font-medium text-green-800">✨ You now have access to:</p>
                    <ul className="text-sm text-green-700 mt-1 space-y-1">
                      <li>• Full dashboard and analytics</li>
                      <li>• Campaign creation and management</li>
                      <li>• Product catalog management</li>
                      <li>• Marketplace access</li>
                    </ul>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          </div>
        )}

        <Tabs defaultValue="profile" className="bg-white rounded-lg shadow-sm p-6">
          <TabsList className="mb-6">
            <TabsTrigger value="profile">
              <Building className="h-4 w-4 mr-2" />
              Brand Profile
            </TabsTrigger>
            <TabsTrigger value="wallet">
              <Wallet className="h-4 w-4 mr-2" />
              Wallet
            </TabsTrigger>
            <TabsTrigger value="billing">
              <CreditCard className="h-4 w-4 mr-2" />
              Billing
            </TabsTrigger>
            <TabsTrigger value="integrations">
              <Plug className="h-4 w-4 mr-2" />
              Shopify Integration
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile">
            <div className="space-y-6">
              {/* Profile Completion Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    Profile Completion
                    <Badge variant={profile?.profile_completed ? "default" : "secondary"}>
                      {profile?.profile_completed ? (
                        <>
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Complete
                        </>
                      ) : (
                        <>
                          <AlertCircle className="h-4 w-4 mr-1" />
                          Incomplete
                        </>
                      )}
                    </Badge>
                  </CardTitle>
                  <CardDescription>
                    Complete your profile to unlock all features and speed up approval
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{calculateProgress()}%</span>
                    </div>
                    <Progress value={calculateProgress()} className="h-2" />
                  </div>
                </CardContent>
              </Card>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="brand-logo" className="block mb-2">Brand Logo</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 flex justify-center">
                    <div className="text-center">
                      <div className="h-24 w-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                        <svg
                          className="h-12 w-12 text-gray-400"
                          stroke="currentColor"
                          fill="none"
                          viewBox="0 0 48 48"
                          aria-hidden="true"
                        >
                          <path
                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                            strokeWidth={2}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      {formData.logo_url ? (
                        <div className="space-y-2">
                          <img
                            src={formData.logo_url}
                            alt="Brand Logo"
                            className="h-24 w-24 mx-auto rounded-full object-cover"
                          />
                          <p className="text-sm text-green-600">Logo uploaded</p>
                        </div>
                      ) : (
                        <div className="h-24 w-24 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                          <svg
                            className="h-12 w-12 text-gray-400"
                            stroke="currentColor"
                            fill="none"
                            viewBox="0 0 48 48"
                            aria-hidden="true"
                          >
                            <path
                              d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                      )}
                      <p className="mt-2 text-sm text-gray-600">Upload your brand logo</p>
                      <div className="mt-2">
                        <input
                          type="file"
                          id="logo-upload"
                          accept="image/*"
                          onChange={handleLogoUpload}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          onClick={() => document.getElementById('logo-upload')?.click()}
                          disabled={uploadingLogo}
                        >
                          {uploadingLogo ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Uploading...
                            </>
                          ) : (
                            <>
                              <Upload className="h-4 w-4 mr-2" />
                              {formData.logo_url ? 'Change Logo' : 'Upload Logo'}
                            </>
                          )}
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">PNG, JPG, GIF up to 5MB</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="brand-name">Brand Name *</Label>
                    <Input
                      id="brand-name"
                      value={formData.brand_name}
                      onChange={(e) => setFormData({...formData, brand_name: e.target.value})}
                      placeholder="Enter your brand name"
                    />
                  </div>

                  <div>
                    <Label htmlFor="brand-website">Website *</Label>
                    <Input
                      id="brand-website"
                      value={formData.website}
                      onChange={(e) => setFormData({...formData, website: e.target.value})}
                      placeholder="https://yourbrand.com"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="brand-bio">Bio *</Label>
                    <Textarea
                      id="brand-bio"
                      rows={5}
                      value={formData.bio}
                      onChange={(e) => setFormData({...formData, bio: e.target.value})}
                      placeholder="Tell us about your brand..."
                    />
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Social Media Handles</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="instagram">Instagram</Label>
                    <Input
                      id="instagram"
                      placeholder="@yourbrand"
                      value={formData.social_handles.instagram}
                      onChange={(e) => setFormData({
                        ...formData,
                        social_handles: {...formData.social_handles, instagram: e.target.value}
                      })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="tiktok">TikTok</Label>
                    <Input
                      id="tiktok"
                      placeholder="@yourbrand"
                      value={formData.social_handles.tiktok}
                      onChange={(e) => setFormData({
                        ...formData,
                        social_handles: {...formData.social_handles, tiktok: e.target.value}
                      })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="twitter">Twitter</Label>
                    <Input
                      id="twitter"
                      placeholder="@yourbrand"
                      value={formData.social_handles.twitter}
                      onChange={(e) => setFormData({
                        ...formData,
                        social_handles: {...formData.social_handles, twitter: e.target.value}
                      })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="facebook">Facebook</Label>
                    <Input
                      id="facebook"
                      placeholder="@yourbrand"
                      value={formData.social_handles.facebook}
                      onChange={(e) => setFormData({
                        ...formData,
                        social_handles: {...formData.social_handles, facebook: e.target.value}
                      })}
                    />
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Business Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="business-name">Business Name *</Label>
                    <Input
                      id="business-name"
                      placeholder="Your Business LLC"
                      value={formData.business_name}
                      onChange={(e) => setFormData({...formData, business_name: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="tax-id">Tax ID *</Label>
                    <Input
                      id="tax-id"
                      placeholder="*********"
                      value={formData.tax_id}
                      onChange={(e) => setFormData({...formData, tax_id: e.target.value})}
                    />
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Business Registration Document</h3>
                <Card className="p-4">
                  <p className="text-sm text-muted-foreground mb-4">
                    Upload your business registration document for verification purposes.
                  </p>
                  <div className="space-y-4">
                    {formData.business_registration_doc_url ? (
                      <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-md">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="text-sm text-green-700">Document uploaded successfully</span>
                        <Button
                          variant="link"
                          size="sm"
                          onClick={() => window.open(formData.business_registration_doc_url, '_blank')}
                        >
                          View
                        </Button>
                      </div>
                    ) : (
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <div className="space-y-2">
                          <p className="text-sm text-gray-600">Upload business registration document</p>
                          <p className="text-xs text-gray-500">PDF, DOC, DOCX up to 10MB</p>
                        </div>
                      </div>
                    )}
                    <div>
                      <input
                        type="file"
                        id="document-upload"
                        accept=".pdf,.doc,.docx,image/*"
                        onChange={handleDocumentUpload}
                        className="hidden"
                      />
                      <Button
                        variant="outline"
                        onClick={() => document.getElementById('document-upload')?.click()}
                        disabled={uploadingDoc}
                      >
                        {uploadingDoc ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Uploading...
                          </>
                        ) : (
                          <>
                            <Upload className="h-4 w-4 mr-2" />
                            {formData.business_registration_doc_url ? 'Replace Document' : 'Upload Document'}
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </Card>
              </div>

              <div className="flex justify-end space-x-3">
                <Button variant="outline" onClick={fetchProfile}>
                  Reset
                </Button>
                <Button
                  className="button-gradient text-white"
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Changes'
                  )}
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="wallet">
            <div className="space-y-6">
              <Card className="p-6 border-brand-purple/20">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">Connected Wallet</h3>
                  <Button variant="outline">Disconnect</Button>
                </div>
                
                <div className="bg-gray-50 p-4 rounded-md mb-4">
                  <p className="font-mono text-sm">******************************************</p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded-md flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                        <span className="text-blue-600 font-bold">E</span>
                      </div>
                      <div>
                        <p className="font-medium">ETH</p>
                        <p className="text-xs text-muted-foreground">Ethereum</p>
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">0.42 ETH</p>
                      <p className="text-xs text-muted-foreground">≈ $1,254.00</p>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 p-3 rounded-md flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                        <span className="text-blue-600 font-bold">U</span>
                      </div>
                      <div>
                        <p className="font-medium">USDC</p>
                        <p className="text-xs text-muted-foreground">USD Coin</p>
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">2,500 USDC</p>
                      <p className="text-xs text-muted-foreground">≈ $2,500.00</p>
                    </div>
                  </div>
                </div>
              </Card>

              <div>
                <h3 className="text-lg font-semibold mb-4">Payout Wallet</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  This is the wallet where rewards will be sent to users who complete your campaign missions.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="payout-wallet">Payout Wallet Address</Label>
                    <Input id="payout-wallet" placeholder="0x..." />
                  </div>
                  
                  <div>
                    <Label htmlFor="default-token">Default Token</Label>
                    <Select defaultValue="usdc">
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select token" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="usdc">USDC</SelectItem>
                        <SelectItem value="eth">ETH</SelectItem>
                        <SelectItem value="brand">BRAND Token</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="mt-4">
                  <Button variant="outline">Connect Different Wallet</Button>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Transaction History</h3>
                <div className="bg-gray-50 p-6 rounded-md text-center">
                  <p className="text-muted-foreground">No transactions yet</p>
                </div>
              </div>

              <div className="flex justify-end">
                <Button className="button-gradient text-white">Save Wallet Settings</Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="billing">
            <div className="space-y-6">
              <Card className="p-6 border-green-200 bg-green-50">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                  <div>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-2">
                      Current Plan
                    </span>
                    <h3 className="text-xl font-semibold">Pro Plan</h3>
                    <p className="text-sm text-muted-foreground">$49.99/month, billed monthly</p>
                  </div>
                  <div className="mt-4 md:mt-0">
                    <Button variant="outline">Change Plan</Button>
                  </div>
                </div>
                
                <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-white p-3 rounded-md">
                    <p className="text-sm text-muted-foreground">Campaigns</p>
                    <p className="font-semibold">10 of 15 used</p>
                  </div>
                  <div className="bg-white p-3 rounded-md">
                    <p className="text-sm text-muted-foreground">Rewards Pool</p>
                    <p className="font-semibold">$750 of $1,000</p>
                  </div>
                  <div className="bg-white p-3 rounded-md">
                    <p className="text-sm text-muted-foreground">Team Members</p>
                    <p className="font-semibold">3 of 5</p>
                  </div>
                </div>
                
                <p className="text-sm text-muted-foreground mt-4">
                  Next billing date: June 15, 2025
                </p>
              </Card>

              <div>
                <h3 className="text-lg font-semibold mb-4">Payment Methods</h3>
                <Card className="p-4 mb-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="h-10 w-10 bg-gray-100 rounded-md flex items-center justify-center mr-3">
                        <CreditCard className="h-5 w-5" />
                      </div>
                      <div>
                        <p className="font-medium">•••• •••• •••• 4242</p>
                        <p className="text-xs text-muted-foreground">Expires 12/25</p>
                      </div>
                    </div>
                    <div className="flex">
                      <Button variant="ghost" size="sm">Edit</Button>
                      <Button variant="ghost" size="sm">Remove</Button>
                    </div>
                  </div>
                </Card>
                
                <Button variant="outline" className="w-full">
                  <CreditCard className="mr-2 h-4 w-4" /> Add Payment Method
                </Button>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Billing History</h3>
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receipt</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">May 15, 2025</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">Pro Plan - Monthly</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">$49.99</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Paid
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <a href="#" className="text-brand-purple hover:underline">Download</a>
                      </td>
                    </tr>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">Apr 15, 2025</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">Pro Plan - Monthly</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">$49.99</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Paid
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <a href="#" className="text-brand-purple hover:underline">Download</a>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="integrations">
            <div className="space-y-6">
              <ShopifyConnectionCard />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-md bg-pink-100 flex items-center justify-center mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-pink-600">
                          <path d="M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z"></path>
                          <path d="M7 7h.01"></path>
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold">Instagram</h3>
                        <p className="text-xs text-muted-foreground">Connect your Instagram Business account</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">Connect</Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Allow users to verify Instagram follow and share missions automatically.
                  </p>
                </Card>
                
                <Card className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-md bg-black flex items-center justify-center mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                          <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold">TikTok</h3>
                        <p className="text-xs text-muted-foreground">Connect your TikTok Business account</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">Connect</Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Verify TikTok video submissions and follower counts.
                  </p>
                </Card>
                
                <Card className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-md bg-blue-100 flex items-center justify-center mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-600">
                          <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                        </svg>
                      </div>
                      <div>
                        <h3 className="font-semibold">Twitter</h3>
                        <p className="text-xs text-muted-foreground">Connect your Twitter Business account</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">Connect</Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Track Twitter engagements and verify follow missions.
                  </p>
                </Card>
                

              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">API Access</h3>
                <Card className="p-6">
                  <p className="text-sm text-muted-foreground mb-4">
                    Generate API keys to integrate with your existing systems.
                  </p>
                  <div className="bg-gray-50 p-4 rounded-md mb-4">
                    <div className="flex justify-between items-center">
                      <p className="font-mono text-sm">••••••••••••••••••••••••••••••</p>
                      <Button variant="outline" size="sm">Regenerate</Button>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">API Documentation</p>
                      <p className="text-xs text-muted-foreground">Learn how to use our API</p>
                    </div>
                    <Button variant="outline">View Docs</Button>
                  </div>
                </Card>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}

export default Settings;
