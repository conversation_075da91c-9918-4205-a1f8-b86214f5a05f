
import { useState } from "react";
import { ChevronLeft, Qr<PERSON>ode, Smartphone, Upload, Download, Plus, X } from "lucide-react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "@/components/ui/use-toast";

export function ProductCreate() {
  const [productType, setProductType] = useState("manual");
  const [images, setImages] = useState<string[]>([]);
  const [qrGenerated, setQrGenerated] = useState(false);
  const [nfcChips, setNfcChips] = useState<string[]>([]);
  const [newChipId, setNewChipId] = useState("");

  const handleImageUpload = () => {
    // Simulate image upload with placeholder image
    if (images.length < 4) {
      const newImage = "https://images.unsplash.com/photo-1581655353564-df123a1eb820?q=80&w=500";
      setImages([...images, newImage]);
      toast({
        title: "Image uploaded",
        description: "Image has been added to the product.",
      });
    } else {
      toast({
        title: "Upload limit reached",
        description: "You can upload maximum 4 images per product.",
        variant: "destructive"
      });
    }
  };

  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    setImages(newImages);
  };

  const generateQrCode = () => {
    setQrGenerated(true);
    toast({
      title: "QR Code generated",
      description: "Your unique product QR code is ready to download.",
    });
  };

  const addNfcChip = () => {
    if (newChipId.trim() !== "") {
      setNfcChips([...nfcChips, newChipId.trim()]);
      setNewChipId("");
      toast({
        title: "NFC chip added",
        description: "Chip ID has been linked to this product."
      });
    }
  };

  const removeNfcChip = (index: number) => {
    const newChips = [...nfcChips];
    newChips.splice(index, 1);
    setNfcChips(newChips);
  };

  const handleCsvUpload = () => {
    // Simulate CSV upload
    const mockChips = ["NFC-001", "NFC-002", "NFC-003"];
    setNfcChips([...nfcChips, ...mockChips]);
    toast({
      title: "CSV imported",
      description: `${mockChips.length} chip IDs have been added.`
    });
  };

  const handleSaveProduct = () => {
    toast({
      title: "Product saved",
      description: "Your product has been saved successfully."
    });
  };

  return (
    <AppLayout>
      <div className="py-6 px-6 max-w-4xl mx-auto">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <a href="/products">
              <ChevronLeft className="h-5 w-5" />
            </a>
          </Button>
          <h1 className="text-3xl font-bold text-brand-dark-purple">Add Product</h1>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <Tabs defaultValue="manual" onValueChange={(value) => setProductType(value)}>
            <TabsList className="w-full grid grid-cols-3 mb-6">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="manual">Manual Product</TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Create a standard product without verification methods</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="qr">QR-Based</TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Generate unique QR codes for campaign verification</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="nfc">NFC-Enabled</TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Link NFC chips to products for tap verification</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </TabsList>
            
            <TabsContent value="manual">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name">Product Name</Label>
                      <Input id="name" placeholder="Eco-Friendly T-Shirt" />
                    </div>
                    
                    <div>
                      <Label htmlFor="sku">SKU</Label>
                      <Input id="sku" placeholder="ECO-TS-001" />
                    </div>
                    
                    <div>
                      <Label htmlFor="brand">Brand Name</Label>
                      <Input id="brand" placeholder="EcoFashion" />
                    </div>
                    
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Textarea 
                        id="description" 
                        placeholder="Describe your product"
                        rows={4}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="price">Price (Optional)</Label>
                      <Input id="price" type="number" placeholder="0.00" />
                    </div>
                  </div>
                  
                  <div>
                    <Label className="mb-2 block">Product Images</Label>
                    <div className="grid grid-cols-2 gap-4">
                      {images.map((image, i) => (
                        <div 
                          key={i} 
                          className="aspect-square border-2 border-gray-300 rounded-md relative overflow-hidden"
                        >
                          <img 
                            src={image} 
                            alt={`Product ${i + 1}`} 
                            className="object-cover w-full h-full"
                          />
                          <Button 
                            variant="destructive" 
                            size="icon" 
                            className="absolute top-1 right-1 h-6 w-6"
                            onClick={() => removeImage(i)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                      {images.length < 4 && Array(4 - images.length).fill(0).map((_, i) => (
                        <div 
                          key={`empty-${i}`} 
                          className="aspect-square border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center"
                          onClick={handleImageUpload}
                        >
                          <Button variant="outline" size="sm">
                            <Upload className="h-4 w-4 mr-2" /> Upload
                          </Button>
                        </div>
                      ))}
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">
                      Upload up to 4 high-quality product images. Recommended: 1000x1000px.
                    </p>
                  </div>
                </div>
                
                <div>
                  <Label>Categories</Label>
                  <div className="grid grid-cols-3 gap-2 mt-2">
                    <Button variant="outline" className="justify-start">T-Shirts</Button>
                    <Button variant="outline" className="justify-start">Sustainable</Button>
                    <Button variant="outline" className="justify-start">Summer</Button>
                    <Button variant="outline" className="justify-start border-dashed">+ Add Category</Button>
                  </div>
                </div>
                
                <div className="flex items-center mt-4">
                  <input id="marketplace" type="checkbox" className="h-4 w-4 mr-2" />
                  <Label htmlFor="marketplace">List in Marketplace</Label>
                </div>
                
                <div className="flex justify-end">
                  <Button className="button-gradient text-white" onClick={handleSaveProduct}>Save Product</Button>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="qr">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="qr-name">Product Name</Label>
                      <Input id="qr-name" placeholder="Eco-Friendly T-Shirt" />
                    </div>
                    
                    <div>
                      <Label htmlFor="qr-sku">SKU</Label>
                      <Input id="qr-sku" placeholder="ECO-TS-001" />
                    </div>
                    
                    <div>
                      <Label htmlFor="qr-brand">Brand Name</Label>
                      <Input id="qr-brand" placeholder="EcoFashion" />
                    </div>
                    
                    <div>
                      <Label htmlFor="qr-description">Description</Label>
                      <Textarea 
                        id="qr-description" 
                        placeholder="Describe your product"
                        rows={4}
                      />
                    </div>

                    <div>
                      <Label className="mb-2 block">Product Images</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {images.map((image, i) => (
                          <div 
                            key={i} 
                            className="aspect-square border-2 border-gray-300 rounded-md relative overflow-hidden"
                          >
                            <img 
                              src={image} 
                              alt={`Product ${i + 1}`} 
                              className="object-cover w-full h-full"
                            />
                            <Button 
                              variant="destructive" 
                              size="icon" 
                              className="absolute top-1 right-1 h-6 w-6"
                              onClick={() => removeImage(i)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                        {images.length < 4 && (
                          <div 
                            className="aspect-square border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center"
                            onClick={handleImageUpload}
                          >
                            <Button variant="outline" size="sm">
                              <Upload className="h-4 w-4 mr-2" /> Upload
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <Label className="mb-2 block">QR Code Setup</Label>
                    <Card className="p-6 flex flex-col items-center">
                      {qrGenerated ? (
                        <>
                          <div className="h-48 w-48 bg-white p-3 rounded-md mb-4 flex items-center justify-center">
                            <img 
                              src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=EcoFriendlyTShirt_UniqueID_123456" 
                              alt="QR Code" 
                              className="h-full w-full"
                            />
                          </div>
                          <div className="flex space-x-2">
                            <Button variant="outline">
                              <Download className="h-4 w-4 mr-2" /> Download QR
                            </Button>
                            <Button variant="outline">
                              Print QR Code
                            </Button>
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="h-32 w-32 bg-gray-200 rounded-md mb-4 flex items-center justify-center">
                            <QrCode className="h-12 w-12 text-gray-400" />
                          </div>
                          <p className="text-sm text-center text-muted-foreground">
                            A unique QR code will be generated for your product.
                            Users can scan this code to verify their content submissions.
                          </p>
                          <Button variant="outline" className="mt-4" onClick={generateQrCode}>
                            Generate QR Code
                          </Button>
                        </>
                      )}
                    </Card>

                    <div>
                      <Label>QR Code Options</Label>
                      <div className="space-y-2 mt-2">
                        <div className="flex items-center">
                          <input id="print-ready" type="checkbox" className="h-4 w-4 mr-2" />
                          <Label htmlFor="print-ready">Generate print-ready QR code</Label>
                        </div>
                        <div className="flex items-center">
                          <input id="track-scans" type="checkbox" className="h-4 w-4 mr-2" />
                          <Label htmlFor="track-scans">Track QR code scans</Label>
                        </div>
                        <div className="flex items-center">
                          <input id="unique-user" type="checkbox" className="h-4 w-4 mr-2" />
                          <Label htmlFor="unique-user">One scan per user (prevent duplicates)</Label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center mt-4">
                  <input id="qr-marketplace" type="checkbox" className="h-4 w-4 mr-2" />
                  <Label htmlFor="qr-marketplace">List in Marketplace</Label>
                </div>

                <div className="flex items-center mt-4">
                  <input id="attach-campaign" type="checkbox" className="h-4 w-4 mr-2" />
                  <Label htmlFor="attach-campaign">Attach to an existing campaign</Label>
                </div>
                
                <div className="flex justify-end">
                  <Button className="button-gradient text-white" onClick={handleSaveProduct}>Save QR Product</Button>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="nfc">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="nfc-name">Product Name</Label>
                      <Input id="nfc-name" placeholder="Eco-Friendly T-Shirt" />
                    </div>
                    
                    <div>
                      <Label htmlFor="nfc-sku">SKU</Label>
                      <Input id="nfc-sku" placeholder="ECO-TS-001" />
                    </div>
                    
                    <div>
                      <Label htmlFor="nfc-brand">Brand Name</Label>
                      <Input id="nfc-brand" placeholder="EcoFashion" />
                    </div>
                    
                    <div>
                      <Label htmlFor="nfc-description">Description</Label>
                      <Textarea 
                        id="nfc-description" 
                        placeholder="Describe your product"
                        rows={4}
                      />
                    </div>

                    <div>
                      <Label className="mb-2 block">Product Images</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {images.map((image, i) => (
                          <div 
                            key={i} 
                            className="aspect-square border-2 border-gray-300 rounded-md relative overflow-hidden"
                          >
                            <img 
                              src={image} 
                              alt={`Product ${i + 1}`} 
                              className="object-cover w-full h-full"
                            />
                            <Button 
                              variant="destructive" 
                              size="icon" 
                              className="absolute top-1 right-1 h-6 w-6"
                              onClick={() => removeImage(i)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                        {images.length < 4 && (
                          <div 
                            className="aspect-square border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center"
                            onClick={handleImageUpload}
                          >
                            <Button variant="outline" size="sm">
                              <Upload className="h-4 w-4 mr-2" /> Upload
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <Label className="mb-2 block">NFC Chip Setup</Label>
                    <Card className="p-6 flex flex-col items-center mb-4">
                      <div className="h-32 w-32 rounded-md mb-4 flex items-center justify-center">
                        <Smartphone className="h-12 w-12 text-gray-400" />
                      </div>
                      <p className="text-sm text-center text-muted-foreground">
                        Link NFC chips to your products. Users can tap their phone on the 
                        product to verify their content submissions.
                      </p>
                    </Card>
                    
                    <div>
                      <Label htmlFor="chip-id">NFC Chip ID</Label>
                      <div className="flex mt-1">
                        <Input 
                          id="chip-id" 
                          placeholder="Enter chip ID or serial number" 
                          value={newChipId}
                          onChange={(e) => setNewChipId(e.target.value)}
                          className="rounded-r-none"
                        />
                        <Button 
                          onClick={addNfcChip}
                          className="rounded-l-none"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {nfcChips.length > 0 && (
                      <div>
                        <Label>Linked NFC Chips ({nfcChips.length})</Label>
                        <div className="mt-2 space-y-2">
                          {nfcChips.map((chip, index) => (
                            <div key={index} className="flex items-center justify-between rounded-md border px-3 py-2">
                              <span>{chip}</span>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => removeNfcChip(index)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <div>
                      <Label>Or Import Multiple</Label>
                      <div className="mt-2 flex flex-col space-y-2">
                        <Button variant="outline" onClick={handleCsvUpload}>
                          <Upload className="h-4 w-4 mr-2" /> Import CSV
                        </Button>
                        <Input type="file" className="hidden" id="csv-upload" />
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        CSV file should contain one chip ID per line.
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="batch-id">GSM Chip Batch ID (Optional)</Label>
                      <Input id="batch-id" placeholder="Batch identification number" />
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center mt-4">
                  <input id="nfc-marketplace" type="checkbox" className="h-4 w-4 mr-2" />
                  <Label htmlFor="nfc-marketplace">List in Marketplace</Label>
                </div>

                <div className="flex items-center mt-4">
                  <input id="nfc-campaign" type="checkbox" className="h-4 w-4 mr-2" />
                  <Label htmlFor="nfc-campaign">Attach to an existing campaign</Label>
                </div>
                
                <div className="flex justify-end">
                  <Button className="button-gradient text-white" onClick={handleSaveProduct}>Save NFC Product</Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </AppLayout>
  );
}

export default ProductCreate;
