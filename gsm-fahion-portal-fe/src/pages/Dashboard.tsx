
import { useState, useEffect } from "react";
import { Package, Calendar, Wallet, Share, Download, User, CheckCircle, Clock, QrCode, Truck, AlertCircle, Settings } from "lucide-react";
import { AppLayout } from "@/components/layout/AppLayout";
import { EmptyState } from "@/components/dashboard/EmptyState";
import { StatCard } from "@/components/dashboard/StatCard";
import { CampaignCard } from "@/components/dashboard/CampaignCard";
import { ProductCard } from "@/components/dashboard/ProductCard";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
// ShopifyContext temporarily removed
import { brandAPI } from "@/lib/api";
import { Link } from "react-router-dom";

interface BrandProfile {
  brand_name?: string;
  website?: string;
  bio?: string;
  logo_url?: string;
  social_handles?: {
    instagram?: string;
    tiktok?: string;
    twitter?: string;
    facebook?: string;
  };
  business_name?: string;
  tax_id?: string;
  business_registration_doc_url?: string;
  profile_completed: boolean;
  user_id: {
    email: string;
    status: 'pending' | 'approved' | 'rejected';
    email_verified: boolean;
  };
}

export function Dashboard() {
  const { toast } = useToast();
  const { user } = useAuth();
  // Shopify temporarily removed
  const connection = { isConnected: false };
  const [hasContent, setHasContent] = useState(false);
  const [showTips, setShowTips] = useState(true);
  const [currentTip, setCurrentTip] = useState(0);
  const [profile, setProfile] = useState<BrandProfile | null>(null);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);
  
  // Demo tips for first-time users
  const tips = [
    {
      title: "Getting Started",
      description: "Import your products, create campaigns, and start collecting UGC content with crypto rewards.",
      icon: Package
    },
    {
      title: "Verify Products with NFC",
      description: "Order NFC chips to verify authentic product ownership when users submit content.",
      icon: QrCode
    },
    {
      title: "Reward Distribution",
      description: "Automatically distribute crypto rewards when submissions are approved.",
      icon: Wallet
    }
  ];

  // This is a demo function to toggle between empty and populated states
  const createDemoContent = () => {
    setHasContent(true);
    toast({
      title: "Demo content created",
      description: "Showing example data for demonstration purposes.",
    });
  };

  // Fetch brand profile on component mount
  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      const response = await brandAPI.getProfile();
      setProfile(response.data);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to load profile data',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingProfile(false);
    }
  };

  // Auto rotate tips every 5 seconds
  useEffect(() => {
    if (!showTips) return;
    
    const interval = setInterval(() => {
      setCurrentTip((prev) => (prev + 1) % tips.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [showTips, tips.length]);

  return (
    <AppLayout>
      <div className="dashboard-container py-6 px-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-brand-dark-purple">Dashboard</h1>
            <p className="text-muted-foreground mt-1">Manage your campaigns and track performance</p>
          </div>
          <div className="mt-4 sm:mt-0 flex flex-row space-x-3">
            {hasContent && (
              <>
                <Button variant="outline" asChild>
                  <Link to="/products/create">Create Product</Link>
                </Button>
                <Button className="button-gradient text-white" asChild>
                  <Link to="/campaigns/create">Create Campaign</Link>
                </Button>
              </>
            )}
            {/* Toggle for demo purposes */}
            {!hasContent && (
              <Button className="button-gradient text-white" onClick={createDemoContent}>
                Demo: Show Content
              </Button>
            )}
          </div>
        </div>

        {/* Status Alert for Brand Approval */}
        {user && user.status !== 'approved' && (
          <div className="mb-6">
            {user.status === 'pending' && (
              <Alert className="border-yellow-200 bg-yellow-50">
                <Clock className="h-4 w-4" />
                <AlertTitle className="text-yellow-800">Application Under Review</AlertTitle>
                <AlertDescription className="text-yellow-700">
                  Your brand application is currently being reviewed by our team.
                  {!profile?.profile_completed && (
                    <span className="block mt-2">
                      <strong>Action Required:</strong> Complete your brand profile to speed up the review process.
                      <Button asChild variant="link" className="p-0 h-auto text-yellow-800 underline ml-1">
                        <Link to="/settings">Complete Profile →</Link>
                      </Button>
                    </span>
                  )}
                </AlertDescription>
              </Alert>
            )}

            {user.status === 'rejected' && (
              <Alert className="border-red-200 bg-red-50">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle className="text-red-800">Application Needs Attention</AlertTitle>
                <AlertDescription className="text-red-700">
                  Your brand application requires additional information. Please update your profile and resubmit.
                  <Button asChild variant="link" className="p-0 h-auto text-red-800 underline ml-1">
                    <Link to="/settings">Update Profile →</Link>
                  </Button>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {/* Profile Completion Status */}
        {user && user.status === 'approved' && profile && !profile.profile_completed && (
          <div className="mb-6">
            <Alert className="border-blue-200 bg-blue-50">
              <Settings className="h-4 w-4" />
              <AlertTitle className="text-blue-800">Complete Your Profile</AlertTitle>
              <AlertDescription className="text-blue-700">
                Finish setting up your brand profile to unlock all features.
                <Button asChild variant="link" className="p-0 h-auto text-blue-800 underline ml-1">
                  <Link to="/settings">Complete Profile →</Link>
                </Button>
              </AlertDescription>
            </Alert>
          </div>
        )}

        {!hasContent ? (
          <>
            {/* First-time user view */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <Card className="p-6 hover:shadow-md transition-all">
                <div className="flex flex-col h-full">
                  <div className="h-12 w-12 rounded-full bg-brand-purple/10 flex items-center justify-center mb-4">
                    <Package className="h-6 w-6 text-brand-purple" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    {connection.connected ? 'Manage Your Products' : 'Import Your Products'}
                  </h3>
                  <p className="text-muted-foreground mb-4 flex-grow">
                    {connection.connected
                      ? `${connection.totalProducts || 0} products synced from ${connection.shopName || 'Shopify'}`
                      : 'Connect to Shopify or import a CSV with your product catalog to get started.'
                    }
                  </p>
                  <Button asChild>
                    <Link to={connection.connected ? "/products" : "/products/import"}>
                      {connection.connected ? 'View Products' : 'Import Products'}
                    </Link>
                  </Button>
                </div>
              </Card>
              
              <Card className="p-6 hover:shadow-md transition-all">
                <div className="flex flex-col h-full">
                  <div className="h-12 w-12 rounded-full bg-brand-purple/10 flex items-center justify-center mb-4">
                    <Calendar className="h-6 w-6 text-brand-purple" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Create Your First Campaign</h3>
                  <p className="text-muted-foreground mb-4 flex-grow">
                    Set up UGC campaigns with custom missions and reward structures.
                  </p>
                  <Button asChild>
                    <Link to="/campaigns/create">Create Campaign</Link>
                  </Button>
                </div>
              </Card>
              
              <Card className="p-6 hover:shadow-md transition-all">
                <div className="flex flex-col h-full">
                  <div className="h-12 w-12 rounded-full bg-brand-purple/10 flex items-center justify-center mb-4">
                    <QrCode className="h-6 w-6 text-brand-purple" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Order NFC Chips</h3>
                  <p className="text-muted-foreground mb-4 flex-grow">
                    Add NFC authentication to your products for secure verification.
                  </p>
                  <Button asChild>
                    <Link to="/nfc-requests">Order NFC</Link>
                  </Button>
                </div>
              </Card>
            </div>

            {/* Tips carousel */}
            <Card className="p-6 mb-8 bg-gradient-to-r from-brand-purple/20 to-indigo-400/20">
              <div className="flex items-start gap-4">
                <div className="h-12 w-12 rounded-full bg-brand-purple/30 flex-shrink-0 flex items-center justify-center">
                  {tips[currentTip].icon && (() => {
                    const IconComponent = tips[currentTip].icon;
                    return <IconComponent className="h-6 w-6 text-brand-purple" />;
                  })()}
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-1">{tips[currentTip].title}</h3>
                  <p className="text-muted-foreground">{tips[currentTip].description}</p>
                </div>
              </div>
              <div className="flex justify-center mt-4 gap-2">
                {tips.map((_, index) => (
                  <button
                    key={index}
                    className={`h-2 rounded-full ${
                      currentTip === index ? "w-6 bg-brand-purple" : "w-2 bg-brand-purple/30"
                    } transition-all`}
                    onClick={() => setCurrentTip(index)}
                  />
                ))}
              </div>
            </Card>
          </>
        ) : (
          <>
            {/* Quick Actions Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              <Card className="bg-brand-purple p-4 text-white hover:bg-brand-purple/90 transition-colors cursor-pointer">
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-full bg-white/10 flex items-center justify-center">
                    <Clock className="h-6 w-6" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium">Pending Submissions</h3>
                    <p className="text-white/80">18 waiting for review</p>
                  </div>
                </div>
                <Button variant="outline" className="w-full bg-white/10 border-white/20 hover:bg-white/20 text-white" asChild>
                  <Link to="/campaigns/1">Review Now</Link>
                </Button>
              </Card>

              <Card className="bg-gradient-to-r from-indigo-500 to-purple-600 p-4 text-white hover:opacity-90 transition-opacity cursor-pointer">
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-full bg-white/10 flex items-center justify-center">
                    <Truck className="h-6 w-6" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium">NFC Orders</h3>
                    <p className="text-white/80">2 orders in transit</p>
                  </div>
                </div>
                <Button variant="outline" className="w-full bg-white/10 border-white/20 hover:bg-white/20 text-white" asChild>
                  <Link to="/nfc-requests">Track Orders</Link>
                </Button>
              </Card>

              <Card className="bg-gradient-to-r from-cyan-500 to-blue-500 p-4 text-white hover:opacity-90 transition-opacity cursor-pointer">
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-full bg-white/10 flex items-center justify-center">
                    <Wallet className="h-6 w-6" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium">Rewards Distributed</h3>
                    <p className="text-white/80">2,458 USDC</p>
                  </div>
                </div>
                <Button variant="outline" className="w-full bg-white/10 border-white/20 hover:bg-white/20 text-white" asChild>
                  <Link to="/rewards">View Rewards</Link>
                </Button>
              </Card>
            </div>

            {/* Stats Overview */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <StatCard
                title="Total Products"
                value="48"
                icon={Package}
                trend="up"
                trendValue="12% from last month"
              />
              <StatCard
                title="Active Campaigns"
                value="3"
                icon={Calendar}
                description="Currently running"
              />
              <StatCard
                title="Total Submissions"
                value="124"
                icon={User}
                trend="up"
                trendValue="8% from last month"
              />
              <StatCard
                title="Social Shares"
                value="892"
                icon={Share}
                trend="up"
                trendValue="24% from last month"
              />
            </div>

            {/* Tabs for Campaigns and Products */}
            <Tabs defaultValue="campaigns" className="mb-8">
              <TabsList className="mb-6">
                <TabsTrigger value="campaigns">Active Campaigns</TabsTrigger>
                <TabsTrigger value="products">Recent Products</TabsTrigger>
              </TabsList>
              <TabsContent value="campaigns">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <CampaignCard
                    title="Summer Collection UGC"
                    description="Users submit photos wearing items from the summer collection"
                    status="active"
                    submissions={45}
                    target={100}
                    daysLeft={7}
                  />
                  <CampaignCard
                    title="Exclusive Sneaker Drop"
                    description="Limited edition sneakers unboxing and styling videos"
                    status="scheduled"
                    submissions={0}
                    target={50}
                    startDate="Jun 15"
                  />
                  <CampaignCard
                    title="Sustainable Fashion Challenge"
                    description="Show how you style our eco-friendly line"
                    status="draft"
                    submissions={0}
                    target={75}
                  />
                </div>
                <div className="mt-6 flex justify-center">
                  <Button variant="outline" asChild>
                    <Link to="/campaigns">View All Campaigns</Link>
                  </Button>
                </div>
              </TabsContent>
              <TabsContent value="products">
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  <ProductCard
                    name="Eco Friendly T-Shirt"
                    image="https://images.unsplash.com/photo-1581655353564-df123a1eb820?q=80&w=500"
                    sku="ECO-TS-001"
                    status="live"
                    campaigns={2}
                  />
                  <ProductCard
                    name="Designer Sneakers"
                    image="https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?q=80&w=500"
                    sku="SHO-SN-452"
                    status="live"
                    campaigns={1}
                  />
                  <ProductCard
                    name="Summer Dress"
                    image="https://images.unsplash.com/photo-1612336307429-8a898d10e223?q=80&w=500"
                    sku="FAS-DR-118"
                    status="pending"
                    campaigns={0}
                  />
                  <ProductCard
                    name="Luxury Handbag"
                    image="https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=500"
                    sku="ACC-BG-287"
                    status="live"
                    campaigns={3}
                  />
                </div>
                <div className="mt-6 flex justify-center">
                  <Button variant="outline" asChild>
                    <Link to="/products">View All Products</Link>
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </AppLayout>
  );
}

export default Dashboard;
