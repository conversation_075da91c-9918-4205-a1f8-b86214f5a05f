
import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { AppLayout } from "@/components/layout/AppLayout";
import { campaignsAPI } from "@/lib/api";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  ChevronLeft,
  Calendar,
  User,
  Video,
  Share2,
  Flag,
  Edit,
  Pause,
  Check,
  X,
  Download,
  Filter,
  Loader2
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";

type SubmissionStatus = 'pending' | 'approved' | 'rejected' | 'flagged';

interface Submission {
  id: string;
  user: string;
  mediaType: 'video' | 'image';
  mediaUrl: string;
  date: string;
  status: SubmissionStatus;
  location?: string;
  verificationMethod?: 'qr' | 'nfc' | 'manual';
  socialTasks: {
    instagram?: boolean;
    tiktok?: boolean;
    twitter?: boolean;
  };
}

interface Campaign {
  _id: string;
  title: string; // This will be mapped from 'name' in the API response
  name?: string; // Keep this for API compatibility
  description: string;
  banner_url?: string;
  status: string;
  selected_products: any[];
  brand_id: string;
  created_at: string;
  updated_at: string;
  missions?: any[];
  reward_settings?: {
    type: string;
    amount: number;
    token?: string;
    daily_limit?: number;
    user_limit?: number;
    approval_required?: boolean;
  };
  submissions_count?: number;
  target_submissions?: number;
  expires_at?: string;
  admin_notes?: string;
  approved_at?: string;
  rejected_at?: string;
}

export function CampaignDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { isAuthenticated, user, token } = useAuth();
  const [campaign, setCampaign] = useState<Campaign | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showEntryDetails, setShowEntryDetails] = useState<string | null>(null);
  const [filterVerified, setFilterVerified] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");

  const [submissions, setSubmissions] = useState<Submission[]>([
    {
      id: "1",
      user: "fashionista23",
      mediaType: "video",
      mediaUrl: "https://images.unsplash.com/photo-1581655353564-df123a1eb820?q=80&w=500",
      date: "May 12, 2025",
      status: "pending",
      location: "New York, NY",
      verificationMethod: "qr",
      socialTasks: {
        instagram: true,
        tiktok: false
      }
    },
    {
      id: "2",
      user: "stylemaster",
      mediaType: "image",
      mediaUrl: "https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?q=80&w=500",
      date: "May 11, 2025",
      status: "approved",
      verificationMethod: "nfc",
      socialTasks: {
        instagram: true,
        tiktok: true
      }
    },
    {
      id: "3",
      user: "trendsetter99",
      mediaType: "video",
      mediaUrl: "https://images.unsplash.com/photo-1612336307429-8a898d10e223?q=80&w=500",
      date: "May 10, 2025",
      status: "rejected",
      location: "Los Angeles, CA",
      verificationMethod: "manual",
      socialTasks: {
        instagram: false,
        tiktok: true
      }
    },
    {
      id: "4",
      user: "fashionforward",
      mediaType: "image",
      mediaUrl: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=500",
      date: "May 10, 2025",
      status: "flagged",
      location: "Miami, FL",
      verificationMethod: "qr",
      socialTasks: {
        instagram: true,
        tiktok: false
      }
    },
    {
      id: "5",
      user: "styleicon",
      mediaType: "image",
      mediaUrl: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?q=80&w=500",
      date: "May 9, 2025",
      status: "pending",
      verificationMethod: "nfc",
      socialTasks: {
        instagram: true,
        tiktok: true
      }
    },
    {
      id: "6",
      user: "modelbehavior",
      mediaType: "video",
      mediaUrl: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?q=80&w=500",
      date: "May 8, 2025",
      status: "approved",
      location: "Chicago, IL",
      verificationMethod: "qr",
      socialTasks: {
        instagram: true,
        tiktok: true
      }
    }
  ]);

  // Fetch campaign data
  useEffect(() => {
    const fetchCampaign = async () => {
      if (!id) {
        setError("Campaign ID not provided");
        setLoading(false);
        return;
      }

      try {
        console.log('Fetching campaign with ID:', id);
        console.log('Auth state:', { isAuthenticated, user: user?.email, token: !!token });
        console.log('Brand token in localStorage:', !!localStorage.getItem('brand_token'));

        const response = await campaignsAPI.getCampaign(id);
        console.log('Campaign API response:', response.data);

        if (response.data.success) {
          // Transform the API response to match the expected interface
          const apiCampaign = response.data.campaign;
          const transformedCampaign = {
            ...apiCampaign,
            title: apiCampaign.name, // Map 'name' to 'title'
            _id: apiCampaign._id,
            description: apiCampaign.description,
            banner_url: apiCampaign.banner_url,
            status: apiCampaign.status,
            selected_products: apiCampaign.selected_products || [],
            brand_id: apiCampaign.brand_id,
            created_at: apiCampaign.createdAt,
            updated_at: apiCampaign.updatedAt
          };
          console.log('Transformed campaign:', transformedCampaign);
          setCampaign(transformedCampaign);
        } else {
          throw new Error(response.data.error || 'Failed to fetch campaign');
        }
      } catch (err: any) {
        console.error('Error fetching campaign:', err);
        console.error('Error response:', err.response);

        // Check if it's an authentication error
        if (err.response?.status === 401) {
          setError('You need to be logged in to view this campaign. Please log in and try again.');
        } else {
          setError(err.response?.data?.error || err.message || 'Failed to fetch campaign');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchCampaign();
  }, [id]);

  const handleApprove = (id: string) => {
    setSubmissions(submissions.map(sub => 
      sub.id === id ? {...sub, status: 'approved'} : sub
    ));
    toast({
      title: "Submission approved",
      description: "The user will receive their reward shortly.",
    });
  };

  const handleReject = (id: string) => {
    setSubmissions(submissions.map(sub => 
      sub.id === id ? {...sub, status: 'rejected'} : sub
    ));
    toast({
      title: "Submission rejected",
      description: "The user has been notified.",
    });
  };

  const handleFlag = (id: string) => {
    setSubmissions(submissions.map(sub => 
      sub.id === id ? {...sub, status: 'flagged'} : sub
    ));
    toast({
      title: "Submission flagged for review",
      description: "This submission has been marked for additional review.",
    });
  };

  const filteredSubmissions = submissions.filter(sub => {
    if (filterVerified !== "all" && sub.verificationMethod !== filterVerified) return false;
    if (filterStatus !== "all" && sub.status !== filterStatus) return false;
    return true;
  });

  const getStatusBadge = (status: SubmissionStatus) => {
    switch(status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">✅ Approved</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800">❌ Rejected</Badge>;
      case 'flagged':
        return <Badge className="bg-orange-100 text-orange-800">⚠️ Flagged</Badge>;
      default:
        return <Badge className="bg-blue-100 text-blue-800">Pending</Badge>;
    }
  };

  const getVerificationBadge = (method?: 'qr' | 'nfc' | 'manual') => {
    switch(method) {
      case 'qr':
        return <Badge className="bg-purple-100 text-purple-800">QR Verified</Badge>;
      case 'nfc':
        return <Badge className="bg-indigo-100 text-indigo-800">NFC Verified</Badge>;
      case 'manual':
        return <Badge className="bg-gray-100 text-gray-800">Manual</Badge>;
      default:
        return null;
    }
  };

  // Loading state
  if (loading) {
    return (
      <AppLayout>
        <div className="py-6 px-6 max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading campaign details...</p>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <AppLayout>
        <div className="py-6 px-6 max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <X className="h-8 w-8 text-red-500 mx-auto mb-4" />
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={() => navigate('/campaigns')}>
                Back to Campaigns
              </Button>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  // No campaign found
  if (!campaign) {
    return (
      <AppLayout>
        <div className="py-6 px-6 max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Flag className="h-8 w-8 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">Campaign not found</p>
              <Button onClick={() => navigate('/campaigns')}>
                Back to Campaigns
              </Button>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="py-6 px-6 max-w-7xl mx-auto">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={() => navigate('/campaigns')} className="mr-2">
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-3xl font-bold text-brand-dark-purple">{campaign.title}</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <Card className="col-span-2 p-6 bg-white">
            <div className="flex justify-between items-center mb-4">
              <div className="flex space-x-2 items-center">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  campaign.status === 'active' ? 'bg-green-100 text-green-800' :
                  campaign.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                </span>
                <span className="text-sm text-muted-foreground">
                  {campaign.expires_at && new Date(campaign.expires_at) > new Date() ?
                    `${Math.ceil((new Date(campaign.expires_at).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days left` :
                    campaign.expires_at ? 'Campaign ended' : 'No end date'
                  }
                </span>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <Edit className="h-4 w-4 mr-2" /> Edit
                </Button>
                <Button variant="outline" size="sm">
                  <Pause className="h-4 w-4 mr-2" /> Pause
                </Button>
              </div>
            </div>

            <p className="text-muted-foreground mb-6">
              {campaign.description}
            </p>

            {campaign.banner_url && (
              <div className="mb-6">
                <img
                  src={campaign.banner_url}
                  alt={campaign.title}
                  className="w-full h-48 object-cover rounded-lg"
                />
              </div>
            )}

            <div className="mb-6">
              <h3 className="text-sm font-medium text-muted-foreground mb-2">Campaign Progress</h3>
              <Progress value={campaign.target_submissions ? Math.min((campaign.submissions_count || 0) / campaign.target_submissions * 100, 100) : 0} className="h-2" />
              <div className="flex justify-between mt-2 text-sm">
                <span>{campaign.submissions_count || 0} submissions</span>
                <span>Target: {campaign.target_submissions || 'No limit'}</span>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="p-4 bg-brand-purple/5">
                <Calendar className="h-5 w-5 text-brand-purple mb-2" />
                <p className="text-sm font-medium">
                  {campaign.expires_at ? new Date(campaign.expires_at).toLocaleDateString() : 'No end date'}
                </p>
                <p className="text-xs text-muted-foreground">Campaign Expires</p>
              </Card>
              <Card className="p-4 bg-brand-purple/5">
                <User className="h-5 w-5 text-brand-purple mb-2" />
                <p className="text-sm font-medium">{campaign.submissions_count || 0}</p>
                <p className="text-xs text-muted-foreground">Total Submissions</p>
              </Card>
              <Card className="p-4 bg-brand-purple/5">
                <Video className="h-5 w-5 text-brand-purple mb-2" />
                <p className="text-sm font-medium">{campaign.missions?.length || 0}</p>
                <p className="text-xs text-muted-foreground">Missions</p>
              </Card>
              <Card className="p-4 bg-brand-purple/5">
                <Share2 className="h-5 w-5 text-brand-purple mb-2" />
                <p className="text-sm font-medium">{campaign.selected_products?.length || 0}</p>
                <p className="text-xs text-muted-foreground">Social Shares</p>
              </Card>
            </div>
          </Card>

          <Card className="p-6 bg-white">
            <h2 className="text-lg font-semibold mb-4">Campaign Details</h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Products</h3>
                <div className="mt-2 space-y-3">
                  {campaign.selected_products && campaign.selected_products.length > 0 ? (
                    campaign.selected_products.map((product: any, index: number) => (
                      <div key={product._id || index} className="flex items-center">
                        <div className="h-12 w-12 rounded-md overflow-hidden mr-3 bg-gray-100">
                          {product.images && product.images.length > 0 ? (
                            <img
                              src={product.images[0].src || product.images[0].url}
                              alt={product.title || product.name || 'Product'}
                              className="h-full w-full object-cover"
                            />
                          ) : (
                            <div className="h-full w-full bg-gray-200 flex items-center justify-center">
                              <span className="text-xs text-gray-500">No Image</span>
                            </div>
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{product.title || product.name || 'Untitled Product'}</p>
                          <p className="text-xs text-muted-foreground">
                            {product.sku || product.shopify_product_id || `ID: ${product._id?.slice(-8)}`}
                          </p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-sm text-muted-foreground">No products selected</div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Reward</h3>
                <div className="mt-2">
                  <p className="font-medium">
                    {campaign.reward_settings?.amount || 0} {campaign.reward_settings?.token || campaign.reward_settings?.type || 'points'} per submission
                  </p>
                  <div className="space-y-1 mt-1">
                    <p className="text-xs text-muted-foreground">
                      {campaign.reward_settings?.approval_required ? 'Manual approval required' : 'Auto-approved'}
                    </p>
                    {campaign.reward_settings?.daily_limit && (
                      <p className="text-xs text-muted-foreground">
                        Daily limit: {campaign.reward_settings.daily_limit} {campaign.reward_settings?.token || campaign.reward_settings?.type || 'points'}
                      </p>
                    )}
                    {campaign.reward_settings?.user_limit && (
                      <p className="text-xs text-muted-foreground">
                        Per user limit: {campaign.reward_settings.user_limit} submissions
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Mission Requirements</h3>
                <div className="mt-2 space-y-2">
                  {campaign.missions && campaign.missions.length > 0 ? (
                    campaign.missions.map((mission: any, index: number) => {
                      const getMissionIcon = (type: string) => {
                        switch (type) {
                          case 'video': return <Video className="h-4 w-4 mr-2 text-brand-purple" />;
                          case 'social': return <Share2 className="h-4 w-4 mr-2 text-brand-purple" />;
                          default: return <Flag className="h-4 w-4 mr-2 text-brand-purple" />;
                        }
                      };

                      const getMissionDescription = (mission: any) => {
                        if (mission.title) return mission.title;
                        if (mission.description) return mission.description;
                        if (mission.instructions) return mission.instructions;

                        // Generate description based on mission type and properties
                        switch (mission.type) {
                          case 'video':
                            return `Video submission${mission.min_duration ? ` (min ${mission.min_duration}s)` : ''}`;
                          case 'social':
                            const platforms = mission.platforms?.join(', ') || 'social media';
                            const hashtags = mission.hashtags ? ` with ${mission.hashtags}` : '';
                            const mentions = mission.mentions ? ` mentioning ${mission.mentions}` : '';
                            return `Share on ${platforms}${hashtags}${mentions}`;
                          default:
                            return `${mission.type} mission`;
                        }
                      };

                      return (
                        <div key={index} className="flex items-start">
                          {getMissionIcon(mission.type)}
                          <div className="flex-1">
                            <span className="text-sm font-medium">{getMissionDescription(mission)}</span>
                            {mission.required && (
                              <span className="ml-2 text-xs bg-red-100 text-red-800 px-1.5 py-0.5 rounded">Required</span>
                            )}
                            {mission.instructions && mission.instructions !== getMissionDescription(mission) && (
                              <p className="text-xs text-muted-foreground mt-1">{mission.instructions}</p>
                            )}
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="flex items-center">
                      <Video className="h-4 w-4 mr-2 text-brand-purple" />
                      <span className="text-sm">No specific missions defined</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card>
        </div>

        <Tabs defaultValue="entries" className="bg-white rounded-lg shadow-sm p-6">
          <TabsList className="mb-6">
            <TabsTrigger value="entries">Entries</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>
          <TabsContent value="entries">
            <div className="mb-6 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <h2 className="text-lg font-semibold">Content Submissions</h2>
              <div className="flex flex-wrap gap-2">
                <Select 
                  value={filterVerified} 
                  onValueChange={setFilterVerified}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Verification Method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Methods</SelectItem>
                    <SelectItem value="qr">QR Verified</SelectItem>
                    <SelectItem value="nfc">NFC Verified</SelectItem>
                    <SelectItem value="manual">Manual</SelectItem>
                  </SelectContent>
                </Select>

                <Select 
                  value={filterStatus} 
                  onValueChange={setFilterStatus}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                    <SelectItem value="flagged">Flagged</SelectItem>
                  </SelectContent>
                </Select>

                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  More Filters
                </Button>
              </div>
            </div>

            {/* Table View for larger screens */}
            <div className="hidden md:block">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Media</TableHead>
                    <TableHead>Verification</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Social Tasks</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSubmissions.map((submission) => (
                    <TableRow key={submission.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-gray-200 mr-2"></div>
                          <span>{submission.user}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="relative h-14 w-20 bg-gray-100 rounded">
                          <img 
                            src={submission.mediaUrl} 
                            alt="Submission media" 
                            className="h-full w-full object-cover rounded"
                          />
                          {submission.mediaType === 'video' && (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <Video className="h-6 w-6 text-white drop-shadow-md" />
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getVerificationBadge(submission.verificationMethod)}
                        {submission.location && (
                          <div className="text-xs text-gray-500 mt-1">{submission.location}</div>
                        )}
                      </TableCell>
                      <TableCell>{submission.date}</TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          {submission.socialTasks.instagram && (
                            <Badge variant="outline" className="bg-purple-50">Instagram</Badge>
                          )}
                          {submission.socialTasks.tiktok && (
                            <Badge variant="outline" className="bg-purple-50">TikTok</Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(submission.status)}</TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          {submission.status === 'pending' && (
                            <>
                              <Button 
                                size="sm" 
                                onClick={() => handleApprove(submission.id)} 
                                className="bg-brand-purple hover:bg-brand-purple/90"
                              >
                                <Check className="h-3 w-3 mr-1" /> Approve
                              </Button>
                              <Button 
                                size="sm" 
                                variant="outline" 
                                onClick={() => handleReject(submission.id)}
                              >
                                <X className="h-3 w-3 mr-1" /> Reject
                              </Button>
                            </>
                          )}
                          {submission.status !== 'flagged' && submission.status !== 'rejected' && (
                            <Button 
                              size="sm" 
                              variant="outline" 
                              onClick={() => handleFlag(submission.id)}
                            >
                              <Flag className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Card View for mobile screens */}
            <div className="md:hidden grid grid-cols-1 gap-6">
              {filteredSubmissions.map((submission) => (
                <Card key={submission.id} className="overflow-hidden">
                  <div className="aspect-video bg-gray-100 relative">
                    <img 
                      src={submission.mediaUrl} 
                      alt="Submission media" 
                      className="h-full w-full object-cover"
                    />
                    {submission.mediaType === 'video' && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Video className="h-10 w-10 text-white drop-shadow-md" />
                      </div>
                    )}
                  </div>
                  <div className="p-4">
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-full bg-gray-200 mr-2"></div>
                        <span className="font-medium">{submission.user}</span>
                      </div>
                      <span className="text-xs text-muted-foreground">{submission.date}</span>
                    </div>
                    <div className="flex flex-wrap gap-1 mt-2 mb-3">
                      {getVerificationBadge(submission.verificationMethod)}
                      {getStatusBadge(submission.status)}
                      {submission.socialTasks.instagram && (
                        <Badge variant="outline" className="bg-purple-50">Instagram</Badge>
                      )}
                      {submission.socialTasks.tiktok && (
                        <Badge variant="outline" className="bg-purple-50">TikTok</Badge>
                      )}
                    </div>
                    {submission.location && (
                      <div className="text-xs text-gray-500 mb-3">{submission.location}</div>
                    )}
                    <div className="flex space-x-2 mt-3">
                      {submission.status === 'pending' && (
                        <>
                          <Button 
                            size="sm" 
                            onClick={() => handleApprove(submission.id)} 
                            className="bg-brand-purple text-white hover:bg-brand-purple/90 flex-1"
                          >
                            Approve
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleReject(submission.id)} className="flex-1">
                            Reject
                          </Button>
                        </>
                      )}
                      {submission.status !== 'flagged' && submission.status !== 'rejected' && (
                        <Button size="sm" variant="outline" onClick={() => handleFlag(submission.id)}>
                          <Flag className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>
          <TabsContent value="analytics">
            <div className="flex flex-col items-center justify-center py-12">
              <div className="h-48 w-full bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                <p className="text-muted-foreground">Analytics charts will appear here</p>
              </div>
              <p className="text-center text-muted-foreground">
                Detailed analytics for this campaign showing performance metrics,<br />
                user engagement, and reward distribution over time.
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}

export default CampaignDetail;
