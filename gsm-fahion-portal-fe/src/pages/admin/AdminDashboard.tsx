import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { adminAPI } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BrandDetailModal } from '@/components/admin/BrandDetailModal';
import {
  Users,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Loader2,
  Shield,
  LogOut,
  Calendar
} from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';

interface Brand {
  _id: string;
  email: string;
  status: 'pending' | 'approved' | 'rejected';
  email_verified: boolean;
  createdAt: string;
  profile?: {
    brand_name?: string;
    website?: string;
    bio?: string;
    logo_url?: string;
    profile_completed: boolean;
  };
}

export function AdminDashboard() {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const { user, logout } = useAuth();
  const { toast } = useToast();
  const location = useLocation();

  useEffect(() => {
    fetchBrands();
  }, []);

  const fetchBrands = async () => {
    try {
      const response = await adminAPI.getAllBrands();
      setBrands(response.data);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to fetch brands',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async (brandId: string) => {
    setActionLoading(brandId);
    try {
      await adminAPI.approveBrand(brandId);
      toast({
        title: 'Brand approved',
        description: 'Brand has been approved and notified via email',
      });
      fetchBrands();
      setIsModalOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to approve brand',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleReject = async (brandId: string) => {
    const reason = prompt('Enter rejection reason (optional):');
    setActionLoading(brandId);
    try {
      await adminAPI.rejectBrand(brandId, reason || undefined);
      toast({
        title: 'Brand rejected',
        description: 'Brand has been rejected and notified via email',
      });
      fetchBrands();
      setIsModalOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to reject brand',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleViewBrand = (brand: Brand) => {
    setSelectedBrand(brand);
    setIsModalOpen(true);
  };

  const getFilteredBrands = () => {
    switch (activeTab) {
      case 'pending':
        return brands.filter(brand => brand.user_id?.status === 'pending');
      case 'approved':
        return brands.filter(brand => brand.user_id?.status === 'approved');
      case 'rejected':
        return brands.filter(brand => brand.user_id?.status === 'rejected');
      default:
        return brands;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      default:
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
    }
  };

  const stats = {
    total: brands.length,
    pending: brands.filter(b => b.status === 'pending').length,
    approved: brands.filter(b => b.status === 'approved').length,
    rejected: brands.filter(b => b.status === 'rejected').length,
  };

  return (
    <div className="min-h-screen bg-slate-900">
      {/* Header */}
      <div className="bg-slate-800 border-b border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-red-500 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-white">Admin Portal</h1>
                <p className="text-slate-400">Fashion Brand Management</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-white">{user?.email}</p>
                <p className="text-xs text-slate-400">Administrator</p>
              </div>
              <Button
                onClick={logout}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-slate-800 border-b border-slate-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            <Link
              to="/admin"
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                location.pathname === '/admin'
                  ? 'border-red-500 text-white'
                  : 'border-transparent text-slate-400 hover:text-slate-300 hover:border-slate-300'
              }`}
            >
              <Users className="h-4 w-4 inline mr-2" />
              Brand Requests
            </Link>
            <Link
              to="/admin/campaigns"
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                location.pathname === '/admin/campaigns'
                  ? 'border-red-500 text-white'
                  : 'border-transparent text-slate-400 hover:text-slate-300 hover:border-slate-300'
              }`}
            >
              <Calendar className="h-4 w-4 inline mr-2" />
              Campaign Requests
            </Link>
          </nav>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-400">Total Brands</p>
                  <p className="text-2xl font-bold text-white">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-400">Pending</p>
                  <p className="text-2xl font-bold text-white">{stats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-400">Approved</p>
                  <p className="text-2xl font-bold text-white">{stats.approved}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-400">Rejected</p>
                  <p className="text-2xl font-bold text-white">{stats.rejected}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Brands List with Tabs */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Brand Applications</CardTitle>
            <CardDescription className="text-slate-400">
              Manage and review brand applications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4 bg-slate-700">
                <TabsTrigger value="all" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                  All ({brands.length})
                </TabsTrigger>
                <TabsTrigger value="pending" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                  Pending ({brands.filter(b => b.status === 'pending').length})
                </TabsTrigger>
                <TabsTrigger value="approved" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                  Approved ({brands.filter(b => b.status === 'approved').length})
                </TabsTrigger>
                <TabsTrigger value="rejected" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                  Rejected ({brands.filter(b => b.status === 'rejected').length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-6">
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
                  </div>
                ) : getFilteredBrands().length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-slate-400">
                      {activeTab === 'all' ? 'No brand applications found' : `No ${activeTab} applications found`}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {getFilteredBrands().map((brand) => (
                  <div
                    key={brand._id}
                    className="flex items-center justify-between p-4 bg-slate-700 rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <Avatar>
                        <AvatarImage src={brand.profile?.logo_url} />
                        <AvatarFallback className="bg-slate-600 text-white">
                          {brand.profile?.brand_name?.[0] || brand.user_id?.email?.[0]?.toUpperCase() || 'B'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-medium text-white">
                          {brand.profile?.brand_name || 'Unnamed Brand'}
                        </h3>
                        <p className="text-sm text-slate-400">{brand.user_id?.email}</p>
                        <p className="text-xs text-slate-500">
                          Applied: {new Date(brand.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      {getStatusBadge(brand.user_id?.status)}

                      {brand.user_id?.status === 'pending' && (
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            className="bg-green-600 hover:bg-green-700 text-white"
                            onClick={() => handleApprove(brand._id)}
                            disabled={actionLoading === brand._id}
                          >
                            {actionLoading === brand._id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <CheckCircle className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            size="sm"
                            className="bg-red-600 hover:bg-red-700 text-white"
                            onClick={() => handleReject(brand._id)}
                            disabled={actionLoading === brand._id}
                          >
                            <XCircle className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                      
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-slate-600 text-slate-300 hover:bg-slate-600"
                        onClick={() => handleViewBrand(brand)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
                </div>
              )}
            </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Brand Detail Modal */}
        <BrandDetailModal
          brand={selectedBrand}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onApprove={handleApprove}
          onReject={handleReject}
          isLoading={!!actionLoading}
        />
      </div>
    </div>
  );
}
