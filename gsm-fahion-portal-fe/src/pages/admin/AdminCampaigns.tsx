import { useState, useEffect } from 'react';
import { adminAPI } from '@/lib/api';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { CampaignDetailModal } from '@/components/admin/CampaignDetailModal';
import {
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Loader2,
  Package
} from 'lucide-react';

interface Campaign {
  _id: string;
  name: string;
  description: string;
  banner_url?: string;
  status: 'pending' | 'approved' | 'rejected' | 'active' | 'completed';
  brand_id: {
    email: string;
    _id: string;
  };
  selected_products: Array<{
    _id: string;
    title: string;
    images: Array<{ src: string }>;
  }>;
  reward_settings: {
    type: string;
    amount: number;
    token?: string;
  };
  missions: Array<{
    type: string;
    title?: string;
    description?: string;
    instructions?: string;
  }>;
  submissions_count: number;
  target_submissions?: number;
  createdAt: string;
  admin_notes?: string;
}

export function AdminCampaigns() {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const { toast } = useToast();

  useEffect(() => {
    fetchCampaigns();
  }, []);

  const fetchCampaigns = async () => {
    try {
      const response = await adminAPI.getAllCampaigns();
      setCampaigns(response.data.campaigns || []);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to fetch campaigns',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async (campaignId: string, notes?: string) => {
    setActionLoading(campaignId);
    try {
      await adminAPI.approveCampaign(campaignId, notes);
      toast({
        title: 'Campaign approved',
        description: 'Campaign has been approved and is now active',
      });
      fetchCampaigns();
      setIsModalOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to approve campaign',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleReject = async (campaignId: string, notes?: string) => {
    setActionLoading(campaignId);
    try {
      await adminAPI.rejectCampaign(campaignId, notes);
      toast({
        title: 'Campaign rejected',
        description: 'Campaign has been rejected',
      });
      fetchCampaigns();
      setIsModalOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: 'Failed to reject campaign',
        variant: 'destructive',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleViewCampaign = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setIsModalOpen(true);
  };

  const getFilteredCampaigns = () => {
    switch (activeTab) {
      case 'pending':
        return campaigns.filter(campaign => campaign.status === 'pending');
      case 'approved':
        return campaigns.filter(campaign => campaign.status === 'approved');
      case 'active':
        return campaigns.filter(campaign => campaign.status === 'active');
      case 'rejected':
        return campaigns.filter(campaign => campaign.status === 'rejected');
      default:
        return campaigns;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800">Approved</Badge>;
      case 'active':
        return <Badge className="bg-blue-100 text-blue-800">Active</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800">Rejected</Badge>;
      case 'completed':
        return <Badge className="bg-gray-100 text-gray-800">Completed</Badge>;
      default:
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
    }
  };

  const stats = {
    total: campaigns.length,
    pending: campaigns.filter(c => c.status === 'pending').length,
    approved: campaigns.filter(c => c.status === 'approved').length,
    active: campaigns.filter(c => c.status === 'active').length,
    rejected: campaigns.filter(c => c.status === 'rejected').length,
  };

  return (
    <AdminLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white">Campaign Requests</h1>
          <p className="text-slate-400 mt-1">Review and manage campaign submissions</p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-400">Total Campaigns</p>
                  <p className="text-2xl font-bold text-white">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-400">Pending</p>
                  <p className="text-2xl font-bold text-white">{stats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-400">Approved</p>
                  <p className="text-2xl font-bold text-white">{stats.approved}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center">
                <Package className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-400">Active</p>
                  <p className="text-2xl font-bold text-white">{stats.active}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-500" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-slate-400">Rejected</p>
                  <p className="text-2xl font-bold text-white">{stats.rejected}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Campaigns List with Tabs */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Campaign Submissions</CardTitle>
            <CardDescription className="text-slate-400">
              Manage and review campaign submissions from brands
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-5 bg-slate-700">
                <TabsTrigger value="all" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                  All ({campaigns.length})
                </TabsTrigger>
                <TabsTrigger value="pending" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                  Pending ({stats.pending})
                </TabsTrigger>
                <TabsTrigger value="approved" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                  Approved ({stats.approved})
                </TabsTrigger>
                <TabsTrigger value="active" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                  Active ({stats.active})
                </TabsTrigger>
                <TabsTrigger value="rejected" className="text-slate-300 data-[state=active]:bg-slate-600 data-[state=active]:text-white">
                  Rejected ({stats.rejected})
                </TabsTrigger>
              </TabsList>

              <TabsContent value={activeTab} className="mt-6">
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
                  </div>
                ) : getFilteredCampaigns().length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-slate-400">
                      {activeTab === 'all' ? 'No campaigns found' : `No ${activeTab} campaigns found`}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {getFilteredCampaigns().map((campaign) => (
                      <div
                        key={campaign._id}
                        className="flex items-center justify-between p-4 bg-slate-700 rounded-lg"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="flex -space-x-2">
                            {campaign.selected_products.slice(0, 3).map((product, index) => (
                              <Avatar key={product._id} className="border-2 border-slate-700">
                                <AvatarImage src={product.images?.[0]?.src} />
                                <AvatarFallback className="bg-slate-600 text-white text-xs">
                                  {product.title[0]}
                                </AvatarFallback>
                              </Avatar>
                            ))}
                            {campaign.selected_products.length > 3 && (
                              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-slate-600 border-2 border-slate-700 text-xs text-white">
                                +{campaign.selected_products.length - 3}
                              </div>
                            )}
                          </div>
                          <div>
                            <h3 className="font-medium text-white">{campaign.name}</h3>
                            <p className="text-sm text-slate-400">{campaign.brand_id.email}</p>
                            <p className="text-xs text-slate-500">
                              Submitted: {new Date(campaign.createdAt).toLocaleDateString()}
                            </p>
                            <div className="flex items-center mt-1 space-x-2">
                              <Badge variant="outline" className="text-xs">
                                {campaign.reward_settings.type} • {campaign.reward_settings.amount} {campaign.reward_settings.token}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {campaign.selected_products.length} product{campaign.selected_products.length > 1 ? 's' : ''}
                              </Badge>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-3">
                          {getStatusBadge(campaign.status)}

                          {campaign.status === 'pending' && (
                            <div className="flex space-x-2">
                              <Button
                                size="sm"
                                className="bg-green-600 hover:bg-green-700 text-white"
                                onClick={() => handleApprove(campaign._id)}
                                disabled={actionLoading === campaign._id}
                              >
                                {actionLoading === campaign._id ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <CheckCircle className="h-4 w-4" />
                                )}
                              </Button>
                              <Button
                                size="sm"
                                className="bg-red-600 hover:bg-red-700 text-white"
                                onClick={() => handleReject(campaign._id)}
                                disabled={actionLoading === campaign._id}
                              >
                                <XCircle className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                          
                          <Button
                            size="sm"
                            variant="outline"
                            className="border-slate-600 text-slate-300 hover:bg-slate-600"
                            onClick={() => handleViewCampaign(campaign)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Campaign Detail Modal */}
        <CampaignDetailModal
          campaign={selectedCampaign}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onApprove={handleApprove}
          onReject={handleReject}
          isLoading={!!actionLoading}
        />
      </div>
    </AdminLayout>
  );
}
