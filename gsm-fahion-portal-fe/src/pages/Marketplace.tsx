
import { useState } from "react";
import { Search, Filter, Tag, Package } from "lucide-react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export function Marketplace() {
  const [products, setProducts] = useState([
    {
      id: 1,
      name: "Eco Friendly T-Shirt",
      image: "https://images.unsplash.com/photo-1581655353564-df123a1eb820?q=80&w=500",
      price: 29.99,
      category: "Clothing",
      campaignCount: 2,
      verificationTypes: ["qr", "manual"]
    },
    {
      id: 2,
      name: "Designer Sneakers",
      image: "https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?q=80&w=500",
      price: 149.99,
      category: "Footwear",
      campaignCount: 1,
      verificationTypes: ["nfc"]
    },
    {
      id: 3,
      name: "<PERSON> Dress",
      image: "https://images.unsplash.com/photo-1612336307429-8a898d10e223?q=80&w=500",
      price: 79.99,
      category: "Clothing",
      campaignCount: 0,
      verificationTypes: ["manual"]
    },
    {
      id: 4,
      name: "Luxury Handbag",
      image: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?q=80&w=500",
      price: 299.99,
      category: "Accessories",
      campaignCount: 3,
      verificationTypes: ["qr", "nfc"]
    },
    {
      id: 5,
      name: "Smart Watch",
      image: "https://images.unsplash.com/photo-1508685096489-7aacd43bd3b1?q=80&w=500",
      price: 199.99,
      category: "Electronics",
      campaignCount: 1,
      verificationTypes: ["qr"]
    },
    {
      id: 6,
      name: "Wireless Headphones",
      image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?q=80&w=500",
      price: 89.99,
      category: "Electronics",
      campaignCount: 2,
      verificationTypes: ["manual"]
    },
  ]);

  const categories = ["All", "Clothing", "Footwear", "Accessories", "Electronics"];
  const [selectedCategory, setSelectedCategory] = useState("All");

  const filteredProducts = selectedCategory === "All"
    ? products
    : products.filter(product => product.category === selectedCategory);

  return (
    <AppLayout>
      <div className="py-6 px-6 max-w-7xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-brand-dark-purple">Marketplace</h1>
            <p className="text-muted-foreground mt-1">Discover products available for UGC campaigns</p>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search products..."
                className="pl-8 w-full"
              />
            </div>
            <div className="flex gap-2">
              <Select 
                value={selectedCategory} 
                onValueChange={setSelectedCategory}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Categories" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>{category}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button variant="outline" className="px-3">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {filteredProducts.map((product) => (
            <Card key={product.id} className="overflow-hidden hover:shadow-md transition-shadow">
              <div className="aspect-square relative overflow-hidden">
                <img 
                  src={product.image} 
                  alt={product.name} 
                  className="h-full w-full object-cover transition-transform hover:scale-105 duration-200"
                />
                
                <div className="absolute top-2 right-2 flex gap-1">
                  {product.verificationTypes.includes("qr") && (
                    <div className="bg-white/90 p-1.5 rounded-md" title="QR Verification Available">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="5" height="5" x="3" y="3" rx="1" /><rect width="5" height="5" x="16" y="3" rx="1" /><rect width="5" height="5" x="3" y="16" rx="1" /><path d="M21 16h-3a2 2 0 0 0-2 2v3" /><path d="M21 21v.01" /><path d="M12 7v3a2 2 0 0 1-2 2H7" /><path d="M3 12h.01" /><path d="M12 3h.01" /><path d="M12 16v.01" /><path d="M16 12h1" /><path d="M21 12v.01" /><path d="M12 21v-1" /></svg>
                    </div>
                  )}
                  {product.verificationTypes.includes("nfc") && (
                    <div className="bg-white/90 p-1.5 rounded-md" title="NFC Verification Available">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M6 8.32a7.43 7.43 0 0 1 0 7.36" /><path d="M9.46 6.21a11.76 11.76 0 0 1 0 11.58" /><path d="M12.91 4.1a15.91 15.91 0 0 1 .01 15.8" /><path d="M16.37 2a20.16 20.16 0 0 1 0 20" /></svg>
                    </div>
                  )}
                </div>
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium text-lg">{product.name}</h3>
                    <span className="text-sm text-muted-foreground">{product.category}</span>
                  </div>
                  <span className="font-bold">${product.price.toFixed(2)}</span>
                </div>
                
                <div className="flex mt-3 space-x-2">
                  {product.campaignCount > 0 ? (
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-brand-purple/10 text-brand-purple">
                      <Package className="h-3 w-3 mr-1" /> {product.campaignCount} Campaigns
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600">
                      No Campaigns
                    </span>
                  )}
                </div>
                
                <div className="flex mt-4">
                  <Button size="sm" className="button-gradient text-white w-full mr-2">View Details</Button>
                  <Button size="sm" variant="outline" className="px-3">
                    <Tag className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </AppLayout>
  );
}

export default Marketplace;
