import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  ChevronLeft,
  ShoppingBag,
  FileText,
  Code,
  CheckCircle,
  AlertCircle,
  Upload,
  Download
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Link, useNavigate } from "react-router-dom";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ShopifyConnectionCard } from "@/components/shopify/ShopifyConnectionCard";
import { useShopify } from "@/contexts/ShopifyContext";
import { CsvImportWorkflow } from "@/components/csv-import/CsvImportWorkflow";

export function ProductImportPage() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [importStatus, setImportStatus] = useState<null | 'success' | 'error'>(null);
  const [importMethod, setImportMethod] = useState<string>("shopify");
  const [importedCount, setImportedCount] = useState(0);
  const { connection, syncProducts } = useShopify();

  const requestCustomIntegration = () => {
    toast({
      title: "Request sent",
      description: "Our team will contact you shortly to discuss custom integration.",
    });
  };

  const handleSetupVerification = () => {
    navigate(`/verification-setup?count=${importedCount}&method=${importMethod}`);
  };

  const handleCsvImportComplete = (count: number) => {
    setImportedCount(count);
    setImportStatus('success');
    setImportMethod('csv');
  };

  return (
    <AppLayout>
      <div className="py-6 px-6 max-w-4xl mx-auto">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" asChild className="mr-2">
            <Link to="/">
              <ChevronLeft className="h-5 w-5" />
            </Link>
          </Button>
          <h1 className="text-3xl font-bold text-brand-dark-purple">Import Products</h1>
        </div>

        {importStatus === 'success' ? (
          <Card className="p-6 text-center">
            <div className="flex flex-col items-center">
              <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h2 className="text-2xl font-semibold mb-2">Import Successful!</h2>
              <p className="text-muted-foreground mb-6">
                {importedCount} products have been imported and are ready for verification setup.
              </p>
              <div className="flex gap-4">
                <Button variant="outline" asChild>
                  <Link to="/products">View Products</Link>
                </Button>
                <Button className="button-gradient text-white" onClick={handleSetupVerification}>
                  Set Up Verification
                </Button>
              </div>
            </div>
          </Card>
        ) : (
          <Tabs value={importMethod} onValueChange={setImportMethod}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="shopify">Shopify</TabsTrigger>
              <TabsTrigger value="csv">CSV Upload</TabsTrigger>
              <TabsTrigger value="custom">Custom Integration</TabsTrigger>
            </TabsList>
            
            <TabsContent value="shopify" className="animate-fade-in">
              <ShopifyConnectionCard
                showTitle={false}
                showDescription={true}
                compact={false}
              />
            </TabsContent>
            
            <TabsContent value="csv" className="animate-fade-in">
              <CsvImportWorkflow onComplete={handleCsvImportComplete} />
            </TabsContent>
            
            <TabsContent value="custom" className="animate-fade-in">
              <Card className="p-6">
                <div className="flex items-center justify-center flex-col">
                  <div className="h-20 w-20 rounded-full bg-amber-100 flex items-center justify-center mb-4">
                    <Code className="h-10 w-10 text-amber-600" />
                  </div>
                  <h2 className="text-2xl font-semibold mb-2">Custom Integration</h2>
                  <p className="text-muted-foreground mb-6 text-center max-w-md">
                    Need to connect a custom platform or e-commerce system? Our team can help build a custom integration.
                  </p>
                  
                  <div className="bg-amber-50 p-4 rounded-lg mb-6 w-full max-w-md">
                    <div className="flex items-start">
                      <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 mr-2" />
                      <p className="text-sm text-amber-800">
                        Custom integrations typically take 3-5 business days to implement after requirements are finalized.
                      </p>
                    </div>
                  </div>
                  
                  <Button 
                    size="lg" 
                    onClick={requestCustomIntegration}
                  >
                    Request Custom Integration
                  </Button>
                </div>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </AppLayout>
  );
}

export default ProductImportPage;
