
import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { AppLayout } from "@/components/layout/AppLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  QrCode, 
  Tag, 
  Package, 
  Download, 
  Printer, 
  Plus, 
  Minus,
  Info,
  CheckCircle,
  ArrowRight
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Product {
  id: string;
  name: string;
  category: string;
  sku: string;
  image: string;
  defaultQty: number;
  requestedQty: number;
}

export function VerificationSetup() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const [step, setStep] = useState(1);
  const [wantsVerification, setWantsVerification] = useState<boolean | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  
  const [products, setProducts] = useState<Product[]>([
    {
      id: "1",
      name: "Eco Friendly T-Shirt",
      category: "Apparel",
      sku: "ECO-TS-001",
      image: "https://images.unsplash.com/photo-1581655353564-df123a1eb820?q=80&w=300",
      defaultQty: 50,
      requestedQty: 50
    },
    {
      id: "2",
      name: "Designer Sneakers",
      category: "Footwear",
      sku: "SHO-SN-452",
      image: "https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?q=80&w=300",
      defaultQty: 25,
      requestedQty: 25
    },
    {
      id: "3",
      name: "Summer Dress",
      category: "Apparel",
      sku: "FAS-DR-118",
      image: "https://images.unsplash.com/photo-1612336307429-8a898d10e223?q=80&w=300",
      defaultQty: 30,
      requestedQty: 30
    }
  ]);

  const chipCost = 0.40;
  const totalChips = products.reduce((sum, product) => sum + product.requestedQty, 0);
  const totalCost = totalChips * chipCost;

  const updateQuantity = (productId: string, change: number) => {
    setProducts(prev => prev.map(product => 
      product.id === productId 
        ? { ...product, requestedQty: Math.max(0, product.requestedQty + change) }
        : product
    ));
  };

  const handleVerificationChoice = (choice: boolean) => {
    setWantsVerification(choice);
    if (choice) {
      setStep(2);
    } else {
      navigate('/campaigns/create');
    }
  };

  const handleConfirmNFCRequest = () => {
    setShowConfirmDialog(false);
    setShowSuccessModal(true);
    
    toast({
      title: "NFC Request Confirmed",
      description: `Request for ${totalChips} chips confirmed. Awaiting payment.`,
    });
  };

  const handleDownloadQRCodes = () => {
    toast({
      title: "QR Codes Downloaded",
      description: "All QR codes have been downloaded as a ZIP file.",
    });
  };

  const handlePrintLabels = () => {
    toast({
      title: "Print Job Sent",
      description: "QR code labels have been sent to your default printer.",
    });
  };

  if (step === 1) {
    return (
      <AppLayout>
        <div className="py-6 px-6 max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-brand-dark-purple mb-2">Products Imported Successfully!</h1>
            <p className="text-muted-foreground">
              {searchParams.get('count') || '3'} products have been imported and are ready for verification setup.
            </p>
          </div>

          <Card className="max-w-2xl mx-auto">
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center gap-2">
                <Tag className="h-5 w-5 text-brand-purple" />
                Set Up Product Verification
              </CardTitle>
              <p className="text-muted-foreground">
                Would you like to assign NFC or QR verification to your products for campaign security?
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card 
                  className="p-4 cursor-pointer border-2 border-transparent hover:border-brand-purple transition-colors"
                  onClick={() => handleVerificationChoice(true)}
                >
                  <div className="text-center">
                    <div className="h-12 w-12 rounded-full bg-brand-purple/10 flex items-center justify-center mx-auto mb-3">
                      <QrCode className="h-6 w-6 text-brand-purple" />
                    </div>
                    <h3 className="font-semibold mb-2">✅ Yes, Set Up Verification</h3>
                    <p className="text-sm text-muted-foreground">
                      Add QR codes and/or NFC chips to secure your campaigns
                    </p>
                  </div>
                </Card>

                <Card 
                  className="p-4 cursor-pointer border-2 border-transparent hover:border-gray-300 transition-colors"
                  onClick={() => handleVerificationChoice(false)}
                >
                  <div className="text-center">
                    <div className="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-3">
                      <ArrowRight className="h-6 w-6 text-gray-600" />
                    </div>
                    <h3 className="font-semibold mb-2">❌ Skip for Now</h3>
                    <p className="text-sm text-muted-foreground">
                      Go directly to campaign creation
                    </p>
                  </div>
                </Card>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-start space-x-2">
                  <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-blue-900 mb-1">Why use verification?</p>
                    <p className="text-blue-700">
                      QR codes and NFC chips ensure only authentic product owners can participate in your campaigns, 
                      preventing fraud and maintaining campaign integrity.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="py-6 px-6 max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-brand-dark-purple mb-2">Verification Setup</h1>
          <p className="text-muted-foreground">Configure QR codes and NFC chips for your imported products</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* QR Code Setup */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <QrCode className="h-5 w-5 text-purple-600" />
                QR Code Setup
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Free and ready to use immediately
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center p-6 bg-gray-50 rounded-lg">
                <div className="h-32 w-32 bg-white border mx-auto mb-4 flex items-center justify-center">
                  <img 
                    src="https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=https://verify.brand.com/sample" 
                    alt="Sample QR Code" 
                    className="max-h-full"
                  />
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  Unique QR codes will be auto-generated for each product
                </p>
                <Badge variant="outline" className="bg-green-50 text-green-700">
                  {products.length} QR codes ready
                </Badge>
              </div>

              <div className="space-y-2">
                <Button onClick={handleDownloadQRCodes} className="w-full" variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Download All QR Codes (ZIP)
                </Button>
                <Button onClick={handlePrintLabels} className="w-full" variant="outline">
                  <Printer className="h-4 w-4 mr-2" />
                  Print Labels
                </Button>
              </div>

              <div className="bg-green-50 p-3 rounded-lg">
                <p className="text-sm text-green-800">
                  <strong>✓ QR codes are free</strong> and can be used immediately for campaign verification.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* NFC Chip Request */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Tag className="h-5 w-5 text-blue-600" />
                NFC Chip Request
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">NFC chips provide secure, tap-to-verify authentication for premium product verification</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Physical chips shipped after payment confirmation
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {products.map((product) => (
                  <div key={product.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                    <img 
                      src={product.image} 
                      alt={product.name}
                      className="h-12 w-12 rounded object-cover"
                    />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{product.name}</p>
                      <p className="text-sm text-muted-foreground">{product.category}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button 
                        variant="outline" 
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => updateQuantity(product.id, -1)}
                        disabled={product.requestedQty <= 0}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                      <Input 
                        value={product.requestedQty}
                        onChange={(e) => {
                          const qty = parseInt(e.target.value) || 0;
                          setProducts(prev => prev.map(p => 
                            p.id === product.id ? { ...p, requestedQty: qty } : p
                          ));
                        }}
                        className="w-16 text-center"
                      />
                      <Button 
                        variant="outline" 
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => updateQuantity(product.id, 1)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              <Separator />

              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span>Total Chips Requested:</span>
                  <span className="font-semibold">{totalChips}</span>
                </div>
                <div className="flex justify-between">
                  <span>Cost per Chip:</span>
                  <span>${chipCost.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-lg font-semibold">
                  <span>Estimated Total:</span>
                  <span>${totalCost.toFixed(2)}</span>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Payment handled externally after confirmation
                </p>
              </div>

              <Button 
                onClick={() => setShowConfirmDialog(true)}
                className="w-full button-gradient text-white"
                disabled={totalChips === 0}
              >
                Confirm NFC Request
              </Button>

              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>ℹ️ NFC chips</strong> will be shipped after external payment is completed.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-6 flex justify-center">
          <Button 
            onClick={() => navigate('/campaigns/create')}
            variant="outline"
            size="lg"
          >
            Skip NFC & Continue to Campaign Creation
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>

        {/* Confirmation Dialog */}
        <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Confirm NFC Chip Request</DialogTitle>
              <DialogDescription>
                You're about to request {totalChips} NFC chips for an estimated cost of ${totalCost.toFixed(2)}.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Order Summary:</h4>
                {products.filter(p => p.requestedQty > 0).map(product => (
                  <div key={product.id} className="flex justify-between text-sm">
                    <span>{product.name}</span>
                    <span>{product.requestedQty} chips</span>
                  </div>
                ))}
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={() => setShowConfirmDialog(false)} className="flex-1">
                  Cancel
                </Button>
                <Button onClick={handleConfirmNFCRequest} className="flex-1">
                  Confirm Request
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Success Modal */}
        <Dialog open={showSuccessModal} onOpenChange={setShowSuccessModal}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="text-center">Request Confirmed!</DialogTitle>
              <DialogDescription className="text-center">
                Your NFC chip request has been submitted successfully.
              </DialogDescription>
            </DialogHeader>
            <div className="text-center space-y-4">
              <div className="h-16 w-16 rounded-full bg-green-100 flex items-center justify-center mx-auto">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="space-y-2">
                <p className="font-medium">Status: Request Confirmed, Awaiting Payment</p>
                <p className="text-sm text-muted-foreground">
                  You'll receive payment instructions via email shortly.
                </p>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={() => navigate('/nfc-requests')} className="flex-1">
                  View NFC Orders
                </Button>
                <Button onClick={() => navigate('/campaigns/create')} className="flex-1">
                  Create Campaign
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </AppLayout>
  );
}

export default VerificationSetup;
