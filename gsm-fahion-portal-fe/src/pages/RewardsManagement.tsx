
import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Search, Filter, Check, X, AlertCircle } from "lucide-react";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

export function RewardsManagement() {
  const [submissions, setSubmissions] = useState([
    { id: 1, status: "pending", user: "<PERSON>", product: "Eco Friendly T-Shirt", date: "2 hours ago", hasVideo: true, hasSocial: true },
    { id: 2, status: "pending", user: "<PERSON>", product: "Designer Sneakers", date: "3 hours ago", hasVideo: true, hasSocial: false },
    { id: 3, status: "pending", user: "<PERSON>", product: "<PERSON> Dress", date: "5 hours ago", hasVideo: true, hasSocial: true },
    { id: 4, status: "flagged", user: "<PERSON> Reed", product: "Luxury Handbag", date: "8 hours ago", hasVideo: true, hasSocial: true },
  ]);

  const [history, setHistory] = useState([
    { id: 101, status: "completed", user: "Morgan Lee", product: "Eco Friendly T-Shirt", date: "2 days ago", reward: "10 USDC", txHash: "0x3a2...7b9c" },
    { id: 102, status: "completed", user: "Jordan Taylor", product: "Designer Sneakers", date: "3 days ago", reward: "15 USDC", txHash: "0x45d...9e2f" },
    { id: 103, status: "rejected", user: "Riley Johnson", product: "Summer Dress", date: "4 days ago", reason: "Low quality content" },
    { id: 104, status: "processing", user: "Quinn Martinez", product: "Luxury Handbag", date: "1 day ago", reward: "20 USDC" },
  ]);

  const handleApprove = (id: number) => {
    // Implementation would handle approval logic
    console.log(`Approving submission ${id}`);
  };

  const handleReject = (id: number) => {
    // Implementation would handle rejection logic
    console.log(`Rejecting submission ${id}`);
  };

  const handleFlag = (id: number) => {
    // Implementation would handle flagging logic
    console.log(`Flagging submission ${id}`);
  };

  return (
    <AppLayout>
      <div className="py-6 px-6 max-w-7xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-brand-dark-purple">Rewards Management</h1>
            <p className="text-muted-foreground mt-1">Review and manage user submissions and rewards</p>
          </div>
        </div>

        <Tabs defaultValue="pending" className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
            <TabsList>
              <TabsTrigger value="pending">Pending Reviews</TabsTrigger>
              <TabsTrigger value="history">Reward History</TabsTrigger>
            </TabsList>
            <div className="flex gap-2 mt-4 sm:mt-0">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search..."
                  className="pl-8 w-[200px]"
                />
              </div>
              <Button variant="outline" className="px-3">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
          </div>

          <TabsContent value="pending">
            <div className="space-y-4">
              {submissions.map((submission) => (
                <Card key={submission.id} className={`p-4 ${submission.status === 'flagged' ? 'border-amber-300' : ''}`}>
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="w-full md:w-1/4 lg:w-1/5">
                      <div className="aspect-video bg-gray-100 rounded-md flex items-center justify-center">
                        {submission.hasVideo && <span className="text-gray-400">Video Preview</span>}
                        {!submission.hasVideo && <span className="text-gray-400">No Video</span>}
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-gray-200 mr-2"></div>
                          <div>
                            <p className="font-medium">{submission.user}</p>
                            <p className="text-xs text-muted-foreground">{submission.date}</p>
                          </div>
                        </div>
                        <div className="mt-2 sm:mt-0">
                          {submission.status === "flagged" && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                              <AlertCircle className="h-3 w-3 mr-1" /> Flagged by AI
                            </span>
                          )}
                          {submission.status === "pending" && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Pending Review
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="mb-3">
                        <p className="text-sm font-medium">Product: {submission.product}</p>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {submission.hasVideo && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-brand-purple/10 text-brand-purple">
                              Video Submitted
                            </span>
                          )}
                          {submission.hasSocial && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-brand-purple/10 text-brand-purple">
                              Shared on Social
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        <Button 
                          size="sm" 
                          onClick={() => handleApprove(submission.id)}
                          className="bg-green-600 hover:bg-green-700 text-white"
                        >
                          <Check className="h-4 w-4 mr-1" /> Approve
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleReject(submission.id)}
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <X className="h-4 w-4 mr-1" /> Reject
                        </Button>
                        <Button size="sm" variant="outline">View Details</Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="history">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reward</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {history.map((item) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-8 w-8 rounded-full bg-gray-200 mr-2"></div>
                          <div className="text-sm font-medium">{item.user}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">{item.product}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.date}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {item.status === "completed" && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Completed
                          </span>
                        )}
                        {item.status === "rejected" && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Rejected
                          </span>
                        )}
                        {item.status === "processing" && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Processing
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">{item.reward || "-"}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {item.txHash ? (
                          <a href="#" className="text-brand-purple hover:underline">{item.txHash}</a>
                        ) : "-"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <Button size="sm" variant="outline">View</Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}

export default RewardsManagement;
