
import { useState, useEffect } from "react";
import { Search, Plus, Filter, Calendar, User } from "lucide-react";
import { AppLayout } from "@/components/layout/AppLayout";
import { CampaignCard } from "@/components/dashboard/CampaignCard";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { campaignsAPI } from "@/lib/api";

type CampaignStatus = 'active' | 'scheduled' | 'draft' | 'ended';

interface Campaign {
  id: string;
  title: string;
  description: string;
  status: CampaignStatus;
  submissions: number;
  target: number;
  daysLeft?: number;
  startDate?: string;
  product?: string;
  verificationMethod: 'manual' | 'qr' | 'nfc';
  rewardType: 'crypto' | 'nft' | 'points';
  createdAt: string;
  banner_url?: string;
}

export function CampaignList() {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [verificationFilter, setVerificationFilter] = useState<string>("all");
  const [rewardFilter, setRewardFilter] = useState<string>("all");
  const [view, setView] = useState<"grid" | "list">("grid");

  // Load campaigns from API
  const loadCampaigns = async () => {
    try {
      setLoading(true);
      const result = await campaignsAPI.getCampaigns();

      if (result.data.success) {
        // Transform API data to match our Campaign interface
        const transformedCampaigns: Campaign[] = result.data.campaigns.map((apiCampaign: any) => ({
          id: apiCampaign._id,
          title: apiCampaign.name,
          description: apiCampaign.description,
          status: apiCampaign.status,
          submissions: apiCampaign.submissions_count || 0,
          target: apiCampaign.target_submissions || 0,
          daysLeft: apiCampaign.expires_at ? Math.ceil((new Date(apiCampaign.expires_at).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : undefined,
          product: apiCampaign.selected_products?.[0]?.title || 'Multiple Products',
          verificationMethod: 'qr', // Default for now
          rewardType: apiCampaign.reward_settings?.type || 'crypto',
          createdAt: new Date(apiCampaign.createdAt).toISOString().split('T')[0],
          banner_url: apiCampaign.banner_url
        }));

        setCampaigns(transformedCampaigns);
      } else {
        throw new Error(result.data.error || 'Failed to load campaigns');
      }
    } catch (error: any) {
      console.error('Failed to load campaigns:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to load campaigns',
        variant: 'destructive',
      });
      setCampaigns([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCampaigns();
  }, []);

  // Filter campaigns based on criteria
  const filteredCampaigns = campaigns.filter(campaign => {
    // Filter by search query
    if (searchQuery && 
        !campaign.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !campaign.description.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !(campaign.product && campaign.product.toLowerCase().includes(searchQuery.toLowerCase()))
       ) {
      return false;
    }
    
    // Filter by status
    if (statusFilter !== "all" && campaign.status !== statusFilter) {
      return false;
    }
    
    // Filter by verification method
    if (verificationFilter !== "all" && campaign.verificationMethod !== verificationFilter) {
      return false;
    }
    
    // Filter by reward type
    if (rewardFilter !== "all" && campaign.rewardType !== rewardFilter) {
      return false;
    }
    
    return true;
  });

  const resetFilters = () => {
    setSearchQuery("");
    setStatusFilter("all");
    setVerificationFilter("all");
    setRewardFilter("all");
  };

  return (
    <AppLayout>
      <div className="py-6 px-6 max-w-7xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-brand-dark-purple">Campaigns</h1>
            <p className="text-muted-foreground mt-1">Create and manage your UGC campaigns</p>
          </div>
          <div className="mt-4 sm:mt-0">
            <Button className="button-gradient text-white" asChild>
              <Link to="/campaigns/create">
                <Plus className="mr-2 h-4 w-4" /> Create Campaign
              </Link>
            </Button>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search campaigns..."
                className="pl-8 w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex flex-wrap gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="ended">Ended</SelectItem>
                </SelectContent>
              </Select>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="px-3">
                    <Filter className="h-4 w-4 mr-2" />
                    More Filters
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuLabel>Filter Campaigns</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  
                  <DropdownMenuLabel className="text-xs font-normal text-muted-foreground mt-2">
                    Verification Method
                  </DropdownMenuLabel>
                  <DropdownMenuItem onClick={() => setVerificationFilter("all")}>
                    <div className={`w-4 h-4 mr-2 rounded-full ${verificationFilter === "all" ? "bg-brand-purple" : "border border-gray-300"}`} />
                    All Methods
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setVerificationFilter("manual")}>
                    <div className={`w-4 h-4 mr-2 rounded-full ${verificationFilter === "manual" ? "bg-brand-purple" : "border border-gray-300"}`} />
                    Manual
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setVerificationFilter("qr")}>
                    <div className={`w-4 h-4 mr-2 rounded-full ${verificationFilter === "qr" ? "bg-brand-purple" : "border border-gray-300"}`} />
                    QR Code
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setVerificationFilter("nfc")}>
                    <div className={`w-4 h-4 mr-2 rounded-full ${verificationFilter === "nfc" ? "bg-brand-purple" : "border border-gray-300"}`} />
                    NFC Chip
                  </DropdownMenuItem>
                  
                  <DropdownMenuSeparator />
                  
                  <DropdownMenuLabel className="text-xs font-normal text-muted-foreground">
                    Reward Type
                  </DropdownMenuLabel>
                  <DropdownMenuItem onClick={() => setRewardFilter("all")}>
                    <div className={`w-4 h-4 mr-2 rounded-full ${rewardFilter === "all" ? "bg-brand-purple" : "border border-gray-300"}`} />
                    All Rewards
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setRewardFilter("crypto")}>
                    <div className={`w-4 h-4 mr-2 rounded-full ${rewardFilter === "crypto" ? "bg-brand-purple" : "border border-gray-300"}`} />
                    Crypto Token
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setRewardFilter("nft")}>
                    <div className={`w-4 h-4 mr-2 rounded-full ${rewardFilter === "nft" ? "bg-brand-purple" : "border border-gray-300"}`} />
                    NFT
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setRewardFilter("points")}>
                    <div className={`w-4 h-4 mr-2 rounded-full ${rewardFilter === "points" ? "bg-brand-purple" : "border border-gray-300"}`} />
                    Points
                  </DropdownMenuItem>
                  
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={resetFilters}>Reset All Filters</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              
              <div className="flex bg-muted rounded-md p-1">
                <Button 
                  variant={view === "grid" ? "default" : "ghost"} 
                  size="sm" 
                  onClick={() => setView("grid")}
                  className="h-8 w-8 p-0"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <rect x="3" y="3" width="7" height="7" />
                    <rect x="14" y="3" width="7" height="7" />
                    <rect x="3" y="14" width="7" height="7" />
                    <rect x="14" y="14" width="7" height="7" />
                  </svg>
                  <span className="sr-only">Grid view</span>
                </Button>
                <Button 
                  variant={view === "list" ? "default" : "ghost"} 
                  size="sm" 
                  onClick={() => setView("list")}
                  className="h-8 w-8 p-0"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="3" y1="6" x2="21" y2="6" />
                    <line x1="3" y1="12" x2="21" y2="12" />
                    <line x1="3" y1="18" x2="21" y2="18" />
                  </svg>
                  <span className="sr-only">List view</span>
                </Button>
              </div>
            </div>
          </div>
          
          {/* Active filters */}
          {(searchQuery || statusFilter !== "all" || verificationFilter !== "all" || rewardFilter !== "all") && (
            <div className="flex flex-wrap gap-2 mt-3 pt-3 border-t">
              {searchQuery && (
                <Badge variant="outline" className="bg-gray-100 flex items-center gap-1">
                  Search: {searchQuery}
                  <button 
                    className="ml-1 h-3 w-3 rounded-full bg-gray-400 text-white flex items-center justify-center"
                    onClick={() => setSearchQuery("")}
                  >×</button>
                </Badge>
              )}
              
              {statusFilter !== "all" && (
                <Badge variant="outline" className="bg-gray-100 flex items-center gap-1">
                  Status: {statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
                  <button 
                    className="ml-1 h-3 w-3 rounded-full bg-gray-400 text-white flex items-center justify-center"
                    onClick={() => setStatusFilter("all")}
                  >×</button>
                </Badge>
              )}
              
              {verificationFilter !== "all" && (
                <Badge variant="outline" className="bg-gray-100 flex items-center gap-1">
                  Verification: {verificationFilter.charAt(0).toUpperCase() + verificationFilter.slice(1)}
                  <button 
                    className="ml-1 h-3 w-3 rounded-full bg-gray-400 text-white flex items-center justify-center"
                    onClick={() => setVerificationFilter("all")}
                  >×</button>
                </Badge>
              )}
              
              {rewardFilter !== "all" && (
                <Badge variant="outline" className="bg-gray-100 flex items-center gap-1">
                  Reward: {rewardFilter.charAt(0).toUpperCase() + rewardFilter.slice(1)}
                  <button 
                    className="ml-1 h-3 w-3 rounded-full bg-gray-400 text-white flex items-center justify-center"
                    onClick={() => setRewardFilter("all")}
                  >×</button>
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Display total count */}
        <div className="mb-4 text-sm text-muted-foreground">
          Showing {filteredCampaigns.length} of {campaigns.length} campaigns
        </div>

        {filteredCampaigns.length > 0 ? (
          view === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCampaigns.map((campaign) => (
                <CampaignCard
                  key={campaign.id}
                  id={campaign.id}
                  title={campaign.title}
                  description={campaign.description}
                  status={campaign.status}
                  submissions={campaign.submissions}
                  target={campaign.target}
                  daysLeft={campaign.daysLeft}
                  startDate={campaign.startDate}
                  banner_url={campaign.banner_url}
                />
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredCampaigns.map((campaign) => (
                <Link to={`/campaigns/${campaign.id}`} key={campaign.id}>
                  <div className="bg-white p-4 rounded-lg border hover:shadow-md transition-shadow">
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium text-lg">{campaign.title}</h3>
                          <Badge className={
                            campaign.status === 'active' ? "bg-green-100 text-green-800" :
                            campaign.status === 'scheduled' ? "bg-blue-100 text-blue-800" :
                            campaign.status === 'draft' ? "bg-yellow-100 text-yellow-800" :
                            "bg-gray-100 text-gray-800"
                          }>
                            {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground line-clamp-1">{campaign.description}</p>
                        {campaign.product && (
                          <div className="mt-2 flex items-center text-xs text-muted-foreground">
                            <span className="mr-1">Product:</span>
                            <Badge variant="outline" className="bg-gray-50">{campaign.product}</Badge>
                          </div>
                        )}
                      </div>
                      
                      <div className="md:text-right flex flex-col items-start md:items-end">
                        <div className="flex items-center gap-2 text-sm">
                          <User className="h-3.5 w-3.5 text-brand-purple" />
                          <span>{campaign.submissions} / {campaign.target} submissions</span>
                        </div>
                        {campaign.daysLeft && (
                          <div className="text-sm text-muted-foreground mt-1">
                            {campaign.daysLeft} days left
                          </div>
                        )}
                        {campaign.startDate && (
                          <div className="text-sm text-muted-foreground mt-1">
                            Starts {campaign.startDate}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )
        ) : (
          <div className="text-center py-12">
            <div className="h-20 w-20 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
              <Calendar className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium mb-2">No campaigns found</h3>
            <p className="text-muted-foreground mb-6">
              No campaigns match your current filter criteria
            </p>
            <Button onClick={resetFilters} variant="outline">
              Reset Filters
            </Button>
          </div>
        )}
      </div>
    </AppLayout>
  );
}

export default CampaignList;
